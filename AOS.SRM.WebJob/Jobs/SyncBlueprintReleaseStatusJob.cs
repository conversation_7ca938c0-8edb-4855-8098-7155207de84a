using System;
using System.Configuration;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;
using Quartz;

namespace AOS.SRM.WebJob.Jobs
{
    /// <summary>
    /// 同步图纸释放状态定时任务
    /// </summary>
    [DisallowConcurrentExecution]
    public class SyncBlueprintReleaseStatusJob : IJob
    {
        private readonly string apiBaseUrl;

        /// <summary>
        /// 构造函数
        /// </summary>
        public SyncBlueprintReleaseStatusJob()
        {
            // 从配置文件获取API基础URL
            apiBaseUrl = ConfigurationManager.AppSettings["ApiBaseUrl"] ?? "http://localhost:8080";
        }

        /// <summary>
        /// 执行任务
        /// </summary>
        /// <param name="context">任务上下文</param>
        /// <returns>任务结果</returns>
        public async Task Execute(IJobExecutionContext context)
        {
            try
            {
                Debug.WriteLine("开始执行同步图纸释放状态任务...");

                // 调用API同步图纸释放状态
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromMinutes(10); // 设置超时时间为10分钟

                    // 构建API URL
                    string apiUrl = $"{apiBaseUrl}/api/Plm/Plm_Blueprint/SyncAllReleaseStatus";

                    // 发送POST请求
                    var response = await client.PostAsync(apiUrl, new StringContent(""));

                    // 检查响应状态
                    if (response.IsSuccessStatusCode)
                    {
                        string result = await response.Content.ReadAsStringAsync();
                        Debug.WriteLine($"同步图纸释放状态任务执行成功: {result}");
                    }
                    else
                    {
                        Debug.WriteLine($"同步图纸释放状态任务执行失败: {response.StatusCode} - {response.ReasonPhrase}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"同步图纸释放状态任务执行异常: {ex.Message}");
                throw;
            }
        }
    }
}
