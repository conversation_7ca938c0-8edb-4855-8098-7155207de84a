using System;
using System.Linq;
using System.Threading.Tasks;
using System.Diagnostics;
using Quartz;
using Quartz.Impl;
using AOS.SRM.WebJob.Jobs;

namespace AOS.SRM.WebJob
{
    /// <summary>
    /// 任务调度器管理类
    /// </summary>
    public class JobScheduler
    {
        private static IScheduler scheduler;

        /// <summary>
        /// 启动调度器
        /// </summary>
        /// <returns></returns>
        public static async Task Start()
        {
            try
            {
                Debug.WriteLine("正在启动任务调度器...");

                // 创建调度器工厂
                StdSchedulerFactory factory = new StdSchedulerFactory();
                scheduler = await factory.GetScheduler();

                // 启动调度器
                await scheduler.Start();

                // 添加同步图纸释放状态任务
                await AddSyncBlueprintReleaseStatusJob();

                Debug.WriteLine("任务调度器启动成功");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"任务调度器启动失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 停止调度器
        /// </summary>
        /// <returns></returns>
        public static async Task Stop()
        {
            try
            {
                if (scheduler != null && !scheduler.IsShutdown)
                {
                    Debug.WriteLine("正在停止任务调度器...");
                    await scheduler.Shutdown();
                    Debug.WriteLine("任务调度器已停止");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"停止任务调度器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加同步图纸释放状态任务
        /// </summary>
        /// <returns></returns>
        private static async Task AddSyncBlueprintReleaseStatusJob()
        {
            try
            {
                // 创建任务
                IJobDetail job = JobBuilder.Create<SyncBlueprintReleaseStatusJob>()
                    .WithIdentity("SyncBlueprintReleaseStatusJob", "BlueprintGroup")
                    .WithDescription("同步图纸释放状态任务")
                    .Build();

                // 创建触发器 - 每小时执行一次
                ITrigger trigger = TriggerBuilder.Create()
                    .WithIdentity("SyncBlueprintReleaseStatusTrigger", "BlueprintGroup")
                    .WithDescription("每小时执行一次同步图纸释放状态")
                    .StartNow()
                    .WithSimpleSchedule(x => x
                        .WithIntervalInHours(1) // 每小时执行一次
                        .RepeatForever())
                    .Build();

                // 调度任务
                await scheduler.ScheduleJob(job, trigger);

                Debug.WriteLine("同步图纸释放状态任务已添加到调度器，每小时执行一次");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"添加同步图纸释放状态任务失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 手动触发同步图纸释放状态任务
        /// </summary>
        /// <returns></returns>
        public static async Task TriggerSyncBlueprintReleaseStatusJob()
        {
            try
            {
                if (scheduler != null && !scheduler.IsShutdown)
                {
                    await scheduler.TriggerJob(new JobKey("SyncBlueprintReleaseStatusJob", "BlueprintGroup"));
                    Debug.WriteLine("手动触发同步图纸释放状态任务成功");
                }
                else
                {
                    Debug.WriteLine("调度器未启动，无法手动触发任务");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"手动触发同步图纸释放状态任务失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取任务状态
        /// </summary>
        /// <returns></returns>
        public static async Task<string> GetJobStatus()
        {
            try
            {
                if (scheduler != null && !scheduler.IsShutdown)
                {
                    var jobKey = new JobKey("SyncBlueprintReleaseStatusJob", "BlueprintGroup");
                    var triggers = await scheduler.GetTriggersOfJob(jobKey);

                    if (triggers.Count > 0)
                    {
                        var trigger = triggers.First(); // 使用First()方法而不是索引
                        var state = await scheduler.GetTriggerState(trigger.Key);
                        var nextFireTime = trigger.GetNextFireTimeUtc();
                        var prevFireTime = trigger.GetPreviousFireTimeUtc();

                        return $"任务状态: {state}, 下次执行时间: {nextFireTime?.ToLocalTime()}, 上次执行时间: {prevFireTime?.ToLocalTime()}";
                    }
                    else
                    {
                        return "任务未找到";
                    }
                }
                else
                {
                    return "调度器未启动";
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取任务状态失败: {ex.Message}");
                return $"获取任务状态失败: {ex.Message}";
            }
        }
    }
}
