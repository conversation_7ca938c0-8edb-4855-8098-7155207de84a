<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{62D9F685-5537-494E-8D51-4DAF877F8AF1}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AOS.Core</RootNamespace>
    <AssemblyName>AOS.Core</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\AOS.Core.xml</DocumentationFile>
    <NoWarn>1591;1573;1570;</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto, Version=*******, Culture=neutral, PublicKeyToken=0e99375e54769942">
      <HintPath>..\packages\BouncyCastle.1.8.5\lib\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=1.0.0.999, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.0.0\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=*******, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="MailKit, Version=*******, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b, processorArchitecture=MSIL">
      <HintPath>..\packages\MailKit.2.2.0\lib\net45\MailKit.dll</HintPath>
    </Reference>
    <Reference Include="MimeKit, Version=*******, Culture=neutral, PublicKeyToken=bede1c8a46c66814, processorArchitecture=MSIL">
      <HintPath>..\packages\MimeKit.2.2.0\lib\net45\MimeKit.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.4.1\lib\net45\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Config\AppConfigHelper.cs" />
    <Compile Include="Email\Email.cs" />
    <Compile Include="Extensions\BaseTypeExt.cs" />
    <Compile Include="Extensions\DateTimeExt.cs" />
    <Compile Include="Extensions\StringExt.cs" />
    <Compile Include="Helper\JsonHelper.cs" />
    <Compile Include="Helper\StringHelper.cs" />
    <Compile Include="Logging\Template\BaseLogTemplate.cs" />
    <Compile Include="Logging\log4netEx\Log4NetExtension.cs" />
    <Compile Include="Helper\LogHelper.cs" />
    <Compile Include="Logging\Template\WMSLogTemplate.cs" />
    <Compile Include="Log\LogUtil.cs" />
    <Compile Include="Office\ExcelCell.cs" />
    <Compile Include="Office\ExcelCellDataFormat.cs" />
    <Compile Include="Office\ExcelColumn.cs" />
    <Compile Include="Office\ExcelExportAttribute.cs" />
    <Compile Include="Office\ExcelFileType.cs" />
    <Compile Include="Office\ExcelService.cs" />
    <Compile Include="Office\ExcelSheet.cs" />
    <Compile Include="Office\ExcelUtil.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Security\Base64.cs" />
    <Compile Include="Security\DES.cs" />
    <Compile Include="Security\MD5.cs" />
    <Compile Include="Security\RSA.cs" />
    <Compile Include="Security\Token.cs" />
    <Compile Include="Utilities\AttributeUtil.cs" />
    <Compile Include="Utilities\LicenseManager.cs" />
    <Compile Include="Utilities\PropertyAccess.cs" />
    <Compile Include="Utilities\PropertyUtil.cs" />
    <Compile Include="Vue\Route.cs" />
    <Compile Include="Vue\RouteMeta.cs" />
    <Compile Include="Http\Pagination.cs" />
    <Compile Include="Http\ResponseData.cs" />
    <Compile Include="Http\ResponsePageData.cs" />
    <Compile Include="Http\ResponseStatus.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Logging\Config\log4net.config.example" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>