namespace AOS.Core.Http
{
    public enum WMSStatusCode
    {
        /// <summary>
        /// 发生未处理异常
        /// </summary>
        UnHandledException = 1001,
        /// <summary>
        /// 请求处理成功
        /// </summary>
        Success = 2000,
        /// <summary>
        /// 处理失败
        /// </summary>
        Failed = 2010,
        /// <summary>
        /// 参数错误
        /// </summary>
        ParameterError = 2011,
        /// <summary>
        /// 数据不存在
        /// </summary>
        DataNotFound = 2012,
        /// <summary>
        /// 数据已存在
        /// </summary>
        DataExisted = 2013,
        /// <summary>
        /// 操作失败
        /// </summary>
        OperationFailed = 2014,
    }
}
