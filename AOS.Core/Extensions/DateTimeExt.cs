using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.Core.Extensions
{
    /// <summary>
    /// DateTime类型扩展
    /// </summary>
    public static class DateTimeExt
    {
        /// <summary>
        /// 获取时间戳
        /// </summary>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static int GetTimestamp(this DateTime dt)
        {
            var timeSpan = dt - new DateTime(1970, 1, 1, 0, 0, 0);
            return (int)timeSpan.TotalSeconds;
        }

        /// <summary>
        /// 时间戳转换为时间
        /// </summary>
        /// <param name="timestamp">时间戳</param>
        /// <returns></returns>
        public static DateTime ConvertToDateTime(this int timestamp)
        {
            return new DateTime(1970, 1, 1, 8, 0, 0).AddSeconds(timestamp);//  第四个参数可认为是时区，中国在东8区
        }

        /// <summary>
        /// 位于指定时间区间内
        /// </summary>
        /// <param name="dtm"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public static bool IsBetween(this DateTime dtm,DateTime startTime,DateTime endTime)
        {
            if (dtm == null) return false;
            return dtm >= startTime && dtm <= endTime;
        }

    }
}
