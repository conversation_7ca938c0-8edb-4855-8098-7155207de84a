using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.Core.Extensions
{
    public static class BaseTypeExt
    {
        /// <summary>
        /// 判断类型是否为空
        /// </summary>
        /// <param name="theType"></param>
        /// <returns></returns>
        public static bool IsNullableType(this Type theType)
        {
            return (theType.IsGenericType && theType.
              GetGenericTypeDefinition().Equals
              (typeof(Nullable<>)));
        }

        /// <summary>
        /// 获取类型
        /// </summary>
        /// <param name="theType"></param>
        /// <returns></returns>
        public static Type GetRealType(this Type theType)
        {
            if (theType.IsNullableType())
            {
                return Nullable.GetUnderlyingType(theType);
            }
            return theType;
        }

    }
}
