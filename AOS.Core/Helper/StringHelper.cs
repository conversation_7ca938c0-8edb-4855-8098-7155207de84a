using AOS.Core.Logging.log4netEx;
using log4net;
using log4net.Core;
using System;

namespace AOS.Core
{
    /// <summary>
    /// String帮助类
    /// </summary>
    public class StringHelper
    {
        #region 全角、半角字符转换

        /// <summary>
        /// 转全角字符串
        /// 全角空格为12288，半角空格为32
        /// 其他字符半角(33-126)与全角(65281-65374)的对应关系是：均相差65248
        /// </summary>
        /// <param name="input">任意字符串</param>
        /// <returns>全角字符串</returns>
        public static String ToSBC(String input)
        {
            // 半角转全角：
            char[] c = input.ToCharArray();
            for (int i = 0; i < c.Length; i++)
            {
                if (c[i] == 32)
                {
                    c[i] = (char)12288;
                    continue;
                }
                if (c[i] < 127)
                    c[i] = (char)(c[i] + 65248);
            }
            return new String(c);
        }

        /// <summary>
        /// 转半角字符串
        /// 全角空格为12288，半角空格为32
        /// 其他字符半角(33-126)与全角(65281-65374)的对应关系是：均相差65248
        /// </summary>
        /// <param name="input">任意字符串</param>
        /// <returns>半角字符串</returns>
        public static String ToDBC(String input)
        {
            char[] c = input.ToCharArray();
            for (int i = 0; i < c.Length; i++)
            {
                if (c[i] == 12288)
                {
                    c[i] = (char)32;
                    continue;
                }
                if (c[i] > 65280 && c[i] < 65375)
                    c[i] = (char)(c[i] - 65248);
            }
            return new String(c);
        }

        #endregion

        #region 去空格

        /// <summary>
        /// 去空格
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static String ToTrim(String input)
        {
            var c = !string.IsNullOrEmpty(input) ? input.Trim() : "";
            return (c);
        }

        /// <summary>
        /// 去空格并且转换为大写
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public static String ToTrimAndToUpper(String input)
        {
            var c = !string.IsNullOrEmpty(input) ? input.ToUpper().Trim() : "";
            return (c);
        }

        #endregion
    }
}
