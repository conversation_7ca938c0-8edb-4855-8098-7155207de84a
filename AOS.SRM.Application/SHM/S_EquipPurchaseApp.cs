
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 
    /// </summary>
    public class S_EquipPurchaseApp : BaseApp<S_EquipPurchase>
    {
        Sys_UserApp _userApp = new Sys_UserApp();
        Sys_MailApp _mailApp = new Sys_MailApp();
        MD_AttachmentManagementApp _attachApp = new MD_AttachmentManagementApp();

        #region 新增
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="fileIds"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool AddEntity(S_EquipPurchase entity, List<string> fileIds, Sys_User _currentUser, out string msg)
        {
            var flag = true;

            try
            {
                msg = string.Empty;
                var attachList = new List<MD_AttachmentManagement>();

                entity.Id = Guid.NewGuid().ToString();
                entity.CUser = _currentUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.IsDelete = false;

                if (fileIds.Count > 0)
                {
                    attachList = _attachApp.GetList(t => fileIds.Contains(t.FileId)).ToList();
                    attachList.ForEach(t =>
                    {
                        t.ReferenceDocNumber = entity.Id;
                        t.MUser = _currentUser.LoginAccount;
                        t.MTime = DateTime.Now;
                    });
                }

                this.DbContext.Ado.BeginTran();

                this.Insert(entity);
                if (fileIds.Count > 0) { _attachApp.Update(attachList); }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 修改
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="fileIds"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool UpdateEntity(S_EquipPurchase entity, List<string> fileIds, Sys_User _currentUser, out string msg)
        {
            var flag = true;

            try
            {
                msg = string.Empty;
                var attachList = new List<MD_AttachmentManagement>();

                entity.MUser = _currentUser.LoginAccount;
                entity.MTime = DateTime.Now;

                if (fileIds.Count > 0)
                {
                    attachList = _attachApp.GetList(t => fileIds.Contains(t.FileId)).ToList();
                    attachList.ForEach(t =>
                    {
                        t.ReferenceDocNumber = entity.Id;
                        t.MUser = _currentUser.LoginAccount;
                        t.MTime = DateTime.Now;
                    });
                }

                this.DbContext.Ado.BeginTran();

                this.Update(entity);
                if (fileIds.Count > 0) { _attachApp.Update(attachList); }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 指派人
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="userCode"></param>
        /// <param name="_currentUser"></param>
        /// <returns></returns>
        public bool AssignUser(List<string> ids, string userCode, Sys_User _currentUser)
        {
            var flag = true;
            try
            {
                #region 邮件信息
                var entityUser = _userApp.GetFirstEntity(t => t.LoginAccount == userCode);

                var entitys = this.GetListByKeys(ids.ToArray());
                entitys.ForEach(entity =>
                {
                    entity.AssignUserCode = userCode;
                    entity.AssignUserName = entityUser.UserName;
                    entity.MUser = _currentUser.LoginAccount;
                    entity.MTime = DateTime.Now;
                });
                var enpo = string.Join(",", entitys.Select(t => t.EPNO).Distinct());

                Sys_Mail mail = new Sys_Mail();
                mail.MailID = Guid.NewGuid().ToString();
                mail.UserID = userCode;
                mail.UserName = entityUser.UserName;
                mail.MessageTypeID = "1001";
                mail.MailSubject = "设备采购跟踪单据处理";
                mail.MailBody = "您好，您有要处理的设备采购跟踪单据，立项单号：" + enpo + "，请前往西子SRM系统确认！";
                mail.ReceiverMail = entityUser.Email;
                mail.SendTime = DateTime.Now;
                mail.SenderDisplayName = "System";
                mail.IsDelete = false;
                mail.CUser = "System";
                mail.CTime = DateTime.Now;
                mail.AutoSend = false;
                #endregion

                this.DbContext.Ado.BeginTran();

                this.Update(entitys);
                _mailApp.Insert(mail);

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="fileIds"></param>
        /// <param name="_currentUser"></param>
        /// <returns></returns>
        public bool UpdateAttachInfo(string id, List<string> fileIds, Sys_User _currentUser)
        {
            var attachList = _attachApp.GetList(t => fileIds.Contains(t.FileId))?.ToList();
            attachList.ForEach(t =>
            {
                t.ReferenceDocNumber = id;
                t.MUser = _currentUser.LoginAccount;
                t.MTime = DateTime.Now;
            });
            _attachApp.Update(attachList);

            return true;
        }
        #endregion
    }
}
