using AOS.SRM.Entity.SHM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 供应商审批应用层
    /// </summary>
    public class SupplierAuditApp : BaseApp<S_SupplierAudit>
    {
        /// <summary>
        /// 获取审批最大序列号
        /// </summary>
        /// <param name="supplierId"></param>
        /// <returns></returns>
        public int GetMaxSequence(string supplierId)
        {
            var maxSequence = 0;
            var lstEntity = this.GetList(t => t.DocId == supplierId).ToList();
            if (lstEntity != null && lstEntity.Count > 0)
            {
                maxSequence = lstEntity.Max(t => t.AuditSequence).Value;
            }
            return maxSequence;
        }
    }
}
