using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 
    /// </summary>
    public class S_TechnicalNoticeApp : BaseApp<S_TechnicalNotice>
    {
        MD_AttachmentManagementApp _attachApp = new MD_AttachmentManagementApp();

        #region 新增
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="fileIds"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool AddEntity(S_TechnicalNotice entity, List<string> fileIds, Sys_User _currentUser, out string msg)
        {
            var flag = true;

            try
            {
                msg = string.Empty;
                var attachList = new List<MD_AttachmentManagement>();

                entity.Id = Guid.NewGuid().ToString();
                entity.CUser = _currentUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.IsDelete = false;
                entity.Status = "已提交";

                if (fileIds.Count > 0)
                {
                    attachList = _attachApp.GetList(t => fileIds.Contains(t.FileId)).ToList();
                    attachList.ForEach(t =>
                    {
                        t.ReferenceDocNumber = entity.Id;
                        t.MUser = _currentUser.LoginAccount;
                        t.MTime = DateTime.Now;
                    });
                }

                this.DbContext.Ado.BeginTran();

                this.Insert(entity);
                if (fileIds.Count > 0) { _attachApp.Update(attachList); }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 修改
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="fileIds"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool UpdateEntity(S_TechnicalNotice entity, List<string> fileIds, Sys_User _currentUser, out string msg)
        {
            var flag = true;

            try
            {
                msg = string.Empty;
                var attachList = new List<MD_AttachmentManagement>();

                entity.MUser = _currentUser.LoginAccount;
                entity.MTime = DateTime.Now;

                if (fileIds.Count > 0)
                {
                    attachList = _attachApp.GetList(t => fileIds.Contains(t.FileId)).ToList();
                    attachList.ForEach(t =>
                    {
                        t.ReferenceDocNumber = entity.Id;
                        t.MUser = _currentUser.LoginAccount;
                        t.MTime = DateTime.Now;
                    });
                    //删除原附件
                    var old = _attachApp.GetList(t => t.ReferenceDocNumber == entity.Id)?.ToList();
                    if (old != null && old.Count > 0)
                    {
                        old.ForEach(t =>
                        {
                            t.IsDelete = true;
                            t.DUser = _currentUser.LoginAccount;
                            t.DTime = DateTime.Now;
                        });
                        attachList.AddRange(old);
                    }
                }

                this.DbContext.Ado.BeginTran();

                this.Update(entity);
                if (fileIds.Count > 0) { _attachApp.Update(attachList); }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion
    }
}
