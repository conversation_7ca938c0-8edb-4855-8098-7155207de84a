using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SAP;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using HZ.WMS.Application.Sys;
using Newtonsoft.Json;
using SqlSugar;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 供应商方法层
    /// </summary>
    public class SupplierInfoApp : BaseApp<S_SupplierInfo>
    {
        Sys_SAPCompanyInfoApp _SAPCompanyInfoApp = new Sys_SAPCompanyInfoApp();
        SupplierAuditApp _auditApp = new SupplierAuditApp();
        Sys_DictionaryApp _dicApp = new Sys_DictionaryApp();
        Sys_MailApp _mailApp = new Sys_MailApp();
        Sys_UserApp _userApp = new Sys_UserApp();


        /// <summary>
        /// 查询供应商
        /// </summary>
        /// <param name="user"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<S_SupplierInfo> GetSupplierInfo(Sys_User user, string keyword)
        {
            if (user.IsSupplier == true)
            {
                var str = DbContext.Queryable<S_SupplierInfo>().Where(t => t.SupplyerCode == user.SupplyCode).Where
                    (t => string.IsNullOrEmpty(keyword) || t.SupplyerCode.Contains(keyword) || t.SupplyerName1.Contains(keyword))
                .ToList();
                return str;
            }
            else
            {
                return DbContext.Queryable<S_SupplierInfo>().Where(t => t.Status == "1").Where
                    (t => string.IsNullOrEmpty(keyword) || t.SupplyerCode.Contains(keyword) || t.SupplyerName1.Contains(keyword)).ToList();
            }
        }

        /// <summary>
        /// 同步供应商主数据到SAP
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool DoPost(List<S_SupplierInfo> entities, out string error_message)
        {
            error_message = "";

            foreach (var item in entities)
            {
                ZFGSRM001 entity = new ZFGSRM001();

                entity.LIFNR = item.SupplyerCode;
                entity.NAME1 = (item.SupplyerName1.Length > 0 ? item.SupplyerName1.Substring(0, item.SupplyerName1.Length) : item.SupplyerName1);
                entity.NAME2 = (item.SupplyerName1.Length > 40 ? item.SupplyerName1.Substring(40) : "");
                entity.SORTL = item.SupplyerName;
                entity.KTOKK = item.AccountGroupCode;
                entity.ANRED = "公司";
                entity.LAND1 = item.CountryCode;
                entity.ORT01 = item.City;
                entity.STRAS = item.Address;
                entity.ORT02 = "";
                entity.SPRAS = item.SupplierLanguage;
                entity.PSTLZ = item.PostalCode;
                entity.TELF1 = item.Tel;
                entity.TELF2 = item.Phone;
                entity.TELFX = item.Fax;
                entity.TELBX = item.EMail;
                entity.STENR = item.TaxNumber;
                entity.KOINH = item.LegalUser;
                entity.BANKA = item.BankDeposit;
                entity.BANKS = item.BankCountryCode;//不能为空？
                entity.BANKN = item.BankAccount;
                entity.NODEL = (item.Status != "1" ? "X" : "");//“X”表示冻结或者删除
                entity.EKORG = item.POrganizationCode;
                entity.WAERS = item.CurrencyCode;
                entity.ZTERM = item.PaymentTermCode;
                entity.BUKRS = "2002";
                entity.AKONT = item.ControlSubject;
                entity.WEBRE = item.InvoiceVerification;
                entity.REMARK = item.Remark;

                var result = _SAPCompanyInfoApp.ZFGSRM001("001", entity, out error_message);
                if (!result)
                {
                    return false;
                }
            }

            return true;
        }


        #region 校验是否已经存在相同供应商名称
        /// <summary>
        ///
        /// </summary>
        /// <param name="supplierName"></param>
        /// <param name="supplierCode"></param>
        /// <returns></returns>
        public bool CheckIsSameSupplier(string supplierName, string supplierCode)
        {
            if (string.IsNullOrEmpty(supplierName))
            {
                return false;
            }
            return base.Any(t => t.SupplyerName1 == supplierName);
        }
        #endregion

        #region 供应商信息注册（内部/外部）
        /// <summary>
        ///
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="fileIds"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool CreateSupplierInfo(S_SupplierInfo entity, string[] fileIds, string userId, string userName)
        {
            //string loginAccount = new Sys_UserApp().CreateSupplierLoginAccount(entity.SupplierCategory);
            //user = entity.InOrOut == "外部" ? loginAccount : user;
            entity.CompanyCode = "2002";
            entity.CUser = userId;
            //entity.CUser = DateTime.Now.ToString("yyyyMMddHHmmss");
            entity.CTime = DateTime.Now;
            //entity.LoginAccount = loginAccount;
            entity.Status = "0";
            entity.AccessStatus = "0";
            entity.NextCheckUserCode = entity.BuyerCode;//默认下一个审核人是采购员
            entity.NextCheckUserName = entity.BuyerName;//默认下一个审核人是采购员
            entity.AuditSequence = 0;
            var insertInfo = this.Insert(entity);
            if (insertInfo != null)
            {
                var fileInfos = new MD_AttachmentManagementApp().GetList(x => fileIds.Contains(x.FileId))?.ToList();
                if (fileInfos.Count() > 0)
                {
                    fileInfos.ForEach(file =>
                    {
                        file.ReferenceDocNumber = insertInfo.Id;
                        file.MUser = userName;
                        file.MTime = DateTime.Now;
                    });
                    new MD_AttachmentManagementApp().Update(fileInfos);
                }
            }
            return true;
        }
        #endregion

        #region 供应商外部注册查询（必须输入供应商公司名称，准确查询）
        /// <summary>
        ///
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public List<S_SupplierInfo> GetOutInfoByName(string name)
        {
            return base.GetList(x => x.SupplyerName1 == name)?.ToList();
        }
        #endregion

        #region 供应商准入查询(分页)
        /// <summary>
        ///
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="supplierName"></param>
        /// <param name="supplierType"></param>
        /// <param name="dateTimes"></param>
        /// <param name="inOrOut"></param>
        /// <param name="states"></param>
        /// <param name="buyerCode">所属采购员</param>
        /// <param name="userInfo"></param>
        /// <returns></returns>
        public List<S_SupplierInfo> GetPageListForAccess(Pagination page, string keyword, string supplierName, string supplierType, DateTime[] dateTimes, string inOrOut, string states, string buyerCode, Sys_User userInfo)
        {
            var itemsData = GetDetailList(keyword);
            dateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            var fromDate = dateTimes[0];
            var toDate = dateTimes[1];
            itemsData = itemsData.Where(x => x.CTime >= fromDate && x.CTime <= toDate
            && (string.IsNullOrEmpty(supplierName) || x.SupplyerName1.Contains(supplierName))
            && (string.IsNullOrEmpty(supplierType) || x.SupplierCategory == supplierType)
            && (string.IsNullOrEmpty(inOrOut) || x.InOrOut == inOrOut)
            && (string.IsNullOrEmpty(states) || x.Status == states)
            && (string.IsNullOrEmpty(buyerCode) || x.BuyerCode == buyerCode)
            );

            //return base.GetPageListBySelf(page, itemsData?.ToList());
            List<S_SupplierInfo> listNew = new List<S_SupplierInfo>();

            listNew = itemsData.ToList();

            return listNew;
        }
        #region 获取明细
        /// <summary>
        /// 获取未删除明细
        ///  用于分页和导出的明细
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        public ISugarQueryable<S_SupplierInfo> GetDetailList(string keyword, string sort = "CTime desc")
        {
            var query = this.GetList(x =>
            (string.IsNullOrEmpty(keyword)
            || x.SupplyerCode.Contains(keyword)
            || x.SupplyerName1.Contains(keyword)
            || x.SupplyerName.Contains(keyword)
            || x.AccountGroupDisc.Contains(keyword)
            || x.Address.Contains(keyword)
            || x.CUser.Contains(keyword)
            || x.MUser.Contains(keyword)
            || x.Phone.Contains(keyword)
            || x.Remark.Contains(keyword)
            )
            , sort); // Pass the sort parameter to GetList instead of applying OrderBy again
            return query;
        }
        #endregion
        #endregion

        #region 供应商准入审核(同意)
        /// <summary>
        ///
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="user"></param>
        /// <param name="msg"></param>
        /// <param name="allowInList"></param>
        /// <returns></returns>
        public bool SupplierApproved(string[] ids, Sys_User user, out string msg, out List<S_SupplierInfo> allowInList)
        {
            msg = string.Empty;
            allowInList = new List<S_SupplierInfo>();
            var auditList = new List<S_SupplierAudit>();
            Sys_Mail mail = new Sys_Mail();
            List<Sys_Mail> mailList = new List<Sys_Mail>();

            try
            {
                var entitys = this.GetList(x => ids.Contains(x.Id))?.ToList();
                foreach (var entity in entitys)
                {
                    //判断是否当前审批人
                    if (user.LoginAccount != entity.NextCheckUserCode)
                    {
                        msg = string.Format("当前审批人是：{1}，供应商：{0}", entity.SupplyerName1, entity.NextCheckUserName);
                        return false;
                    }

                    //插入审批记录
                    var audit = new S_SupplierAudit()
                    {
                        Id = Guid.NewGuid().ToString(),
                        DocId = entity.Id,
                        SupplierName = entity.SupplyerName1,
                        AuditSequence = _auditApp.GetMaxSequence(entity.Id) + 1,
                        AuditStatus = "1",
                        AuditUser = user.UserName,
                        AuditTime = DateTime.Now,
                        CTime = DateTime.Now,
                        CUser = user.LoginAccount
                    };
                    auditList.Add(audit);

                    //获取当前审批人的节点序号
                    var nextSeq = (entity.AuditSequence == null ? 1 : entity.AuditSequence) + 1;
                    var nextDicEntity = _dicApp.GetFirstEntity(t => t.TypeCode == "SupplierAudit" && t.EnumKey == nextSeq);
                    if (nextDicEntity != null)
                    {
                        entity.AuditSequence = nextSeq;
                        entity.NextCheckUserCode = nextDicEntity.EnumValue;
                        entity.NextCheckUserName = nextDicEntity.EnumValue1;
                        entity.AccessStatus = "2";
                        entity.MUser = user.LoginAccount;
                        entity.MTime = DateTime.Now;

                        //获取下一个审批人的邮箱
                        var nextUser = _userApp.GetFirstEntity(t => t.LoginAccount == entity.NextCheckUserCode);

                        #region 邮件通知下一个审批人
                        mail = new Sys_Mail()
                        {
                            MailID = Guid.NewGuid().ToString(),
                            UserID = entity.NextCheckUserCode,
                            UserName = entity.NextCheckUserName,
                            MessageTypeID = "1001",
                            MailSubject = "供应商准入审批",
                            MailBody = "您好，待准入供应商[" + entity.SupplyerName1 + "]需要您审批，请前往西子SRM系统的供应商准入模块进行审批",
                            ReceiverMail = nextUser.Email,
                            IsDelete = false,
                            CUser = "admin",
                            CTime = DateTime.Now,
                            SenderDisplayName = "System",
                            AutoSend = false
                        };
                        mailList.Add(mail);
                        #endregion
                    }
                    else
                    {
                        entity.NextCheckUserCode = "";
                        entity.NextCheckUserName = "";
                        entity.AuditSequence = null;//审批结束，
                        entity.AccessStatus = "3";
                        entity.Status = "1";
                        entity.MUser = user.LoginAccount;
                        entity.MTime = DateTime.Now;

                        #region 输出已准入供应商
                        //输出已准入供应商
                        allowInList.Add(entity);
                        #endregion

                        #region 创建用户、分配默认权限

                        //创建用户账号
                        Sys_User userInfo = new Sys_User();
                        userInfo.UserID = Guid.NewGuid().ToString();
                        userInfo.UserName = entity.SupplyerName;
                        userInfo.LoginAccount = entity.SupplyerCode;
                        userInfo.IsEnable = true;
                        user.IsDelete = false;
                        userInfo.IsSupplier = true;
                        userInfo.SupplyCode = entity.SupplyerCode;
                        userInfo.SupplyName = entity.SupplyerName;
                        userInfo.Email = entity.EMail;
                        userInfo.Telphone = entity.Phone;
                        string error_msg = "";
                        var isHaveLogin = _userApp.GetFirstEntity(x => x.LoginAccount == userInfo.LoginAccount);
                        Sys_User insertUser = new Sys_User();
                        if (isHaveLogin == null)
                        {
                            insertUser = _userApp.Insert(userInfo, out error_msg);
                            if (insertUser == null)
                            {
                                throw new Exception("创建用户登录账户失败！" + error_msg);
                            }
                            //分配权限
                            var roleId = new Sys_RoleApp().GetFirstEntity(x => x.RoleDesc.Contains("物料采购供应商")).RoleID;
                            new Sys_UserRoleApp().ResetUserRoles(insertUser.UserID, roleId, "自动生成");
                        }
                        #endregion

                    }
                    entity.MUser = user.LoginAccount;
                    entity.MTime = DateTime.Now;
                }

                DbContext.Ado.BeginTran();//开始事务
                _auditApp.DbContext = this.DbContext;
                _mailApp.DbContext = this.DbContext;

                base.Update(entitys);
                _auditApp.Insert(auditList);
                _mailApp.Insert(mailList);

                DbContext.Ado.CommitTran();//提交事务
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    DbContext.RollbackTran();//回滚事务
                }
                msg = ex.Message;
                return false;
            }
            return true;

        }
        #endregion

        #region 供应商准入驳回
        /// <summary>
        /// 供应商准入驳回
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="rejectReason"></param>
        /// <param name="user"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool SupplierReject(string[] ids, string rejectReason, Sys_User user, out string msg)
        {
            msg = string.Empty;
            List<S_SupplierAudit> auditList = new List<S_SupplierAudit>();

            try
            {
                var entitys = this.GetList(x => ids.Contains(x.Id))?.ToList();
                entitys.ForEach(entity =>
                {
                    entity.AccessStatus = "1";
                    entity.NextCheckUserCode = "";
                    entity.NextCheckUserName = "";
                    entity.AuditSequence = null;
                    entity.MUser = user.LoginAccount;
                    entity.MTime = DateTime.Now;

                    //插入审批记录
                    var audit = new S_SupplierAudit()
                    {
                        Id = Guid.NewGuid().ToString(),
                        DocId = entity.Id,
                        SupplierName = entity.SupplyerName1,
                        AuditSequence = _auditApp.GetMaxSequence(entity.Id) + 1,
                        AuditStatus = "2",
                        AuditOpinions = rejectReason,
                        AuditUser = user.UserName,
                        AuditTime = DateTime.Now,
                        CTime = DateTime.Now,
                        CUser = user.LoginAccount,
                        IsDelete = false
                    };
                    auditList.Add(audit);
                });

                DbContext.Ado.BeginTran();//开始事务

                _auditApp.DbContext = this.DbContext;

                base.Update(entitys);
                _auditApp.Insert(auditList);

                DbContext.Ado.CommitTran();//提交事务
            }
            catch (Exception ex)
            {
                DbContext.RollbackTran();//回滚事务
                msg = ex.Message;
                return false;
            }
            return true;
        }

        #endregion

        #region 供应商准入导出
        /// <summary>
        ///
        /// </summary>
        /// <param name="SupplyerName"></param>
        /// <param name="SupplierCategory"></param>
        /// <param name="InOrOut"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<S_SupplierInfo> AdmittanceExportData(string SupplyerName, string SupplierCategory, string InOrOut, string Status, DateTime StartTime, DateTime EndTime)
        {
            var itemsData = this.DbContext.Queryable<S_SupplierInfo>()
                .Where(t => (string.IsNullOrEmpty(SupplyerName) || t.SupplyerName.Contains(SupplyerName))
                && (string.IsNullOrEmpty(SupplierCategory) || t.SupplierCategory.Contains(SupplierCategory))
                && (t.DateEstablishment >= StartTime) && (t.DateEstablishment <= EndTime.AddDays(1))
                && (string.IsNullOrEmpty(InOrOut) || t.SupplyerName.Contains(InOrOut))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))).ToList();
            return itemsData;
        }
        #endregion


        #region 供应商主数据查询(分页)
        /// <summary>
        ///
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="supplierName"></param>
        /// <param name="supplierType"></param>
        /// <param name="dateTimes"></param>
        /// <param name="inOrOut"></param>
        /// <param name="states"></param>
        /// <param name="userInfo"></param>
        /// <returns></returns>
        public List<S_SupplierInfo> GetPageListForMaintain(Pagination page, string keyword, string supplierName, string supplierType, DateTime[] dateTimes, string inOrOut, string states, Sys_User userInfo)
        {
            var itemsData = GetDetailList(keyword);
            var buyerSuppliers = GetSupplierInfo(userInfo, "").Select(x => x.SupplyerCode).Distinct().ToArray();
            dateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            var fromDate = dateTimes[0];
            var toDate = dateTimes[1];
            itemsData = itemsData.Where(x =>
             //x.Status == "1"&&//信息维护必须是准入的才能看见
             x.CTime >= fromDate && x.CTime <= toDate
            && (string.IsNullOrEmpty(supplierName) || x.SupplyerName1 == supplierName)
            && (string.IsNullOrEmpty(supplierType) || x.SupplierCategory == supplierType)
            && (string.IsNullOrEmpty(inOrOut) || x.InOrOut == inOrOut)
            && (string.IsNullOrEmpty(states) || x.Status == states)
            && (buyerSuppliers.Count() == 0 || (buyerSuppliers.Contains(x.SupplyerCode) || string.IsNullOrEmpty(x.SupplyerCode)))
            //&& (string.IsNullOrEmpty(buyerCode) || x.BuyerCode == buyerCode)
            ).OrderBy(x => x.SupplyerCode);

            //return base.GetPageListBySelf(page, itemsData?.ToList());
            List<S_SupplierInfo> listNew = new List<S_SupplierInfo>();
            return listNew;
        }
        #endregion

        #region 供应商主数据更新
        /// <summary>
        /// 需要生成两条数据，通过删除旧数据，驳回删除新数据
        /// 附件也是
        /// </summary>
        /// <returns></returns>
        public bool SupplierInfoUpdate(S_SupplierInfo entity, string[] filesId, string user, out string errMsg)
        {
            var flag = true;
            errMsg = "";
            if (string.IsNullOrEmpty(entity.SupplyerCode))//还未变成正式供应商时的更新
            {
                this.Update(entity);
            }
            else
            {
                var oldInfo = this.GetFirstEntity(x => x.Id == entity.Id);
                entity.MUser = user;
                entity.MTime = DateTime.Now;
                entity.AccessStatus = oldInfo.AccessStatus;
                entity.Status = oldInfo.Status;
                entity.NextCheckUserCode = oldInfo.NextCheckUserCode;
                entity.NextCheckUserName = oldInfo.NextCheckUserName;
                entity.AuditSequence = oldInfo.AuditSequence;
                try
                {
                    this.DbContext.Ado.BeginTran();
                    MD_AttachmentManagementApp _AttachmentManagementApp = new MD_AttachmentManagementApp();
                    _AttachmentManagementApp.DbContext = this.DbContext;

                    this.Update(entity);
                    if (filesId.Length > 0)//如果有附件就更新
                    {
                        //需要将新的附件更新相关单号
                        var newFiles = _AttachmentManagementApp.GetList(x => filesId.Contains(x.FileId))?.ToList();
                        var typeCodes = newFiles.Select(x => x.TypeOrder).Distinct()?.ToArray();
                        newFiles.ForEach(file =>
                        {
                            file.ReferenceDocNumber = entity.Id;
                            file.SupplyCode = entity.SupplyerCode;
                        });
                        if (_AttachmentManagementApp.Update(newFiles) > 0)
                        {
                            //先查询出来前台传入的信息，然后根据code和typecode去查找原附件信息，业务逻辑删除，
                            //后面审核通过和驳回根据相关单号更新或者找回
                            var oldFiles = _AttachmentManagementApp.GetList(x => x.ReferenceDocNumber == oldInfo.Id && typeCodes.Contains(x.TypeOrder))?.ToList();
                            _AttachmentManagementApp.Delete(oldFiles);
                        }

                    }
                    if (entity.AccessStatus == "3")
                    {

                        List<S_SupplierInfo> lstEntity = new List<S_SupplierInfo>();
                        lstEntity.Add(entity);
                        bool bResult = this.DoPost(lstEntity, out errMsg);
                        if (!bResult)
                        {
                            errMsg = "同步SAP失败：" + errMsg;
                            if (this.DbContext.Ado.IsAnyTran())
                            {
                                this.DbContext.Ado.RollbackTran();
                            }
                            return false;
                        }
                    }
                    this.DbContext.Ado.CommitTran();
                }
                catch (Exception ex)
                {
                    if (this.DbContext.Ado.IsAnyTran())
                    {
                        this.DbContext.Ado.RollbackTran();
                    }
                    throw ex;
                }
            }

            return flag;
        }
        #endregion

        #region 供应商信息维护导出
        /// <summary>
        ///
        /// </summary>
        /// <param name="SupplyerName"></param>
        /// <param name="SupplierCategory"></param>
        /// <param name="InOrOut"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<S_SupplierInfo> MaintainExportData(string SupplyerName, string SupplierCategory, string InOrOut, string Status, DateTime StartTime, DateTime EndTime)
        {
            var itemsData = this.DbContext.Queryable<S_SupplierInfo>().Where(t => (string.IsNullOrEmpty(SupplyerName) || t.SupplyerName.Contains(SupplyerName))
                && (string.IsNullOrEmpty(SupplierCategory) || t.SupplierCategory.Contains(SupplierCategory))
                // && (t.DateEstablishment >= StartTime) && (t.DateEstablishment <= EndTime.AddDays(1))
                && (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(InOrOut) || t.SupplyerName.Contains(InOrOut))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))).ToList();
            return itemsData;
        }
        #endregion

        #region 上传协议资料
        /// <summary>
        ///
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool AssessmentUpload(string user)
        {
            HttpRequest request = HttpContext.Current.Request;
            var supplierModel = request.Params["entity"].ToString();
            var fileModels = request.Params["files"].ToString();
            var entity = JsonConvert.DeserializeObject<S_SupplierInfo>(supplierModel);
            var files = JsonConvert.DeserializeObject<List<MD_AttachmentManagement>>(fileModels);
            if (entity == null) throw new Exception("供应商信息未传入");
            //entity = this.GetFirstEntity(x => x.Id == entity.Id);
            files.ForEach(file =>
            {
                file.ReferenceDocNumber = entity.Id;
                file.MUser = user;
                file.MTime = DateTime.Now;
            });
            return new MD_AttachmentManagementApp().Update(files) > 0;
            //if (new MD_AttachmentManagementApp().Update(files) > 0)
            //{
            //    entity.MUser = user;
            //    entity.MTime = DateTime.Now;
            //}
            //return this.Update(entity) > 0;
        }
        #endregion


        #region 供应商信息更新并重新提交审核
        /// <summary>
        ///
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="filesId"></param>
        /// <param name="userID"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public bool SupplierUpdateAndResubmit(S_SupplierInfo entity, string[] fileIds, string loginAccount, string userName, out string errMsg)
        {
            //校验是否本人
            if (!entity.CUser.Equals(loginAccount))
            {
                errMsg = "非当前条目创建人，无权操作！";
                return false;
            }
            var flag = true;
            errMsg = "";
            var oldInfo = this.GetFirstEntity(x => x.Id == entity.Id);
            //if (oldInfo.AccessStatus != "1")
            //{
            //    errMsg = "当前只支持未准入的供应商条目操作！";
            //    return false;
            //}
            entity.MUser = loginAccount;
            entity.MTime = DateTime.Now;
            entity.AccessStatus = "0";
            entity.Status = "0";
            entity.NextCheckUserCode = entity.BuyerCode;//默认下一个审核人是采购员
            entity.NextCheckUserName = entity.BuyerName;//默认下一个审核人是采购员
            entity.AuditSequence = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                MD_AttachmentManagementApp _AttachmentManagementApp = new MD_AttachmentManagementApp();
                _AttachmentManagementApp.DbContext = this.DbContext;

                this.Update(entity);
                if (fileIds.Length > 0)//如果有附件就更新
                {
                    //需要将新的附件更新相关单号
                    var newFiles = _AttachmentManagementApp.GetList(x => fileIds.Contains(x.FileId))?.ToList();
                    var typeCodes = newFiles.Select(x => x.TypeOrder).Distinct()?.ToArray();
                    newFiles.ForEach(file =>
                    {
                        file.ReferenceDocNumber = entity.Id;
                        file.SupplyCode = entity.SupplyerCode;
                    });
                    if (_AttachmentManagementApp.Update(newFiles) > 0)
                    {
                        //先查询出来前台传入的信息，然后根据code和typecode去查找原附件信息，业务逻辑删除，
                        //后面审核通过和驳回根据相关单号更新或者找回
                        var oldFiles = _AttachmentManagementApp.GetList(x => x.ReferenceDocNumber == oldInfo.Id && typeCodes.Contains(x.TypeOrder))?.ToList();
                        _AttachmentManagementApp.Delete(oldFiles);
                    }
                }
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                {
                    this.DbContext.Ado.RollbackTran();
                }
                throw ex;
            }
            return flag;
        }
        #endregion

    }
}
