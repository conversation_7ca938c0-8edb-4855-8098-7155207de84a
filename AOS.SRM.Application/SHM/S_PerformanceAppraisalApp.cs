using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 绩效考核应用层
    /// </summary>
    public class S_PerformanceAppraisalApp : BaseApp<S_PerformanceAppraisal>
    {
        /// <summary>
        /// 
        /// </summary>
        public Sys_DictionaryApp _dicApp = new Sys_DictionaryApp();
        /// <summary>
        /// 
        /// </summary>
        public Sys_MailApp _mailApp = new Sys_MailApp();
        /// <summary>
        /// 
        /// </summary>
        public Sys_UserApp _userApp = new Sys_UserApp();
        /// <summary>
        /// 
        /// </summary>
        public S_SupplierWarningLetterApp _letterApp = new S_SupplierWarningLetterApp();
        /// <summary>
        /// 
        /// </summary>
        public S_PerformanceAppraisalBaseApp _baseApp = new S_PerformanceAppraisalBaseApp();

        #region 新增
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entitys"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool AddEntitys(List<S_PerformanceAppraisal> entitys, Sys_User _currentUser ,out string msg)
        {
            var flag = true;
            
            try
            {
                msg = "";
                List<Sys_Mail> mailList = new List<Sys_Mail>();
                List<S_PerformanceAppraisalBase> baseList = new List<S_PerformanceAppraisalBase>();

                var dicEntity = _dicApp.GetFirstEntity(t => t.TypeCode == "PerformAssess" && t.EnumKey == 1);
                string nextUserCode = dicEntity.EnumValue;
                string nextUserName = dicEntity.EnumValue1;//第一个填写人

                int i = base.GetList(t => t.DocNum == entitys[0].DocNum).ToList().Count + 1;
                foreach (var entity in entitys)
                {
                    var bResult = base.Any(t => t.DocNum == entity.DocNum && t.SupplierCode == entity.SupplierCode);
                    if (!bResult)
                    {
                        entity.Id = Guid.NewGuid().ToString();
                        entity.Line = i;
                        entity.Sequence = 1;
                        entity.NextChkUserCode = nextUserCode;
                        entity.NextChkUserName = nextUserName;
                        entity.CUser = _currentUser.LoginAccount;
                        entity.CTime = DateTime.Now;
                        entity.IsDelete = false;
                        i = i + 1;

                        #region 更新下一个考核日期
                        var entityBase = _baseApp.GetFirstEntity(t => t.AssessYear == entity.AssessYear && t.SupplierCode == entity.SupplierCode);
                        if (entityBase == null)
                        {
                            msg = string.Format("没有供应商[{0}]的基础配置信息");
                            return false;
                        }
                        var date = entityBase.NextDate.HasValue ? entityBase.NextDate.Value : DateTime.Now;
                        entityBase.NextDate = GetNextDate(date, entityBase.AssessMode);
                        entityBase.MUser = _currentUser.LoginAccount;
                        entityBase.MTime = DateTime.Now;
                        baseList.Add(entityBase);
                        #endregion
                    }
                };
                #region 发邮件给下一个人
                
                string email = _userApp.GetFirstEntity(t => t.LoginAccount == nextUserCode).Email;
                string systemAddr = _dicApp.GetFirstEntity(t => t.TypeCode == "SystemAddr" && t.EnumKey == 1).EnumValue;
                if (!string.IsNullOrEmpty(email))
                {
                    Sys_Mail mail = new Sys_Mail();
                    mail.MailID = Guid.NewGuid().ToString();
                    mail.UserID = nextUserCode;
                    mail.UserName = nextUserName;
                    mail.MessageTypeID = "1001";
                    mail.MailSubject = "绩效考核综合评定";
                    mail.MailBody = "您好，本月有绩效考核的供应商，请前往西子SRM系统确认！系统地址:" + systemAddr;
                    mail.ReceiverMail = email;
                    mail.SendTime = DateTime.Now;
                    mail.SenderDisplayName = "System";
                    mail.IsDelete = false;
                    mail.CUser = "System";
                    mail.CTime = DateTime.Now;
                    mail.AutoSend = false;
                    mailList.Add(mail);
                }
                #endregion

                DbContext.Ado.BeginTran();
                _mailApp.DbContext = this.DbContext;
                _baseApp.DbContext = this.DbContext;

                base.Insert(entitys);
                _mailApp.Insert(mailList);
                if (baseList.Count > 0) { _baseApp.Update(baseList); }

                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                msg = ex.Message;
                return false;
            }
            return flag;
        }
        #endregion

        #region 修改
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="entitys"></param>
        /// <param name="_currentUser"></param>
        /// <returns></returns>
        public bool UpdateEntitys(List<S_PerformanceAppraisal> entitys, Sys_User _currentUser)
        {
            var flag = true;
            try
            {
                string nextUserCode = string.Empty;
                string nextUserName = string.Empty;
                List<Sys_Mail> mailList = new List<Sys_Mail>();
                List<S_SupplierWarningLetter> letterList = new List<S_SupplierWarningLetter>();

                foreach (var item in entitys)
                {
                    #region 赋值
                    item.MTime = DateTime.Now;
                    item.MUser = _currentUser.LoginAccount;
                    if (item.Sequence == 1) //质量
                    {
                        item.Q_User = _currentUser.UserName;
                        item.Q_Time = DateTime.Now;
                        //评定
                        item.Q_Score = item.Q_PPMFPY + item.Q_Process + item.Q_Improved + item.Q_Escape + item.Q_AddPoints + item.Q_DeducPoints + item.Q_AMT;
                    }
                    else if (item.Sequence == 2)//绩效
                    {
                        item.D_User = _currentUser.UserName;
                        item.D_Time = DateTime.Now;
                        //评定
                        item.D_Score = item.D_Timely + item.D_MissPart + item.D_Integrity;
                    }
                    else if (item.Sequence == 3)//合作
                    {
                        item.C_User = _currentUser.UserName;
                        item.C_Time = DateTime.Now;
                        item.C_Score = item.C_SQCC;
                    }
                    else if (item.Sequence == 4)
                    {
                        item.T_User = _currentUser.UserName;
                        item.T_Time = DateTime.Now;
                        item.T_Score = item.T_Technical;
                    }
                    //下一个操作人
                    var nextSequence = item.Sequence + 1;
                    var dicEntity = _dicApp.GetFirstEntity(t => t.TypeCode == "PerformAssess" && t.EnumKey == nextSequence);
                    if (dicEntity != null)
                    {
                        item.Sequence = nextSequence;
                        item.NextChkUserCode = dicEntity.EnumValue;
                        item.NextChkUserName = dicEntity.EnumValue1;
                        nextUserCode = dicEntity.EnumValue;
                        nextUserName = dicEntity.EnumValue1;
                    }
                    else //综合评定
                    {
                        item.Sequence = null;
                        item.NextChkUserCode = "";
                        item.NextChkUserName = "";
                        item.TotalScore = item.Q_Score + item.D_Score + item.C_Score + item.T_Score;
                        item.Rating = GradeEvaluation(item);
                    }
                    #endregion

                    #region 生成警告函信息并邮件通知供应商和财务
                    if (item.Rating == "不合格")
                    {
                        S_SupplierWarningLetter letter = new S_SupplierWarningLetter();
                        letter.Id = Guid.NewGuid().ToString();
                        letter.DocNum = item.DocNum;
                        letter.SupplierCode = item.SupplierCode;
                        letter.SupplierName = item.SupplierName;
                        letter.Q_Score = item.Q_Score;
                        letter.D_Score = item.D_Score;
                        letter.C_Score = item.C_Score;
                        letter.T_Score = item.T_Score;
                        letter.TotalScore = item.TotalScore;
                        letter.Q_Rating = item.Q_Score >= 30 ? "合格" : "不合格";
                        letter.D_Rating = item.D_Score >= 24 ? "合格" : "不合格";
                        letter.Rating = item.Rating;
                        letter.WarnNumber = GetWarnNumber(item.CTime.Value.Year, item.SupplierCode) + 1;
                        letter.DueDate = DateTime.Now.AddDays(3);
                        letter.DisposalResult = GetDisposalResult(letter.WarnNumber.Value);
                        letter.Status = "0";
                        letter.CUser = _currentUser.LoginAccount;
                        letter.CTime = DateTime.Now;
                        letterList.Add(letter);

                        #region 邮件通知供应商
                        string email = _userApp.GetFirstEntity(t => t.LoginAccount == letter.SupplierCode).Email;
                        string systemAddr = _dicApp.GetFirstEntity(t => t.TypeCode == "SystemAddr" && t.EnumKey == 1).EnumValue;
                        if (!string.IsNullOrEmpty(email))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MailID = Guid.NewGuid().ToString();
                            mail.UserID = letter.SupplierCode;
                            mail.UserName = letter.SupplierName;
                            mail.MessageTypeID = "1001";
                            mail.MailSubject = "供应商警告函";
                            mail.MailBody = "您好，您有警告函待接收，请前往西子SRM系统确认！系统地址:" + systemAddr;
                            mail.ReceiverMail = email;
                            mail.SendTime = DateTime.Now;
                            mail.SenderDisplayName = "System";
                            mail.IsDelete = false;
                            mail.CUser = "System";
                            mail.CTime = DateTime.Now;
                            mail.AutoSend = false;
                            mailList.Add(mail);
                        }
                        #endregion

                        #region 邮件通知财务？
                        //string email = _userApp.GetFirstEntity(t => t.LoginAccount == letter.SupplierCode).Email;
                        //string systemAddr = _dicApp.GetFirstEntity(t => t.TypeCode == "SystemAddr" && t.EnumKey == 1).EnumValue;
                        //if (!string.IsNullOrEmpty(email))
                        //{
                        //    mail = new Sys_Mail();
                        //    mail.MailID = Guid.NewGuid().ToString();
                        //    mail.UserID = letter.SupplierCode;
                        //    mail.UserName = letter.SupplierName;
                        //    mail.MessageTypeID = "1001";
                        //    mail.MailSubject = "供应商警告函";
                        //    mail.MailBody = "您好，您有警告函待接收，请前往西子SRM系统确认！系统地址:" + systemAddr;
                        //    mail.ReceiverMail = email;
                        //    mail.SendTime = DateTime.Now;
                        //    mail.SenderDisplayName = "System";
                        //    mail.IsDelete = false;
                        //    mail.CUser = "System";
                        //    mail.CTime = DateTime.Now;
                        //    mail.AutoSend = false;
                        //}
                        #endregion
                    }
                    #endregion
                }
                if (!string.IsNullOrEmpty(nextUserCode))
                {
                    #region 发邮件给下一个人
                    string email = _userApp.GetFirstEntity(t => t.LoginAccount == nextUserCode).Email;
                    string systemAddr = _dicApp.GetFirstEntity(t => t.TypeCode == "SystemAddr" && t.EnumKey == 1).EnumValue;
                    if (!string.IsNullOrEmpty(email))
                    {
                        Sys_Mail mail = new Sys_Mail();
                        mail.MailID = Guid.NewGuid().ToString();
                        mail.UserID = nextUserCode;
                        mail.UserName = nextUserName;
                        mail.MessageTypeID = "1001";
                        mail.MailSubject = "绩效考核综合评定";
                        mail.MailBody = "您好，本月有绩效考核的供应商，请前往西子SRM系统确认！系统地址:" + systemAddr;
                        mail.ReceiverMail = email;
                        mail.SendTime = DateTime.Now;
                        mail.SenderDisplayName = "System";
                        mail.IsDelete = false;
                        mail.CUser = "System";
                        mail.CTime = DateTime.Now;
                        mail.AutoSend = false;
                        mailList.Add(mail);
                    }
                    #endregion
                }

                DbContext.Ado.BeginTran();
                _mailApp.DbContext = this.DbContext;
                _letterApp.DbContext = this.DbContext;

                base.Update(entitys);
                if (mailList.Count > 0)
                { _mailApp.Insert(mailList); }
                if (letterList.Count > 0)
                { _letterApp.Insert(letterList); }

                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 导入

        /// <summary>
        /// 
        /// </summary>
        /// <param name="excelList"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool ImprotExcelToBaseData(List<S_PerformanceAppraisal> excelList, Sys_User _currentUser, out string msg)
        {
            msg = "";
            var flag = true;
            var insertList = new List<S_PerformanceAppraisal>();
            var letterList = new List<S_SupplierWarningLetter>();
            Sys_Mail mail = new Sys_Mail();
            var baseList = new List<S_PerformanceAppraisalBase>();

            if (!ValidateCheck(excelList, out msg))
            {
                return false;
            }

            var dicList = _dicApp.GetList(t => t.TypeCode == "PerformAssess").ToList();
            int i = 1;
            foreach (var item in excelList)
            {
                var assessYear = item.DocNum.Substring(0, 4);
                var data = base.GetFirstEntity(t => t.DocNum == item.DocNum && t.SupplierCode == item.SupplierCode);
                if (data == null) //判断是否存在
                {
                    #region 更新下一个考核日期
                    var entityBase = _baseApp.GetFirstEntity(t => t.AssessYear == assessYear && t.SupplierCode == item.SupplierCode);
                    if (entityBase == null)
                    {
                        msg = string.Format("没有供应商[{0}]的基础配置信息");
                        return false;
                    }
                    var date = entityBase.NextDate.HasValue ? entityBase.NextDate.Value : DateTime.Now;
                    entityBase.NextDate = GetNextDate(date, entityBase.AssessMode);
                    entityBase.MUser = _currentUser.LoginAccount;
                    entityBase.MTime = DateTime.Now;
                    baseList.Add(entityBase);
                    #endregion

                    item.Id = Guid.NewGuid().ToString();
                    item.CTime = DateTime.Now;
                    item.CUser = _currentUser.LoginAccount;
                    item.Line = i;
                    item.AssessYear = item.DocNum.Substring(0, 4);
                    item.Q_User = dicList.Find(t => t.EnumKey == 1).EnumValue1;
                    item.Q_Time = DateTime.Now;
                    item.D_User = dicList.Find(t => t.EnumKey == 2).EnumValue1;
                    item.D_Time = DateTime.Now;
                    item.C_User = dicList.Find(t => t.EnumKey == 3).EnumValue1;
                    item.C_Time = DateTime.Now;
                    item.T_User = dicList.Find(t => t.EnumKey == 4).EnumValue1;
                    item.T_Time = DateTime.Now;
                    insertList.Add(item);

                    i += 1;
                    #region 不合格生成警告函信息
                    if (item.Rating == "不合格")
                    {
                        S_SupplierWarningLetter letter = new S_SupplierWarningLetter();
                        letter.Id = Guid.NewGuid().ToString();
                        letter.DocNum = item.DocNum;
                        letter.SupplierCode = item.SupplierCode;
                        letter.SupplierName = item.SupplierName;
                        letter.Q_Score = item.Q_Score;
                        letter.D_Score = item.D_Score;
                        letter.C_Score = item.C_Score;
                        letter.T_Score = item.T_Score;
                        letter.TotalScore = item.TotalScore;
                        letter.Q_Rating = item.Q_Score >= 30 ? "合格" : "不合格";
                        letter.D_Rating = item.D_Score >= 24 ? "合格" : "不合格";
                        letter.Rating = item.Rating;
                        letter.WarnNumber = GetWarnNumber(item.CTime.Value.Year, item.SupplierCode) + 1;
                        letter.DueDate = DateTime.Now.AddDays(3);
                        letter.DisposalResult = GetDisposalResult(letter.WarnNumber.Value);
                        letter.Status = "0";
                        letter.CUser = _currentUser.LoginAccount;
                        letter.CTime = DateTime.Now;
                        letterList.Add(letter);

                        #region 邮件通知供应商
                        string email = _userApp.GetFirstEntity(t => t.LoginAccount == letter.SupplierCode).Email;
                        string systemAddr = _dicApp.GetFirstEntity(t => t.TypeCode == "SystemAddr" && t.EnumKey == 1).EnumValue;
                        if (!string.IsNullOrEmpty(email))
                        {
                            mail = new Sys_Mail();
                            mail.MailID = Guid.NewGuid().ToString();
                            mail.UserID = letter.SupplierCode;
                            mail.UserName = letter.SupplierName;
                            mail.MessageTypeID = "1001";
                            mail.MailSubject = "供应商警告函";
                            mail.MailBody = "您好，您有警告函待接收，请前往西子SRM系统确认！系统地址:" + systemAddr;
                            mail.ReceiverMail = email;
                            mail.SendTime = DateTime.Now;
                            mail.SenderDisplayName = "System";
                            mail.IsDelete = false;
                            mail.CUser = "System";
                            mail.CTime = DateTime.Now;
                            mail.AutoSend = false;
                        }
                        #endregion
                    }
                    #endregion
                }
            }
            try
            {
                this.DbContext.Ado.BeginTran();
                _mailApp.DbContext = this.DbContext;
                _letterApp.DbContext = this.DbContext;
                _baseApp.DbContext = this.DbContext;

                if (insertList.Count() > 0) { this.Insert(insertList); }

                if (mail != null) { _mailApp.Insert(mail); }

                if (letterList.Count > 0) { _letterApp.Insert(letterList); }

                if (baseList.Count > 0) { _baseApp.Update(baseList); }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 评定等级
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public string GradeEvaluation(S_PerformanceAppraisal entity)
        {
            if (entity.Q_Score < 30 || entity.D_Score < 24 || entity.TotalScore < 60)
                return "不合格";
            else if (entity.TotalScore >= 60 && entity.TotalScore <= 80)
                return "合格";
            else if (entity.TotalScore >= 81 && entity.TotalScore <= 90)
                return "良好";
            else if (entity.TotalScore >= 91 && entity.TotalScore <= 100)
                return "优秀";
            else
                return "评定失败";
        }

        /// <summary>
        /// 获取年度警告累计次数
        /// </summary>
        /// <param name="year"></param>
        /// <param name="supplierCode"></param>
        /// <returns></returns>
        public int GetWarnNumber(int year, string supplierCode)
        {
            var number = 0;
            var data = _letterApp.GetList(t => t.CTime.Value.Year == year && t.SupplierCode == supplierCode).ToList();
            if (data != null && data.Count > 0)
                number = data.Count;

            return number;
        }
        /// <summary>
        /// 获取绩效考核处置结果
        /// </summary>
        /// <param name="number"></param>
        /// <returns></returns>
        public string GetDisposalResult(int number)
        {
            var dicEntity = _dicApp.GetFirstEntity(t => t.TypeCode == "DisposalResult" && t.EnumKey == number);
            if (dicEntity != null)
                return dicEntity.EnumValue;
            else
                return "";
        }

        /// <summary>
        /// 导入校验规则
        /// </summary>
        /// <param name="excelList"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool ValidateCheck(List<S_PerformanceAppraisal> excelList, out string msg)
        {
            msg = "";
            var flag = true;

            foreach (var item in excelList)
            {
                if (string.IsNullOrEmpty(item.SupplierCode))
                {
                    msg = "供应商编码不能为空";
                    flag = false;
                    break;
                }

                if (string.IsNullOrEmpty(item.SupplierName))
                {
                    msg = "供应商名称不能为空";
                    flag = false;
                    break;
                }
            }
            return flag;

        }

        #region 获取下一个考核日期
        /// <summary>
        /// 
        /// </summary>
        /// <param name="date"></param>
        /// <param name="mode"></param>
        /// <returns></returns>
        public DateTime GetNextDate(DateTime date, string mode)
        {
            switch (mode)
            {
                case "月度":
                    date = date.AddMonths(1);
                    break;
                case "季度":
                    date = date.AddMonths(3);
                    break;
                case "半年度":
                    date = date.AddMonths(6);
                    break;
                case "年度":
                    date = date.AddYears(1);
                    break;
                default:
                    break;
            }
            return date;
        }
        #endregion

        #endregion
    }
}
