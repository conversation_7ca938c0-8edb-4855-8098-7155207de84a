using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 合同应用层
    /// </summary>
    public class S_ContractApp : BaseApp<S_Contract>
    {
        MD_AttachmentManagementApp _attachApp = new MD_AttachmentManagementApp();

        #region 新增
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="fileIds"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool AddEntity(S_Contract entity, List<string> fileIds, Sys_User _currentUser, out string msg)
        {
            var flag = true;

            try
            {
                msg = string.Empty;
                var attachList = new List<MD_AttachmentManagement>();

                entity.Id = Guid.NewGuid().ToString();
                entity.CUser = _currentUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.IsDelete = false;
                entity.Status = "已提交";

                if (fileIds.Count > 0)
                {
                    attachList = _attachApp.GetList(t => fileIds.Contains(t.FileId)).ToList();
                    attachList.ForEach(t =>
                    {
                        t.ReferenceDocNumber = entity.Id;
                        t.MUser = _currentUser.LoginAccount;
                        t.MTime = DateTime.Now;
                    });
                }

                this.DbContext.Ado.BeginTran();

                this.Insert(entity);
                if (fileIds.Count > 0) { _attachApp.Update(attachList); }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 修改
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="fileIds"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool UpdateEntity(S_Contract entity, List<string> fileIds, Sys_User _currentUser, out string msg)
        {
            var flag = true;

            try
            {
                msg = string.Empty;
                var attachList = new List<MD_AttachmentManagement>();

                entity.MUser = _currentUser.LoginAccount;
                entity.MTime = DateTime.Now;

                if (fileIds.Count > 0)
                {
                    attachList = _attachApp.GetList(t => fileIds.Contains(t.FileId)).ToList();
                    attachList.ForEach(t =>
                    {
                        t.ReferenceDocNumber = entity.Id;
                        t.MUser = _currentUser.LoginAccount;
                        t.MTime = DateTime.Now;
                    });
                    //删除原附件
                    var old = _attachApp.GetList(t => t.ReferenceDocNumber == entity.Id)?.ToList();
                    if (old != null && old.Count > 0)
                    {
                        old.ForEach(t =>
                        {
                            t.IsDelete = true;
                            t.DUser = _currentUser.LoginAccount;
                            t.DTime = DateTime.Now;
                        });
                        attachList.AddRange(old);
                    }
                }

                this.DbContext.Ado.BeginTran();

                this.Update(entity);
                if (fileIds.Count > 0) { _attachApp.Update(attachList); }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 回传
        /// <summary>
        /// 回传
        /// </summary>
        /// <param name="id"></param>
        /// <param name="status"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool UploadReturn(string id,string status, Sys_User _currentUser, out string msg)
        {
            var flag = true;
            try
            {
                msg = "";
                var entity = base.GetEntityByKey(id);
                entity.Status = status;
                entity.MUser = _currentUser.LoginAccount;
                entity.MTime = DateTime.Now;

                var newAttatch = _attachApp.Upload(_currentUser.UserName);
                newAttatch.ReferenceDocNumber = id;
                entity.AttachId = newAttatch.FileId;

                var attachList = _attachApp.GetList(t => t.ReferenceDocNumber == id)?.ToList();
                if (attachList != null && attachList.Count > 0)
                {
                    attachList.ForEach(t =>
                    {
                        t.IsDelete = true;
                        t.DTime = DateTime.Now;
                        t.DUser = _currentUser.LoginAccount;
                    });
                }

                this.DbContext.Ado.BeginTran();
                _attachApp.DbContext = this.DbContext;

                this.Update(entity);
                _attachApp.Insert(newAttatch);
                if (attachList != null && attachList.Count > 0) { _attachApp.Update(attachList); }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion
    }
}
