using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 
    /// </summary>
    public class S_DrawingDistributionRecyclingApp : BaseApp<S_DrawingDistributionRecycling>
    {
        //Sys_UserApp _userApp = new Sys_UserApp();
        Sys_MailApp _mailApp = new Sys_MailApp();
        SupplierInfoApp _supplierApp = new SupplierInfoApp();

        #region 导入

        /// <summary>
        /// 
        /// </summary>
        /// <param name="excelList"></param>
        /// <param name="opUser"></param>
        /// <returns></returns>
        public bool ImprotExcelToBaseData(List<S_DrawingDistributionRecycling> excelList, string opUser)
        {
            var flag = 0;
            var updateList = new List<S_DrawingDistributionRecycling>();
            var insertList = new List<S_DrawingDistributionRecycling>();
            if (!ValidateCheck(excelList))
            {
                return false;
            }

            foreach (var item in excelList)
            {
                //var baseEntity = base.GetFirstEntity(t => t.AssessYear == item.AssessYear && t.SupplierCodes == item.SupplierCodes);
                //if (baseEntity == null)
                //{
                item.Id = Guid.NewGuid().ToString();
                item.CTime = DateTime.Now;
                item.CUser = opUser;
                item.Status = "已提交";
                insertList.Add(item);
                //}
                //else
                //{
                //    baseEntity.SupplierName = item.SupplierName;
                //    baseEntity.SupplyProducts = item.SupplyProducts;
                //    baseEntity.Category = item.Category;
                //    baseEntity.AssessMode = item.AssessMode;
                //    baseEntity.Remark = item.Remark;
                //    baseEntity.MTime = DateTime.Now;
                //    baseEntity.MUser = opUser;
                //    updateList.Add(baseEntity);
                //}
            }
            try
            {
                this.DbContext.Ado.BeginTran();
                if (insertList.Count() > 0)
                {
                    flag = this.Insert(insertList);
                }
                if (updateList.Count() > 0)
                {
                    flag += this.Update(updateList);
                }
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag > 0;
        }

        /// <summary>
        /// 导入校验规则
        /// </summary>
        /// <param name="excelList"></param>
        /// <returns></returns>
        public bool ValidateCheck(List<S_DrawingDistributionRecycling> excelList)
        {
            var flag = true;

            foreach (var item in excelList)
            {
                if (string.IsNullOrEmpty(item.ItemCode))
                    throw new Exception("供应商编码不能为空");

            }
            return flag;

        }

        #endregion

        #region 发放
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool SendOut(string[] ids, Sys_User _currentUser,out string msg)
        {
            var flag = true;

            try
            {
                msg = "";
                List<string> lstSupplierCode = new List<string>();
                List<Sys_Mail> mailList = new List<Sys_Mail>();

                var lstEntity = this.GetListByKeys(ids.ToArray());
                lstEntity.ForEach(t =>
                {
                    t.Status = "已发放";
                    t.DistributeDate = DateTime.Now;
                    t.MUser = _currentUser.LoginAccount;
                    t.MTime = DateTime.Now;

                    string[] arrSupplierCode = t.SupplierCodes.Split(',');
                    foreach (var item in arrSupplierCode)
                    {
                        if (lstSupplierCode.IndexOf(item) < 0)
                        {
                            lstSupplierCode.Add(item);
                        }
                    }
                });
                //获取邮箱
                var supplierList = _supplierApp.GetList(t => lstSupplierCode.Contains(t.SupplyerCode)).ToList();

                supplierList.ForEach(t =>
                {
                    Sys_Mail mail = new Sys_Mail()
                    {
                        MailID = Guid.NewGuid().ToString(),
                        UserID = t.SupplyerCode,
                        UserName = t.SupplyerName1,
                        MessageTypeID = "1001",
                        MailSubject = "图纸发放通知",
                        MailBody = "您好，" + DateTime.Now.ToString("yyyy年MM月dd日") + "有图纸发放，请前往西子SRM系统确认",
                        ReceiverMail = t.EMail,
                        IsDelete = false,
                        CUser = "admin",
                        CTime = DateTime.Now,
                        SenderDisplayName = "System",
                        AutoSend = false
                    };
                    mailList.Add(mail);
                });
                this.DbContext.Ado.BeginTran();
                _mailApp.DbContext = this.DbContext;

                this.Update(lstEntity);
                _mailApp.Insert(mailList);
                
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                flag = false;
                msg = ex.Message;
                return flag;
            }
            return flag;
            
        }
        #endregion
    }
}
