using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.SHM.DisposalManagement;
using AOS.SRM.Entity.SHM.ViewModel;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 让步接收处置单
    /// </summary>
    public class CompromiseDisposalApp : BaseApp<S_CompromiseDisposal>
    {
        /// <summary>
        /// 
        /// </summary>
        public CompromiseDisposalDetailsApp _detailApp = new CompromiseDisposalDetailsApp();


        #region 查询物料主数据
        /// <summary>
        /// 查询物料主数据
        /// </summary>
        /// <param name="page"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <returns></returns>
        public List<XZ_SAP_MARC> GetMaterial(Pagination page, string MaterialCode, string MaterialName)
        {
            //var itemsData = DbContext.Queryable<XZ_SAP_MARC>(
            //     ) .Where(t => string.IsNullOrEmpty(MaterialCode) || t.ItemCode.Contains(MaterialCode)
            //            || (string.IsNullOrEmpty(MaterialName) || t.ItemName.Contains(MaterialName)))
            //    .ToList();
            var itemsData = DbContext.Queryable<XZ_SAP_MARC>(
                 ).Where(t => string.IsNullOrEmpty(MaterialCode) && (string.IsNullOrEmpty(MaterialName))
                       || (t.ItemCode.Contains(MaterialCode) || t.ItemName.Contains(MaterialName)))
                .ToList();
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<XZ_SAP_MARC>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        #endregion

        #region 让步接收处置单主信息/查询列表信息 
        /// <summary>
        /// 查询列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="DisposalNo"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<S_CompromiseDisposal> GetList(Pagination page, string SupplyCode, string DisposalNo, string Status, DateTime StartTime, DateTime EndTime)
        {
            var query = this.DbContext.Queryable<S_CompromiseDisposal>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status.ToString()) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(DisposalNo) || t.DisposalNo.Contains(DisposalNo))
                && t.IsDelete == false);
            
            page.Total = query.Count();
            var itemPageData = new List<S_CompromiseDisposal>();
            itemPageData = query.ToPageList(page.PageNumber, page.PageSize)
                .OrderByDescending(t => t.CTime).ToList();
            return itemPageData;
        }

        /// <summary>
        /// 让步接收处置单主信息
        /// </summary>
        /// <param name="DisposalNo"></param>
        /// <returns></returns>
        public S_CompromiseDisposal GetInfo(string DisposalNo)
        {
            var itemsData = this.GetList().Where(x => x.DisposalNo.Equals(DisposalNo)).ToList().FirstOrDefault();
            return itemsData;
        }

        #endregion

        #region 让步接收处置单列表提交
        /// <summary>
        /// 让步接收处置单列表提交
        /// </summary>
        /// <param name="main"></param>
        /// <param name="userCode"></param>
        /// <returns></returns>
        public bool InsertCompromiseDisposal(I_CompromiseDisposal_Views main, string userCode)
        {
            try
            {
                //提交主表信息
                S_CompromiseDisposal app = new S_CompromiseDisposal();
                app.DisposalNo = main.DisposalNo;
                app.SupplyName = main.SupplyName;
                app.SupplyCode = main.SupplyCode;
                app.CUser = userCode;
                app.Status = "1";
                app.InitiatingDept = main.InitiatingDept;
                app.Initiatinger = main.Initiatinger;
                app.DisposalDate = Convert.ToDateTime(main.DisposalDate.ToString("yyyy-MM-dd"));
                app.ProblemSource = main.ProblemSource;
                app.ProblemDescription = main.ProblemDescription;
                app.HandlingOpinions = main.HandlingOpinions;
                app.CompanyCode = "2002";
                app.DisposalMoney = main.DisposalMoney;


                Insert(app);
                //提交子表信息
                foreach (var item in main.CompromiseDisposalDetails)
                {
                    item.CUser = userCode;
                    item.DisposalNo = main.DisposalNo;
                    item.CompanyCode = "2002";
                    item.SupplyCode = main.SupplyName;
                }
                _detailApp.Insert(main.CompromiseDisposalDetails);

                return true;

            }
            catch (Exception ex)
            {
                return false;
                throw ex;

            }

        }
        #endregion

        #region 财务定价
        /// <summary>
        /// 财务定价
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="userCode"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public bool FinancialPricing(ConcessionAcceptDto dto,string userCode, string UserName)
        {
            try
            {
                //更新主表信
                var mainUpdate = dto.main;
                var main = GetFirstEntityByFieldValue("DisposalNo", mainUpdate.DisposalNo);
                main.DisposalMoney = mainUpdate.DisposalMoney;
                main.Status = "6";//财务定价
                main.MakePriceDate = DateTime.Now;
                main.MakePriceUser = UserName;
                main.MUser = userCode;
                main.MTime = DateTime.Now;

                //提交子表信息
                foreach (var item in dto.detail)
                {
                    item.CUser = userCode;
                    item.CTime = DateTime.Now;
                    item.DisposalNo = main.DisposalNo;
                    item.CompanyCode = "2002";
                    item.SupplyCode = main.SupplyCode;
                }

                DbContext.Ado.BeginTran();
                _detailApp.DbContext = this.DbContext;

                Update(main);
                var detailOld = _detailApp.GetList(t => t.DisposalNo == main.DisposalNo).ToList();
                _detailApp.HardDelete(detailOld);
                _detailApp.Insert(dto.detail);

                DbContext.Ado.CommitTran();
                return true;

            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                return false;
                throw ex;

            }

        }
        #endregion

        #region 供应商意见提交(修改功能)
        /// <summary>
        /// 供应商意见提交
        /// </summary>
        /// <param name="CompromiseDisposal"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertSupplyOptions(S_CompromiseDisposal CompromiseDisposal, string[] Id, string UserName)
        {
            List<S_CompromiseDisposal> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.SupplyUserName = UserName;
                item.SupplyDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.SupplyOptions = CompromiseDisposal.SupplyOptions;
                item.Status = "2";
            }
            return Update(Disposal);
        }
        #endregion

        #region 发出部门审核(修改功能)
        /// <summary>
        /// 发出部门审核
        /// </summary>
        /// <param name="CompromiseDisposal"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertRepDeptOptions(S_CompromiseDisposal CompromiseDisposal, string[] Id, string UserName)
        {

            List<S_CompromiseDisposal> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.Status = "3";
                item.RelDeptUserName = UserName;
                item.RelDeptDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.RelDeptOptions = CompromiseDisposal.RelDeptOptions;
                if (item.DisposalMoney <= 20000)
                {
                    item.Status = "4";
                    item.GMOUserName = "System";
                    item.GMODate = DateTime.Now.ToString("yyyy-MM-dd");
                    item.GMOptions = "系统默认";
                }
            }
            return Update(Disposal);
        }
        #endregion

        #region 总经理审核(修改功能)
        /// <summary>
        /// 总经理审核
        /// </summary>
        /// <param name="CompromiseDisposal"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertGMOOptions(S_CompromiseDisposal CompromiseDisposal, string[] Id, string UserName)
        {
            List<S_CompromiseDisposal> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.Status = "4";
                item.GMOUserName = UserName;
                item.GMODate = DateTime.Now.ToString("yyyy-MM-dd");
                item.GMOptions = CompromiseDisposal.GMOptions;
            }
            return Update(Disposal);
        }
        #endregion

        #region 财务审核(修改功能)
        /// <summary>
        /// 财务审核
        /// </summary>
        /// <param name="CompromiseDisposal"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertFinanceOptions(S_CompromiseDisposal CompromiseDisposal, string[] Id, string UserName)
        {
            List<S_CompromiseDisposal> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.FinanceUserName = UserName;
                item.FinanceOptions = CompromiseDisposal.FinanceOptions;
                item.FinanceDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.Status = "5";
            }
            return Update(Disposal);
        }
        #endregion

        #region 不合格处置单删除
        /// <summary>
        /// 不合格处置单删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public int DeleteCompromiseDisposal(string[] ids)
        {
            List<S_CompromiseDisposal> CompromiseDisposal = GetListByKeys(ids);
            return DeleteWithTran(CompromiseDisposal);
        }
        #endregion
    }


    #region 查询让步接收处置单明细信息
    /// <summary>
    /// 查询让步接收处置单明细信息
    /// </summary>
    public class CompromiseDisposalDetailsApp : BaseApp<S_CompromiseDisposalDetails>
    {
        /// <summary>
        /// 查询让步接收处置单明细信息
        /// </summary>
        /// <param name="DisposalNo"></param>
        /// <returns></returns>
        public List<S_CompromiseDisposalDetails> GetList(string DisposalNo)
        {
            var itemsData = this.GetList().Where(x => x.DisposalNo.Equals(DisposalNo))?.ToList();
            return itemsData;
        }

    }

    #endregion
}
