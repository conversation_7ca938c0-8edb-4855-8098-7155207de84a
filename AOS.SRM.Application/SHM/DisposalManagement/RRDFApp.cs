using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 供应商奖励/处置返还申请单
    /// </summary>
    public class RRDFApp : BaseApp<S_RRDF>
    {
        #region 保存
        /// <summary>
        /// 保存功能
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="userCode"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public S_RRDF InsertRRDF(S_RRDF RRDF, string userCode, string UserName)
        {
            //  1代表未下达给供应商
            RRDF.Status = "1";
            //RRDF.SupplyUserName = UserName;
            RRDF.CUser = userCode;
            RRDF.CompanyCode = "2002";
            return Insert(RRDF);
        }
        #endregion

        #region 查询供应商奖励/处置返还申请
        /// <summary>
        /// 查询不合格处置单列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="RNo"></param>
        /// <param name="Status"></param>
        /// <param name="ApplyType"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<S_RRDF> GetRRDF(Pagination page, string SupplyCode, string RNo, string Status, string ApplyType, DateTime StartTime, DateTime EndTime)
        {
            var query = this.DbContext.Queryable<S_RRDF>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(ApplyType) || t.ApplyType.Contains(ApplyType))
                && (string.IsNullOrEmpty(RNo) || t.RNo.Contains(RNo))
                && t.IsDelete == false);
            
            page.Total = query.Count();
            var itemPageData = new List<S_RRDF>();
            itemPageData = query.ToPageList(page.PageNumber, page.PageSize)
                 .OrderByDescending(t => t.CTime).ToList();
            return itemPageData;
        }
        #endregion

        #region 删除
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public int DeleteRRDF(string[] ids)
        {
            List<S_RRDF> rrdf = GetListByKeys(ids);
            if (ids == null)
            {
                return -1;
            }
            return DeleteWithTran(rrdf);
        }
        #endregion

        #region  供应商管理意见提交
        /// <summary>
        /// 供应商管理意见提交
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <param name="_currentUser"></param>
        /// <returns></returns>
        public int SupplyManageOptions(S_RRDF RRDF, string[] Id, Sys_User _currentUser)
        {
            List<S_RRDF> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.SupplyUserName = _currentUser.UserName;
                item.SupplyOptions = RRDF.SupplyOptions;
                item.Status = "2";
                item.SupplyDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.MUser = _currentUser.LoginAccount;
                item.MTime = DateTime.Now;
            }
            return Update(Disposal);
        }
        #endregion

        #region 质量中心审核(修改功能)
        /// <summary>
        /// 质量中心审核
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertRepDeptOptions(S_RRDF RRDF, string[] Id, string UserName)
        {

            List<S_RRDF> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.Status = "3";
                item.SQEUserName = UserName;
                item.SQEDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.SQEOptions = RRDF.SQEOptions;
            }
            return Update(Disposal);
        }
        #endregion

        #region 财务审核(修改功能)
        /// <summary>
        /// 财务审核
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertFinanceOptions(S_RRDF RRDF, string[] Id, string UserName)
        {
            List<S_RRDF> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.Status = "4";
                item.FinanceUserName = UserName;
                item.FinanceDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.FinanceOptions = RRDF.FinanceOptions;
                if (item.DisposalMoney <= 20000)
                {
                    item.Status = "5";
                    item.GMOUserName = "System";
                    item.GMODate = DateTime.Now.ToString("yyyy-MM-dd");
                    item.GMOptions = "系统默认";
                }
            }
            return Update(Disposal);
        }
        #endregion

        #region 总经理审核(修改功能)
        /// <summary>
        /// 总经理审核
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertGMOOptions(S_RRDF RRDF, string[] Id, string UserName)
        {
            List<S_RRDF> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.GMOUserName = UserName;
                item.GMOptions = RRDF.GMOptions;
                item.GMODate = DateTime.Now.ToString("yyyy-MM-dd");
                item.Status = "5";
            }
            return Update(Disposal);
        }
        #endregion

        #region 财务部门签收(修改功能)
        /// <summary>
        /// 财务部门签收
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertFinanceSign(S_RRDF RRDF, string[] Id, string UserName)
        {
            List<S_RRDF> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.FinanceSignUserName = UserName;
                item.FinanceSignOptions = RRDF.FinanceSignOptions;
                item.FinanceSignDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.Status = "6";
            }
            return Update(Disposal);
        }
        #endregion

    }
}
