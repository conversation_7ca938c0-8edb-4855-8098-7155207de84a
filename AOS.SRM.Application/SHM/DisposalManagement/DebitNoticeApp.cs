using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.SHM;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 扣款通知单
    /// </summary>
    public class DebitNoticeApp : BaseApp<S_DebitNotice>
    {
        #region 保存
        /// <summary>
        /// 保存功能
        /// </summary>
        /// <param name="DebitNotice"></param>
        /// <param name="CompanyCode"></param>
        /// <param name="UserName"></param>
        /// <param name="userCode"></param>
        /// <returns></returns>
        public S_DebitNotice InsertDebitNotice(S_DebitNotice DebitNotice, string CompanyCode, string UserName, string userCode)
        {
            DebitNotice.CompanyCode = CompanyCode;
            //  1代表未下达给供应商
            DebitNotice.Status = "1";
            DebitNotice.HandlingUserName = UserName;
            DebitNotice.HandlingDate = DateTime.Now.ToString("yyyy-MM-dd");
            DebitNotice.CUser = userCode;
            return Insert(DebitNotice);
        }
        #endregion

        #region 供应商回复
        /// <summary>
        /// 供应商意见提交
        /// </summary>
        /// <param name="DebitNotice"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int SupplierReply(S_DebitNotice DebitNotice, string[] Id, string UserName)
        {
            List<S_DebitNotice> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.SupplyUserName = UserName;
                item.SupplyDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.SupplyOptions = DebitNotice.SupplyOptions;
                item.Status = "2";
            }
            return Update(Disposal);
        }
        #endregion

        #region 发出部门审核(修改功能)
        /// <summary>
        /// 发出部门审核
        /// </summary>
        /// <param name="DebitNotice"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int RepDeptOptions(S_DebitNotice DebitNotice, string[] Id, string UserName)
        {

            List<S_DebitNotice> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.Status = "3";
                item.RelDeptUserName = UserName;
                item.RelDeptDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.RelDeptOptions = DebitNotice.RelDeptOptions;
                if (item.DisposalMoney <= 20000)
                {
                    item.Status = "4";
                    item.GMOUserName = "System";
                    item.GMODate = DateTime.Now.ToString("yyyy-MM-dd");
                    item.GMOptions = "系统默认";
                }
            }
            return Update(Disposal);
        }
        #endregion

        #region 总经理审核(修改功能)
        /// <summary>
        /// 总经理审核
        /// </summary>
        /// <param name="DebitNotice"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int GMOOptions(S_DebitNotice DebitNotice, string[] Id, string UserName)
        {
            List<S_DebitNotice> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.Status = "4";
                item.GMOUserName = UserName;
                item.GMODate = DateTime.Now.ToString("yyyy-MM-dd");
                item.GMOptions = DebitNotice.GMOptions;
            }
            return Update(Disposal);
        }
        #endregion

        #region 财务审核(修改功能)
        /// <summary>
        /// 财务审核
        /// </summary>
        /// <param name="DebitNotice"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int FinanceOptions(S_DebitNotice DebitNotice, string[] Id, string UserName)
        {
            List<S_DebitNotice> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.FinanceUserName = UserName;
                item.FinanceOptions = DebitNotice.FinanceOptions;
                item.FinanceDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.Status = "5";
            }
            return Update(Disposal);
        }
        #endregion

        #region 查询扣款通知单列表
        /// <summary>
        /// 查询扣款通知单列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="DisposalOrder"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<S_DebitNotice> GetDebitNotice(Pagination page, string SupplyCode, string DisposalOrder, string Status, DateTime StartTime, DateTime EndTime)
        {
            var query = this.DbContext.Queryable<S_DebitNotice>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(DisposalOrder) || t.DisposalOrder.Contains(DisposalOrder))
                && t.IsDelete == false);
            
            page.Total = query.Count();
            var itemPageData = new List<S_DebitNotice>();
            itemPageData = query.ToPageList(page.PageNumber, page.PageSize)
                .OrderByDescending(t => t.CTime).ToList();
            return itemPageData;
        }
        #endregion

        #region 扣款通知单删除
        /// <summary>
        /// 扣款通知单删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public int DeleteDebitNotice(string[] ids)
        {
            List<S_DebitNotice> DebitNotice = GetListByKeys(ids);
            return DeleteWithTran(DebitNotice);
        }
        #endregion

    }

}
