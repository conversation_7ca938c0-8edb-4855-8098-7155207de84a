using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.SHM;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 不合格处置单
    /// </summary>
    public class UnqualifiedNoticeApp : BaseApp<S_UnqualifiedDisposal>
    {
        #region 保存
        /// <summary>
        /// 保存功能
        /// </summary>
        /// <param name="UnqualifiedNotice"></param>
        /// <param name="userCode"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public S_UnqualifiedDisposal InsertUnqualifiedNotice(S_UnqualifiedDisposal UnqualifiedNotice,string userCode,string UserName)
        {
            UnqualifiedNotice.CompanyCode = "2002";
            UnqualifiedNotice.Status = "1";
            UnqualifiedNotice.HandlingUserName = UserName;
            UnqualifiedNotice.HandlingDate = DateTime.Now.ToString("yyyy-MM-dd");
            UnqualifiedNotice.CUser = userCode;
            return Insert(UnqualifiedNotice);
        }
        #endregion

        #region 供应商意见提交(修改功能)
        /// <summary>
        /// 供应商意见提交
        /// </summary>
        /// <param name="UnqualifiedNotice"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertSupplyOptions(S_UnqualifiedDisposal UnqualifiedNotice, string[] Id, string UserName)
        {
            List<S_UnqualifiedDisposal> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.SupplyUserName = UserName;
                item.SupplyDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.SupplyOptions = UnqualifiedNotice.SupplyOptions;
                item.Status = "2";
            }
            return Update(Disposal);
        }
        #endregion

        #region 发起部门审核(修改功能)
        /// <summary>
        /// 发出部门审核
        /// </summary>
        /// <param name="UnqualifiedNotice"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertRepDeptOptions(S_UnqualifiedDisposal UnqualifiedNotice, string[] Id, string UserName)
        {

            List<S_UnqualifiedDisposal> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.Status = "3";
                item.RelDeptUserName = UserName;
                item.RelDeptDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.RelDeptOptions = UnqualifiedNotice.RelDeptOptions;
                if (item.DisposalMoney <= 20000)
                {
                    item.Status = "4";
                    item.GMOUserName = "System";
                    item.GMODate = DateTime.Now.ToString("yyyy-MM-dd");
                    item.GMOptions = "系统默认";
                }
            }
            return Update(Disposal);
        }
        #endregion

        #region 总经理审核(修改功能)
        /// <summary>
        /// 总经理审核
        /// </summary>
        /// <param name="UnqualifiedNotice"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertGMOOptions(S_UnqualifiedDisposal UnqualifiedNotice, string[] Id, string UserName)
        {
            List<S_UnqualifiedDisposal> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.Status = "4";
                item.GMOUserName = UserName;
                item.GMODate = DateTime.Now.ToString("yyyy-MM-dd");
                item.GMOptions = UnqualifiedNotice.GMOptions;
            }
            return Update(Disposal);
        }
        #endregion

        #region 财务审核(修改功能)
        /// <summary>
        /// 财务审核
        /// </summary>
        /// <param name="UnqualifiedNotice"></param>
        /// <param name="Id"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int InsertFinanceOptions(S_UnqualifiedDisposal UnqualifiedNotice, string[] Id, string UserName)
        {
            List<S_UnqualifiedDisposal> Disposal = GetListByKeys(Id);
            foreach (var item in Disposal)
            {
                item.FinanceUserName = UserName;
                item.FinanceOptions = UnqualifiedNotice.FinanceOptions;
                item.FinanceDate = DateTime.Now.ToString("yyyy-MM-dd");
                item.Status = "5";
            }
            return Update(Disposal);
        }
        #endregion

        #region 查询不合格处置单列表
        /// <summary>
        /// 查询不合格处置单列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="DisposalOrder"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<S_UnqualifiedDisposal> GetUnqualifiedNotice(Pagination page, string SupplyCode, string DisposalOrder, string Status, DateTime StartTime, DateTime EndTime)
        {
            var query = this.DbContext.Queryable<S_UnqualifiedDisposal>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(DisposalOrder) || t.DisposalOrder.Contains(DisposalOrder))
                && t.IsDelete == false);
            page.Total = query.Count();
            var itemPageData = new List<S_UnqualifiedDisposal>();
            itemPageData = query.ToPageList(page.PageNumber, page.PageSize)
                .OrderByDescending(t => t.CTime).ToList();
            return itemPageData;
        }
        #endregion

        #region 不合格处置单删除
        /// <summary>
        /// 不合格处置单删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public int DeleteUnqualifiedNotice(string[] ids)
        {
            List<S_UnqualifiedDisposal> UnqualifiedNotice = GetListByKeys(ids);
            return DeleteWithTran(UnqualifiedNotice);
        }
        #endregion

    }

}
