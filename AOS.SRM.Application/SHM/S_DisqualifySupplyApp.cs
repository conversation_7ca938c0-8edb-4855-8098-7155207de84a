using System;
using System.Collections.Generic;
using System.Linq;
using AOS.SRM.Application.Base;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 
    /// </summary>
    public class S_DisqualifySupplyApp : BaseApp<S_DisqualifySupply>
    {
        Sys_MailApp _mailApp = new Sys_MailApp();
        Sys_UserApp _userApp = new Sys_UserApp();
        Sys_DictionaryApp _dicApp = new Sys_DictionaryApp();
        SupplierAuditApp _auditApp = new SupplierAuditApp();
        SupplierInfoApp _supplierInfoApp = new SupplierInfoApp();

        #region 新增
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool AddEntity(S_DisqualifySupply entity, Sys_User _currentUser, out string msg)
        {
            var flag = true;

            try
            {
                msg = string.Empty;
                List<Sys_Mail> mailList = new List<Sys_Mail>();
                Sys_Mail mail = new Sys_Mail();

                entity.Id = Guid.NewGuid().ToString();
                entity.CUser = _currentUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.IsDelete = false;
                entity.Status = "已提交";

                //获取审批节点
                var nextSeq =  1;
                var nextDicEntity = _dicApp.GetFirstEntity(t => t.TypeCode == "CancelSupply" && t.EnumKey == nextSeq);
                if (nextDicEntity != null)
                {
                    #region 更新单据信息
                    if (nextDicEntity.EnumValue == "申请部门")
                    {
                        nextDicEntity = _dicApp.GetDeptLeader(entity.Dept);
                        if (nextDicEntity == null)
                        {
                            msg = string.Format("单据[{0}]的{1}负责人没有配置", entity.DocNum, entity.Dept);
                            return false;
                        }
                    }
                    entity.AuditSequence = nextSeq;
                    entity.NextCheckUserCode = nextDicEntity.EnumValue1;
                    entity.NextCheckUserName = nextDicEntity.EnumValue2;
                    #endregion

                    #region 邮件通知下一个审批人

                    var nextUser = _userApp.GetFirstEntity(t => t.LoginAccount == entity.NextCheckUserCode);
                    mail = new Sys_Mail()
                    {
                        MailID = Guid.NewGuid().ToString(),
                        UserID = entity.NextCheckUserCode,
                        UserName = entity.NextCheckUserName,
                        MessageTypeID = "1001",
                        MailSubject = "取消供货资格审核",
                        MailBody = "您好，您有取消供货资格申请单需要审核，请前往西子SRM系统确认",
                        ReceiverMail = nextUser.Email,
                        IsDelete = false,
                        CUser = "admin",
                        CTime = DateTime.Now,
                        SenderDisplayName = "System",
                        AutoSend = false
                    };
                    mailList.Add(mail);
                    #endregion
                }

                this.DbContext.Ado.BeginTran();
                _mailApp.DbContext = this.DbContext;

                this.Insert(entity);
                _mailApp.Insert(mailList);

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 审核
        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="auditStatus"> 1:同意 2：驳回</param>
        /// <param name="auditOpinions"></param>
        /// <param name="_currentUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool Check(string[] ids, string auditStatus, string auditOpinions, Sys_User _currentUser, out string msg)
        {
            msg = string.Empty;
            var auditList = new List<S_SupplierAudit>();
            Sys_Mail mail = new Sys_Mail();
            List<Sys_Mail> mailList = new List<Sys_Mail>();
            List<Sys_User> userList = new List<Sys_User>();
            List<S_SupplierInfo> supplierInfoList = new List<S_SupplierInfo>();

            try
            {
                var entitys = this.GetList(x => ids.Contains(x.Id))?.ToList();
                foreach (var entity in entitys)
                {
                    //判断是否当前审批人
                    if (_currentUser.LoginAccount != entity.NextCheckUserCode)
                    {
                        msg = string.Format("当前审批人是：{0}，单号：{1}", entity.NextCheckUserName, entity.DocNum);
                        return false;
                    }

                    #region 插入审批记录
                    //插入审批记录
                    var audit = new S_SupplierAudit()
                    {
                        Id = Guid.NewGuid().ToString(),
                        CheckType = "取消供货资格",
                        DocId = entity.Id,
                        DocNum = entity.DocNum,
                        SupplierCode = entity.SupplierCode,
                        SupplierName = entity.SupplierName,
                        AuditSequence = _auditApp.GetMaxSequence(entity.Id) + 1,
                        AuditStatus = auditStatus,
                        AuditOpinions = auditOpinions,
                        AuditUser = _currentUser.UserName,
                        AuditTime = DateTime.Now,
                        CTime = DateTime.Now,
                        CUser = _currentUser.LoginAccount
                    };
                    auditList.Add(audit);
                    #endregion

                    #region 同意
                    if (auditStatus == "1") //同意
                    {
                        //获取下一个审批人
                        var nextSeq = (entity.AuditSequence == null ? 1 : entity.AuditSequence) + 1;
                        var nextDicEntity = _dicApp.GetFirstEntity(t => t.TypeCode == "CancelSupply" && t.EnumKey == nextSeq);
                        if (nextDicEntity != null)
                        {
                            #region 更新单据信息
                            if (nextDicEntity.EnumValue == "申请部门")
                            {
                                nextDicEntity = _dicApp.GetDeptLeader(entity.Dept);
                                if (nextDicEntity == null)
                                {
                                    msg = string.Format("单据[{0}]的{1}负责人没有配置", entity.DocNum, entity.Dept);
                                    return false;
                                }
                            }

                            entity.AuditSequence = nextSeq;
                            entity.NextCheckUserCode = nextDicEntity.EnumValue1;
                            entity.NextCheckUserName = nextDicEntity.EnumValue2;
                            entity.Status = "审批中";
                            entity.MUser = _currentUser.LoginAccount;
                            entity.MTime = DateTime.Now;
                            #endregion

                            #region 邮件通知下一个审批人

                            var nextUser = _userApp.GetFirstEntity(t => t.LoginAccount == entity.NextCheckUserCode);
                            mail = new Sys_Mail()
                            {
                                MailID = Guid.NewGuid().ToString(),
                                UserID = entity.NextCheckUserCode,
                                UserName = entity.NextCheckUserName,
                                MessageTypeID = "1001",
                                MailSubject = "取消供货申请审核",
                                MailBody = "您好，您有取消供货资格申请单需要审核，请前往西子SRM系统进行审批",
                                ReceiverMail = nextUser.Email,
                                IsDelete = false,
                                CUser = "admin",
                                CTime = DateTime.Now,
                                SenderDisplayName = "System",
                                AutoSend = false
                            };
                            mailList.Add(mail);
                            #endregion
                        }
                        else //审批结束，同步接口
                        {
                            #region 更新单据信息
                            entity.NextCheckUserCode = "";
                            entity.NextCheckUserName = "";
                            entity.AuditSequence = null;//审批结束，
                            entity.Status = "已通过";
                            entity.MUser = _currentUser.LoginAccount;
                            entity.MTime = DateTime.Now;
                            #endregion

                            #region 同步供应商信息接口
                            var supplierInfo = _supplierInfoApp.GetFirstEntity(t => t.SupplyerCode == entity.SupplierCode);
                            supplierInfo.Status = "2";//已取消
                            supplierInfo.MUser = _currentUser.LoginAccount;
                            supplierInfo.MTime = DateTime.Now;
                            supplierInfoList.Add(supplierInfo);
                            #endregion
                        }
                    }
                    #endregion

                    #region 驳回
                    else //驳回
                    {
                        #region 更新单据信息
                        entity.NextCheckUserCode = "";
                        entity.NextCheckUserName = "";
                        entity.AuditSequence = null;//审批结束，
                        entity.Status = "已驳回";
                        entity.MUser = _currentUser.LoginAccount;
                        entity.MTime = DateTime.Now;
                        #endregion

                        #region 邮件通知申请人
                        var cUser = _userApp.GetFirstEntity(t => t.LoginAccount == entity.CUser);
                        mail = new Sys_Mail()
                        {
                            MailID = Guid.NewGuid().ToString(),
                            UserID = entity.NextCheckUserCode,
                            UserName = entity.NextCheckUserName,
                            MessageTypeID = "1001",//固定值
                            MailSubject = "取消供货申请审核",
                            MailBody = "您好，取消供货资格申请单[" + entity.DocNum + "]被驳回，请前往西子SRM系统确认",
                            ReceiverMail = cUser.Email,
                            IsDelete = false,
                            CUser = "admin",
                            CTime = DateTime.Now,
                            SenderDisplayName = "System",
                            AutoSend = false
                        };
                        mailList.Add(mail);
                        #endregion
                    }
                    #endregion
                }

                DbContext.Ado.BeginTran();//开始事务
                _auditApp.DbContext = this.DbContext;
                _mailApp.DbContext = this.DbContext;

                base.Update(entitys);
                _auditApp.Insert(auditList);
                _mailApp.Insert(mailList);
                if (supplierInfoList.Count > 0)
                {
                    _supplierInfoApp.DoPost(supplierInfoList, out msg);
                    _supplierInfoApp.Update(supplierInfoList);
                }

                DbContext.Ado.CommitTran();//提交事务
            }
            catch (Exception ex)
            {
                DbContext.RollbackTran();//回滚事务
                msg = ex.Message;
                return false;
            }
            return true;

        }
        #endregion

    }
}
