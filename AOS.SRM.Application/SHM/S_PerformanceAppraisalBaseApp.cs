using AOS.SRM.Entity.SHM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 绩效考核基础信息应用层
    /// </summary>
    public class S_PerformanceAppraisalBaseApp : BaseApp<S_PerformanceAppraisalBase>
    {
        #region 导入

        /// <summary>
        /// 
        /// </summary>
        /// <param name="excelList"></param>
        /// <param name="opUser"></param>
        /// <returns></returns>
        public bool ImprotExcelToBaseData(List<S_PerformanceAppraisalBase> excelList, string opUser)
        {


            var flag = 0;
            var updateList = new List<S_PerformanceAppraisalBase>();
            var insertList = new List<S_PerformanceAppraisalBase>();
            if (!ValidateCheck(excelList))
            {
                return false;
            }

            foreach (var item in excelList)
            {
                var baseEntity = base.GetFirstEntity(t => t.AssessYear == item.AssessYear && t.SupplierCode == item.SupplierCode);
                if (baseEntity==null)
                {
                    item.Id = Guid.NewGuid().ToString();
                    item.CTime = DateTime.Now;
                    item.CUser = opUser;
                    insertList.Add(item);
                }
                else {
                    baseEntity.SupplierName = item.SupplierName;
                    baseEntity.SupplyProducts = item.SupplyProducts;
                    baseEntity.Category = item.Category;
                    baseEntity.AssessMode = item.AssessMode;
                    baseEntity.Remark = item.Remark;
                    baseEntity.MTime = DateTime.Now;
                    baseEntity.MUser = opUser;
                    updateList.Add(baseEntity);
                }
            }
            try
            {
                this.DbContext.Ado.BeginTran();
                if (insertList.Count() > 0)
                {
                    flag = this.Insert(insertList);
                }
                if (updateList.Count() > 0)
                {
                    flag += this.Update(updateList);
                }
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                    this.DbContext.Ado.RollbackTran();
                throw ex;
            }
            return flag > 0;
        }

        /// <summary>
        /// 导入校验规则
        /// </summary>
        /// <param name="excelList"></param>
        /// <returns></returns>
        public bool ValidateCheck(List<S_PerformanceAppraisalBase> excelList)
        {
            var flag = true;

            foreach (var item in excelList)
            {
                if (string.IsNullOrEmpty(item.SupplierCode))
                    throw new Exception("供应商编码不能为空");
                if (string.IsNullOrEmpty(item.SupplierName))
                    throw new Exception("供应商名称不能为空");
                if (string.IsNullOrWhiteSpace(item.AssessMode))
                    throw new Exception("考核方式不能为空");
            }
            return flag;

        }

        #endregion
    }
}
