using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Application.SHM.ViewModel
{
    /// <summary>
    /// 选择供应商处置单据
    /// </summary>
    public class V_SHM_SupplierDisposalnfo
    {
        /// <summary>
        /// 单据类型
        /// </summary>
        public string DocType { get; set; }
        /// <summary>
        /// 单据名称
        /// </summary>
        public string DocName { get; set; }
        /// <summary>
        /// 处置单据的Id
        /// </summary>
        public string DisposalId { get; set; }
        /// <summary>
        /// 处置单号
        /// </summary>
        public string DisposalNo { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }
        /// <summary>
        /// 发起部门
        /// </summary>
        public string InitiatingDept { get; set; }
        /// <summary>
        /// 发起人
        /// </summary>
        public string Initiatinger { get; set; }
        /// <summary>
        /// 发起日期
        /// </summary>
        public DateTime? DisposalDate { get; set; }
        /// <summary>
        /// 问题来源
        /// </summary>
        public string ProblemSource { get; set; }
        /// <summary>
        /// 处置金额
        /// </summary>
        public decimal? DisposalMoney { get; set; }
        /// <summary>
        /// 问题描述
        /// </summary>
        public string ProblemDescription { get; set; }
    }
}
