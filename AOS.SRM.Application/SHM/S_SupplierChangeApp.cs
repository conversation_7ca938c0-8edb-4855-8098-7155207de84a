using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using SqlSugar;

namespace AOS.SRM.Application.SHM
{

    /// <summary>
    /// 供应商信息变更应用层
    /// </summary>
    public class S_SupplierChangeApp : BaseApp<S_SupplierChange>
    {

        SupplierAuditApp _auditApp = new SupplierAuditApp();
        Sys_DictionaryApp _dicApp = new Sys_DictionaryApp();
        Sys_MailApp _mailApp = new Sys_MailApp();
        Sys_UserApp _userApp = new Sys_UserApp();
        SupplierInfoApp _supplierInfoApp = new SupplierInfoApp();
        MD_AttachmentManagementApp _AttachmentManagementApp = new MD_AttachmentManagementApp();

        #region 供应商信息变更申请
        /// <summary>
        /// 需要生成两条数据，通过删除旧数据，驳回删除新数据
        /// 附件也是
        /// </summary>
        /// <returns></returns>
        public bool SupplierInfoChangeRequest(S_SupplierChange entity, string[] filesId, string user)
        {
            try
            {
                Sys_Mail mail = new Sys_Mail();

                this.DbContext.Ado.BeginTran();
                _AttachmentManagementApp.DbContext = this.DbContext;
                _mailApp.DbContext = this.DbContext;

                entity.CTime = DateTime.Now;
                entity.CUser = user;
                entity.Status = "0";
                entity.NextCheckUserCode = entity.BuyerCode;
                entity.NextCheckUserName = entity.BuyerName;
                entity.AuditSequence = 1;
                base.Insert(entity);
                if (filesId.Length > 0)//如果有附件就更新
                {
                    //需要将新的附件更新相关单号
                    var newFiles = _AttachmentManagementApp.GetList(x => filesId.Contains(x.FileId))?.ToList();
                    newFiles.ForEach(file =>
                    {
                        file.ReferenceDocNumber = entity.Id;
                    });
                    _AttachmentManagementApp.Update(newFiles);

                }
                #region 邮件通知采购员
                var nextUser = _userApp.GetFirstEntity(t => t.LoginAccount == entity.NextCheckUserCode);
                mail = new Sys_Mail()
                {
                    MailID = Guid.NewGuid().ToString(),
                    UserID = entity.NextCheckUserCode,
                    UserName = entity.NextCheckUserName,
                    MessageTypeID = "1001",
                    MailSubject = "供应商变更申请",
                    MailBody = "您好，供应商[" + entity.SupplyerCode + " " + entity.SupplyerName1 + "]信息变更需要您审批，请前往西子SRM系统的供应商变更申请模块进行审批",
                    ReceiverMail = nextUser.Email,
                    IsDelete = false,
                    CUser = "admin",
                    CTime = DateTime.Now,
                    SenderDisplayName = "System",
                    AutoSend = false
                };
                _mailApp.Insert(mail);
                #endregion

                this.DbContext.Ado.CommitTran();
                return true;
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                {
                    this.DbContext.Ado.RollbackTran();
                }
                throw ex;
            }
        }
        #endregion

        #region 供应商变更申请查询(分页)
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="supplierName"></param>
        /// <param name="supplierType"></param>
        /// <param name="dateTimes"></param>
        /// <param name="inOrOut"></param>
        /// <param name="states"></param>
        /// <param name="buyerCode">所属采购员</param>
        /// <param name="userInfo"></param>
        /// <returns></returns>
        public List<S_SupplierChange> GetPageListForChange(Pagination page, string keyword, string supplierName, string supplierType, DateTime[] dateTimes, string inOrOut, string states, string buyerCode, Sys_User userInfo)
        {
            var itemsData = GetDetailList(keyword);
            dateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            var fromDate = dateTimes[0];
            var toDate = dateTimes[1];
            itemsData = itemsData.Where(x => x.CTime >= fromDate && x.CTime <= toDate
            && (string.IsNullOrEmpty(supplierName) || x.SupplyerName1.Contains(supplierName))
             && (string.IsNullOrEmpty(supplierType) || x.SupplierCategory == supplierType)
            && (string.IsNullOrEmpty(inOrOut) || x.InOrOut == inOrOut)
            && (string.IsNullOrEmpty(states) || x.Status == states)
            && (string.IsNullOrEmpty(buyerCode) || x.BuyerCode == buyerCode)
            );

            //return base.GetPageListBySelf(page, itemsData?.ToList());
            List<S_SupplierChange> listNew = new List<S_SupplierChange>();
            return listNew;
        }
        #region 获取明细
        /// <summary>
        /// 获取未删除明细
        ///  用于分页和导出的明细
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        public ISugarQueryable<S_SupplierChange> GetDetailList(string keyword, string sort = "CTime desc")
        {
            var query = this.GetList(x =>
            (string.IsNullOrEmpty(keyword)
            || x.SupplyerCode.Contains(keyword)
            || x.SupplyerName1.Contains(keyword)
            || x.SupplyerName.Contains(keyword)
            || x.AccountGroupDisc.Contains(keyword)
            || x.Address.Contains(keyword)
            || x.CUser.Contains(keyword)
            || x.MUser.Contains(keyword)
            || x.Phone.Contains(keyword)
            || x.Remark.Contains(keyword)
            )
            ).OrderBy(sort);
            return query;
        }
        #endregion
        #endregion

        #region 供应商信息变更审核(同意)
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="user"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool SupplierChangeApproved(string[] ids, Sys_User user, out string msg)
        {
            msg = string.Empty;
            var auditList = new List<S_SupplierAudit>();
            Sys_Mail mail = new Sys_Mail();
            List<Sys_Mail> mailList = new List<Sys_Mail>();
            List<S_SupplierInfo> supplierInfoList = new List<S_SupplierInfo>();
            List<Sys_User> userList = new List<Sys_User>();

            try
            {
                var entitys = this.GetList(x => ids.Contains(x.Id))?.ToList();
                foreach (var entity in entitys)
                {
                    //判断是否当前审批人
                    if (user.LoginAccount != entity.NextCheckUserCode)
                    {
                        msg = string.Format("当前审批人是：{1}，供应商：{0}", entity.SupplyerName1, entity.NextCheckUserName);
                        return false;
                    }

                    //插入审批记录
                    var audit = new S_SupplierAudit()
                    {
                        Id = Guid.NewGuid().ToString(),
                        DocId = entity.Id,
                        SupplierName = entity.SupplyerName1,
                        AuditSequence = _auditApp.GetMaxSequence(entity.Id) + 1,
                        AuditStatus = "1",
                        AuditUser = user.UserName,
                        AuditTime = DateTime.Now,
                        CTime = DateTime.Now,
                        CUser = user.LoginAccount
                    };
                    auditList.Add(audit);

                    //获取当前审批人的节点序号
                    var nextSeq = (entity.AuditSequence == null ? 1 : entity.AuditSequence) + 1;
                    var nextDicEntity = _dicApp.GetFirstEntity(t => t.TypeCode == "SupplierChangeAudit" && t.EnumKey == nextSeq);
                    if (nextDicEntity != null)
                    {
                        entity.AuditSequence = nextSeq;
                        entity.NextCheckUserCode = nextDicEntity.EnumValue;
                        entity.NextCheckUserName = nextDicEntity.EnumValue1;
                        entity.Status = "1";
                        entity.MUser = user.LoginAccount;
                        entity.MTime = DateTime.Now;

                        //获取下一个审批人的邮箱
                        var nextUser = _userApp.GetFirstEntity(t => t.LoginAccount == entity.NextCheckUserCode);

                        #region 邮件通知下一个审批人
                        mail = new Sys_Mail()
                        {
                            MailID = Guid.NewGuid().ToString(),
                            UserID = entity.NextCheckUserCode,
                            UserName = entity.NextCheckUserName,
                            MessageTypeID = "1001",
                            MailSubject = "供应商变更申请",
                            MailBody = "您好，供应商[" + entity.SupplyerCode + " " + entity.SupplyerName1 + "]信息变更需要您审批，请前往西子SRM系统的供应商变更申请模块进行审批",
                            ReceiverMail = nextUser.Email,
                            IsDelete = false,
                            CUser = "admin",
                            CTime = DateTime.Now,
                            SenderDisplayName = "System",
                            AutoSend = false
                        };
                        mailList.Add(mail);
                        #endregion
                    }
                    else
                    {
                        entity.NextCheckUserCode = "";
                        entity.NextCheckUserName = "";
                        entity.AuditSequence = null;//审批结束，
                        entity.Status = "2";
                        entity.MUser = user.LoginAccount;
                        entity.MTime = DateTime.Now;

                        #region 更新供应商信息表
                        var supplierInfo = GetSupplierChangeInfo(entity);
                        supplierInfoList.Add(supplierInfo);

                        #endregion
                        #region 更新用户信息
                        var userInfo = _userApp.GetFirstEntity(t => t.LoginAccount == entity.SupplyerCode);
                        if (userInfo != null)
                        {
                            userInfo.UserName = entity.SupplyerName;
                            userInfo.Telphone = entity.Tel;
                            userInfo.Mobile = entity.Phone;
                            userInfo.Email = entity.EMail;
                            userInfo.SupplyCode = entity.SupplyerCode;
                            userInfo.SupplyName = entity.SupplyerName1;
                            userList.Add(userInfo);
                        }
                        #endregion
                    }
                    entity.MUser = user.LoginAccount;
                    entity.MTime = DateTime.Now;
                }

                DbContext.Ado.BeginTran();//开始事务
                _auditApp.DbContext = this.DbContext;
                _mailApp.DbContext = this.DbContext;
                _userApp.DbContext = this.DbContext;

                base.Update(entitys);
                _auditApp.Insert(auditList);
                _mailApp.Insert(mailList);
                if (supplierInfoList.Count > 0)
                {
                    _supplierInfoApp.Update(supplierInfoList);
                }
                _userApp.Update(userList);

                DbContext.Ado.CommitTran();//提交事务

                if (supplierInfoList.Count > 0)
                {
                   bool bResult =_supplierInfoApp.DoPost(supplierInfoList, out msg);
                    if (!bResult)
                    {
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                DbContext.RollbackTran();//回滚事务
                msg = ex.Message;
                return false;
            }
            return true;

        }
        #endregion

        #region 供应商变更申请驳回
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="rejectReason"></param>
        /// <param name="user"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool SupplierChangeReject(string[] ids, string rejectReason, Sys_User user, out string msg)
        {
            msg = string.Empty;
            Sys_Mail mail = new Sys_Mail();
            List<Sys_Mail> mailList = new List<Sys_Mail>();
            List<S_SupplierAudit> auditList = new List<S_SupplierAudit>();
            try
            {
                var entitys = this.GetList(x => ids.Contains(x.Id))?.ToList();
                entitys.ForEach(entity =>
                {
                    entity.Status = "3";//已驳回
                    entity.Remark = rejectReason;
                    entity.MUser = user.LoginAccount;
                    entity.MTime = DateTime.Now;

                    //插入审批记录
                    var audit = new S_SupplierAudit()
                    {
                        Id = Guid.NewGuid().ToString(),
                        DocId = entity.Id,
                        SupplierName = entity.SupplyerName1,
                        AuditSequence = _auditApp.GetMaxSequence(entity.Id) + 1,
                        AuditStatus = "2",
                        AuditOpinions = rejectReason,
                        AuditUser = user.UserName,
                        AuditTime = DateTime.Now,
                        CTime = DateTime.Now,
                        CUser = user.LoginAccount,
                        IsDelete = false
                    };
                    auditList.Add(audit);

                    #region 邮件通知供应商
                    mail = new Sys_Mail()
                    {
                        MailID = Guid.NewGuid().ToString(),
                        UserID = entity.SupplyerCode,
                        UserName = entity.SupplyerName1,
                        MessageTypeID = "1001",
                        MailSubject = "供应商变更申请驳回",
                        MailBody = string.Format("您好，您{0}申请的供应商信息变更申请被驳回，驳回原因：{1}", entity.CTime, rejectReason),
                        ReceiverMail = entity.EMail,
                        IsDelete = false,
                        CUser = "System",
                        CTime = DateTime.Now,
                        SenderDisplayName = "System",
                        AutoSend = false
                    };
                    mailList.Add(mail);
                    #endregion
                });

                DbContext.Ado.BeginTran();//开始事务

                _auditApp.DbContext = this.DbContext;
                _mailApp.DbContext = this.DbContext;

                base.Update(entitys);
                _auditApp.Insert(auditList);
                _mailApp.Insert(mailList);

                DbContext.Ado.CommitTran();//提交事务
            }
            catch (Exception ex)
            {
                DbContext.RollbackTran();//回滚事务
                msg = ex.Message;
                return false;
            }
            return true;

        }

        #endregion

        #region 供应商变更申请修改
        /// <summary>
        /// 附件也是
        /// </summary>
        /// <returns></returns>
        public bool SupplierChangeUpdate(S_SupplierChange entity, string[] filesId, string user)
        {
            var flag = true;

            var oldInfo = this.GetFirstEntity(x => x.Id == entity.Id);
            entity.MUser = user;
            entity.MTime = DateTime.Now;
            entity.Status = oldInfo.Status;
            entity.NextCheckUserCode = oldInfo.NextCheckUserCode;
            entity.NextCheckUserName = oldInfo.NextCheckUserName;
            entity.AuditSequence = oldInfo.AuditSequence;
            try
            {
                this.DbContext.Ado.BeginTran();
                _AttachmentManagementApp.DbContext = this.DbContext;

                this.Update(entity);
                if (filesId.Length > 0)//如果有附件就更新
                {
                    //需要将新的附件更新相关单号
                    var newFiles = _AttachmentManagementApp.GetList(x => filesId.Contains(x.FileId))?.ToList();
                    newFiles.ForEach(file =>
                    {
                        file.ReferenceDocNumber = entity.Id;
                    });
                    _AttachmentManagementApp.Update(newFiles);

                }
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                {
                    this.DbContext.Ado.RollbackTran();
                }
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 供应商变更申请删除
        /// <summary>
        /// 供应商变更申请删除
        /// </summary>
        /// <param name="ids">主键</param>
        /// <param name="userCode">登陆人编码</param>
        /// <returns></returns>
        public bool SuppplierChangeDelete(string[] ids,string userCode)
        {
            var flag = true;
            try
            {
                var attachList = _AttachmentManagementApp.GetList(t => ids.Contains(t.ReferenceDocNumber))?.ToList();
                this.DbContext.Ado.BeginTran();
                _AttachmentManagementApp.DbContext = this.DbContext;

                this.DeleteByKeys(ids, userCode);
                if (attachList != null && attachList.Count > 0)
                {
                    _AttachmentManagementApp.Delete(attachList, userCode);
                }
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                if (this.DbContext.Ado.IsAnyTran())
                {
                    this.DbContext.Ado.RollbackTran();
                }
                throw ex;
            }
            return flag;
        }
        #endregion

        #region 公共方法

        #region 供应商信息变更修改赋值
        /// <summary>
        /// 供应商信息变更修改赋值
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public S_SupplierInfo GetSupplierChangeInfo(S_SupplierChange entity)
        {
            var supplierInfo = _supplierInfoApp.GetFirstEntity(t => t.SupplyerCode == entity.SupplyerCode);
            supplierInfo.SupplyerName1 = entity.SupplyerName1;
            supplierInfo.SupplyerName = entity.SupplyerName;
            supplierInfo.CompanyCode = entity.CompanyCode;
            supplierInfo.AccountGroupCode = entity.AccountGroupCode;
            supplierInfo.AccountGroupDisc = entity.AccountGroupDisc;
            supplierInfo.Nature = entity.Nature;
            supplierInfo.BWO = entity.BWO;
            supplierInfo.Importance = entity.Importance;
            supplierInfo.UnitSize = entity.UnitSize;
            supplierInfo.Industry = entity.Industry;
            supplierInfo.IsMarket = entity.IsMarket;
            supplierInfo.CountryCode = entity.CountryCode;
            supplierInfo.CountryName = entity.CountryName;
            supplierInfo.Province = entity.Province;
            supplierInfo.City = entity.City;
            supplierInfo.Address = entity.Address;
            supplierInfo.PostalCode = entity.PostalCode;
            supplierInfo.Tel = entity.Tel;
            supplierInfo.Phone = entity.Phone;
            supplierInfo.Fax = entity.Fax;
            supplierInfo.EMail = entity.EMail;
            supplierInfo.Homepage = entity.Homepage;
            supplierInfo.LegalUser = entity.LegalUser;
            supplierInfo.LegalUserCardId = entity.LegalUserCardId;
            supplierInfo.BankAccount = entity.BankAccount;
            supplierInfo.BankLicenseNo = entity.BankLicenseNo;
            supplierInfo.BankDeposit = entity.BankDeposit;
            supplierInfo.BusinessLicenseNo = entity.BusinessLicenseNo;
            supplierInfo.TaxNumber = entity.TaxNumber;
            supplierInfo.DateEstablishment = entity.DateEstablishment;
            supplierInfo.BusinessScope = entity.BusinessScope;
            supplierInfo.IsVATInvoice = entity.IsVATInvoice;
            supplierInfo.TaxRate = entity.TaxRate;
            supplierInfo.AnnualSales = entity.AnnualSales;
            supplierInfo.FactoryArea = entity.FactoryArea;
            supplierInfo.ProcductType = entity.ProcductType;
            supplierInfo.IsCertificate = entity.IsCertificate;
            supplierInfo.PeopleNumber = entity.PeopleNumber;
            supplierInfo.SupplierCategory = entity.SupplierCategory;
            supplierInfo.POrganizationCode = entity.POrganizationCode;
            supplierInfo.CurrencyCode = entity.CurrencyCode;
            supplierInfo.CurrencyDisc = entity.CurrencyDisc;
            supplierInfo.PaymentTermCode = entity.PaymentTermCode;
            supplierInfo.PaymentTermDisc = entity.PaymentTermDisc;
            supplierInfo.InvoiceVerification = entity.InvoiceVerification;
            supplierInfo.DeliveryMethod = entity.DeliveryMethod;
            supplierInfo.CarNumber = entity.CarNumber;
            supplierInfo.Remark = entity.Remark;
            supplierInfo.SupplierLanguage = entity.SupplierLanguage;
            supplierInfo.BankCountryCode = entity.BankCountryCode;
            supplierInfo.ControlSubject = entity.ControlSubject;
            supplierInfo.PayMode = entity.PayMode;

            return supplierInfo;
        }
        #endregion

        #endregion
    }
}
