using AOS.Core.Http;
using AOS.SRM.Entity.FO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.FO
{
    /// <summary>
    /// 返还单
    /// </summary>
    public class I_ReturnNoApp : BaseApp<I_ReturnNote>
    {
        #region 返还单
        //修改返还单
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ReturnNote"></param>
        /// <returns></returns>
        public int UpdateReturnNo(I_ReturnNote ReturnNote)
        {
            return Update(ReturnNote);
        }
        //接收且打印
        //public int print()
        //{

        //}

        /// <summary>
        /// 修改状态为已确认（对账完成）
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="userCode"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public int UpdateStatus(string[] ids,string userCode,string userName)
        {

            List<I_ReturnNote> lstEntity = GetListByKeys(ids);

            lstEntity.ForEach(t =>
            {
                t.Status = 4;
                t.MUser = userCode;
                t.MTime = DateTime.Now;
                t.Confirmer = userName;
            });
            return UpdateWithTran(lstEntity);
        }
        //导出返还单
        /// <summary>
        /// 
        /// </summary>
        /// <param name="Status"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<I_ReturnNote> GetReturnNoData(string Status, string SupplyCode, DateTime StartTime, DateTime EndTime)
        {
            var itemsData = this.DbContext.Queryable<I_ReturnNote>().Where(t => (t.ReturnDate >= StartTime) && (t.ReturnDate < EndTime.AddDays(1))

                                                                                        && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode)))
                                                                                         .Where(t => t.IsDelete == true)
                                                                                        .ToList();
            return itemsData;
        }

        /// <summary>
        /// 返还单列表查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="ReturnNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<I_ReturnNote> GetReturnNoteList(Pagination page, string ReturnNo, string SupplyCode, DateTime StartTime, DateTime EndTime)
        {
            var query = this.DbContext.Queryable<I_ReturnNote>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(ReturnNo) || t.ReturnNo.Contains(ReturnNo))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode))
                && t.IsDelete == false);

            page.Total = query.Count();
            var itemPageData = new List<I_ReturnNote>();
            itemPageData = query.ToPageList(page.PageNumber, page.PageSize).OrderByDescending(t => t.CTime).ToList();
            return itemPageData;
        }

        /// <summary>
        /// 删除附件，删除带附件的整行数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public int DeleteFiles(string[] ids)
        {
            List<I_ReturnNote> lstReturn = GetListByKeys(ids);
            return DeleteWithTran(lstReturn);

        }



        #endregion

        /// <summary>
        /// 制作返还单
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="userCode"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        public bool CreateReturnNote(I_ReturnNote entity,string userCode,string userName)
        {
            try
            {
                entity.CUser = userCode;
                entity.Producer = userName;
                entity.Status = 0;
                Insert(entity);
                return true;
            }
            catch (Exception ex)
            {
                return false;
                throw ex;
            }

        }
    }
}
