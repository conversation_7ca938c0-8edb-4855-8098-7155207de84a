using AOS.Core.Http;
using AOS.SRM.Entity.FO;
using AOS.SRM.Entity.FO.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.FO
{
    /// <summary>
    /// 对账明细方法层
    /// </summary>
    public class I_ReconciliationDetailApp : BaseApp<I_ReconciliationDetail>
    {
        /// <summary>
        /// 获取物流订单信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="logisticsNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public List<V_FO_LogisticsOrderInfo> GetLogisticsOrderInfo(Pagination page, string logisticsNo, string SupplyCode, DateTime startTime, DateTime endTime)
        {
            var data = DbContext.Queryable<V_FO_LogisticsOrderInfo>()
                .Where(t => (string.IsNullOrEmpty(logisticsNo) || t.LogisticsNo.Contains(logisticsNo))
                && (string.IsNullOrEmpty(SupplyCode) || t.LogisticsSupplierCode.Contains(SupplyCode))
                && t.CTime >= startTime && t.CTime < endTime.AddDays(1)
                ).ToList();

            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            int total = data.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return data.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                return data.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
        }
    }
}
