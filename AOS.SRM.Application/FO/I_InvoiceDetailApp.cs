using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.FO;
using AOS.SRM.Entity.FO.ViewModel;
using AOS.SRM.Entity.SAP;
using HZ.WMS.Application.Sys;
using SqlSugar;

namespace AOS.SRM.Application.FO
{
    /// <summary>
    /// 开票申请
    /// </summary>
    public class I_InvoiceDetailApp : BaseApp<I_InvoiceDetails>
    {
        private Sys_SAPCompanyInfoApp _sap = new Sys_SAPCompanyInfoApp();

        #region 收货明细列表
        /// <summary>
        /// 查询收货明细
        /// </summary>
        /// <returns></returns>
        public List<FO_ReceivingDetails_View> GetReceivingDetails(Pagination page, string SupplyCode, string InspectionNo, string OrderNo, string MaterialCode, 
            string OrderType, DateTime StartTime, DateTime EndTime, string status, string CompanyCode, string userAccount, bool isSupplier, bool? checkFlag, 
            out bool isPosted, out string message)
        {
            //动态视图名称
            string viewName = "FO_ReceivingDetails_Standard_View";
            
            if (OrderType == "2")
            {
                viewName = "FO_ReceivingDetails_Outsourcing_View";
            }
            if (OrderType == "3")
            {
                viewName = "FO_ReceivingDetails_Rejected_View";
            }

            List<FO_ReceivingDetails_View> itemsData = new List<FO_ReceivingDetails_View>();
            if (page != null)
            {
                //执行sql
                itemsData = DbContext.Queryable<FO_ReceivingDetails_View>()
                    .AS(viewName)
                    .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                    && (string.IsNullOrEmpty(InspectionNo) || t.InspectionNo.Contains(InspectionNo))
                    && (string.IsNullOrEmpty(MaterialCode) || t.ItemCode.Contains(MaterialCode) || t.ItemName.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                    && (
                        (isSupplier == false && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode == SupplyCode))
                        || 
                        (isSupplier == true && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode == SupplyCode) && (t.LIFRE == userAccount || t.LIFRE == null || t.LIFRE == ""))
                    )
                    && (string.IsNullOrEmpty(CompanyCode) || t.WERKS == CompanyCode)
                    && (string.IsNullOrEmpty(OrderNo) || t.OrderNo.Contains(OrderNo) || t.ZTEXT.Contains(OrderNo))
                    && t.OrderType == OrderType
                    && (OrderType != "3" || checkFlag == null))
                    .OrderBy(t => t.InspectionNo)
                    .OrderBy(t => t.InspectionLine)
                    .ToPageList(page.PageNumber, page.PageSize);

                var total = DbContext.Queryable<FO_ReceivingDetails_View>()
                    .AS(viewName)
                    .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                    && (string.IsNullOrEmpty(InspectionNo) || t.InspectionNo.Contains(InspectionNo))
                    && (string.IsNullOrEmpty(MaterialCode) || t.ItemCode.Contains(MaterialCode) || t.ItemName.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                    && (
                        (isSupplier == false && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode == SupplyCode))
                        || 
                        (isSupplier == true && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode == SupplyCode) && (t.LIFRE == userAccount || t.LIFRE == null || t.LIFRE == ""))
                    )
                    && (string.IsNullOrEmpty(CompanyCode) || t.WERKS == CompanyCode)
                    && (string.IsNullOrEmpty(OrderNo) || t.OrderNo.Contains(OrderNo) || t.ZTEXT.Contains(OrderNo))
                    && t.OrderType == OrderType
                    && (OrderType != "3" || checkFlag == null))
                    .OrderBy(t => t.InspectionNo)
                    .OrderBy(t => t.InspectionLine)
                    .Count();
                page.Total = total;
            }
            else
            {
                //执行sql
                itemsData = DbContext.Queryable<FO_ReceivingDetails_View>()
                    .AS(viewName)
                    .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                                                       && (string.IsNullOrEmpty(InspectionNo) || t.InspectionNo.Contains(InspectionNo))
                                                       && (string.IsNullOrEmpty(MaterialCode) || t.ItemCode.Contains(MaterialCode) || t.ItemName.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                                                       && (
                                                           (isSupplier == false && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode == SupplyCode))
                                                           || 
                                                           (isSupplier == true && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode == SupplyCode) && (t.LIFRE == userAccount || t.LIFRE == null || t.LIFRE == ""))
                                                       )
                                                       && (string.IsNullOrEmpty(CompanyCode) || t.WERKS == CompanyCode)
                                                       && (string.IsNullOrEmpty(OrderNo) || t.OrderNo.Contains(OrderNo) || t.ZTEXT.Contains(OrderNo))
                                                       && t.OrderType == OrderType
                                                       && (OrderType != "3" || checkFlag == null))
                    .OrderBy(t => t.InspectionNo)
                    .OrderBy(t => t.InspectionLine).ToList();
            }

            foreach (var item in itemsData)
            {
                HEADZFGSRM002 entity = new HEADZFGSRM002();
                entity.MATNR = item.ItemCode;
                entity.LIFNR = item.SupplyCode;
                entity.EKORG = item.Organization;
                entity.ESOKZ = item.ProjectType;
                //entity.WERKS = "2002"; //cc-220713注释
                entity.WERKS = item.WERKS; //cc-220713添加
                entity.ZZSTACODE = item.StatusCode;
                entity.ZZZGJ = "";
                entity.ZSRRQ = item.OrderCTime.Value;
                //Z003 原材料  Z012 漆包线 通过采购订单、行号获取价格 Z008 服务类 Z013 铜材
                if (item.BaseType.ToUpper().Trim() == "Z003" || item.BaseType.ToUpper().Trim() == "Z012" || item.BaseType.ToUpper().Trim() == "Z008" || item.BaseType.ToUpper().Trim() == "Z013")
                {
                    entity.EBELN = item.OrderNo;
                    entity.EBELP = item.OrderLine;
                }
                var sapReturn = _sap.ZFGSRM002("001", DateTime.Now, entity, out isPosted, out message);
                if (isPosted)
                {
                    item.SettleUnitPrice = sapReturn.SettleUnitPrice;//结算单价
                    item.SettlementPrice = Math.Round(Convert.ToDecimal(item.SettleUnitPrice * item.PurchaseReceiptQty), 2, MidpointRounding.AwayFromZero);//结算金额
                    item.TaxAmount = Math.Round(Convert.ToDecimal(item.SettlementPrice * Convert.ToDecimal(item.TaxRate)), 2, MidpointRounding.AwayFromZero);//税额 =结算金额*税率  
                    item.AmountWithTax = item.SettlementPrice + item.TaxAmount; //含税金额
                }
                else
                {
                    item.SettleUnitPrice = 0;//结算单价
                    item.SettlementPrice = 0;//结算金额
                    item.TaxAmount = 0;//税额
                    item.AmountWithTax = 0;//含税金额
                }
            }

            //结算单价是否为0
            if (status == "1")
            {
                itemsData = itemsData.Where(t => t.SettleUnitPrice == 0).ToList();
            }
            else if (status == "0")
            {
                itemsData = itemsData.Where(t => t.SettleUnitPrice > 0).ToList();
            }

            isPosted = true;
            message = "";
            return itemsData;
        }
        
        #endregion

        #region 开票申请明细导出
        /// <summary>
        /// id集合
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<V_FO_InvoiceDetailExport> GetAllExportData(string[] ids)
        {
            var lstEntity = DbContext.Queryable<V_FO_InvoiceDetailExport>()
                .Where(t => ids.Contains(t.Id))
                .OrderBy(t => t.BillingNo)
                .OrderBy(t => t.InspectionNo)
                .OrderBy(t => t.InspectionLine).ToList();
            return lstEntity;
        }
        #endregion
    }
}
