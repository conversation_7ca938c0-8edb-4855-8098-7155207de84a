using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Application.SHM;
using AOS.SRM.Application.SHM.ViewModel;
using AOS.SRM.Application.Sys;
using AOS.SRM.Application.WMS;
using AOS.SRM.Entity.FO;
using AOS.SRM.Entity.FO.Dto;
using AOS.SRM.Entity.FO.ViewModel;
using AOS.SRM.Entity.SAP;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using AOS.SRM.Entity.WMS.req;
using HZ.WMS.Application.Sys;

namespace AOS.SRM.Application.FO
{
    /// <summary>
    /// 开票申请主表方法层
    /// </summary>
    public class I_InvoiceApp : BaseApp<I_Invoice>
    {
        I_InvoiceDetailApp _detailApp = new I_InvoiceDetailApp(); //开票明细
        I_InvoiceDisposalApp _disposalApp = new I_InvoiceDisposalApp();//开票-处置单
        UnqualifiedNoticeApp _unqualifiedApp = new UnqualifiedNoticeApp();//不合格处置
        CompromiseDisposalApp _compromiseApp = new CompromiseDisposalApp();//让步接收
        RRDFApp _rrdfApp = new RRDFApp();//奖励/返还处置
        DebitNoticeApp _debitApp = new DebitNoticeApp();//扣款通知单
        Sys_SAPCompanyInfoApp _sap = new Sys_SAPCompanyInfoApp();
        PO_PurchaseReceiptApp _purchaseReceiptApp = new PO_PurchaseReceiptApp();
        P_ConsignmentNoteApp _noteApp = new P_ConsignmentNoteApp();
        SupplierInfoApp _supplierApp = new SupplierInfoApp();
        Sys_UserApp _userApp = new Sys_UserApp();

        #region 提交开票信息
        /// <summary>
        /// 提交开票信息
        /// </summary>
        /// <param name="v_Invoice"></param>
        /// <param name="userCode"></param>
        public void SaveInvoiceInfo(I_Invoice_Views v_Invoice, string userCode)
        {
            var res = DbContext.Queryable<Sys_Dictionary>().Where(t => t.TypeCode == "GoodsRejected").ToList();
            if (res.Count > 0)
            {
                if (res[0].EnumValue == "1")
                {
                    // 校验是否存在退货明细 存在则无法先要提交所有的退货明细
                    var list = DbContext.Queryable<FO_ReceivingDetails_View>().AS("FO_ReceivingDetails_Rejected_View").Where(t => t.SupplyCode == v_Invoice.SupplyCode && t.CheckFlag == true).ToList();
                    // 跳过无结算单价的订单
                    for (var i = list.Count - 1; i >= 0; i--)
                    {
                        var item = list[i];
                        HEADZFGSRM002 entity = new HEADZFGSRM002();
                        entity.MATNR = item.ItemCode;
                        entity.LIFNR = item.SupplyCode;
                        entity.EKORG = item.Organization;
                        entity.ESOKZ = item.ProjectType;
                        //entity.WERKS = "2002"; //cc-220713注释
                        entity.WERKS = item.WERKS; //cc-220713添加
                        entity.ZZSTACODE = item.StatusCode;
                        entity.ZZZGJ = "";
                        entity.ZSRRQ = item.OrderCTime.Value;
                        //Z003 原材料  Z012 漆包线 通过采购订单、行号获取价格 Z008 服务类 Z013 铜材
                        if (item.BaseType.ToUpper().Trim() == "Z003" || item.BaseType.ToUpper().Trim() == "Z012" || item.BaseType.ToUpper().Trim() == "Z008" || item.BaseType.ToUpper().Trim() == "Z013")
                        {
                            entity.EBELN = item.OrderNo;
                            entity.EBELP = item.OrderLine;
                        }
                        var sapReturn = _sap.ZFGSRM002("001", DateTime.Now, entity, out bool isPosted, out string message);
                        if (isPosted)
                        {
                            if (sapReturn.SettleUnitPrice == 0)
                            {
                                list.RemoveAt(i);
                            }
                        }
                        else
                        {
                            list.RemoveAt(i);
                        }
                    }
                    // 校验需要判断的数据
                    var docNums = list.Select(t => t.ReceiptNo + t.OrderLine).ToList();
                    var receiptNos = v_Invoice.Invoice.Select(t => t.ReceiptNo + t.OrderLine).ToList();
                    var orderTypes = v_Invoice.Invoice.GroupBy(t => t.OrderType).Select(t => t.Key).ToList();
                    var contain = docNums.All(item => receiptNos.Contains(item));
                    if (!contain && !(orderTypes.Count == 1 && orderTypes[0] == "3"))
                    {
                        throw new Exception("存在未提交的退货明细，请先提交退货明细。");
                    }
                }
            }
            if (string.IsNullOrEmpty(v_Invoice.Id))
            {
                this.InvoiceAdd(v_Invoice, userCode);
            }
            else {
                this.InvoiceUpdate(v_Invoice, userCode);
            }
        }
        #endregion

        #region 开票申请新增
        /// <summary>
        /// 开票申请新增
        /// </summary>
        /// <returns></returns>
        public bool InvoiceAdd(I_Invoice_Views v_Invoice, string userCode)
        {
            v_Invoice.AdjustMoney = (v_Invoice.AdjustMoney == null ? 0 : v_Invoice.AdjustMoney);
            if (string.IsNullOrEmpty(v_Invoice.BillingNo) || this.Any(t => t.BillingNo == v_Invoice.BillingNo))
            {
                v_Invoice.BillingNo = "IN" + DateTime.Now.ToString("yyyyMMddHHmmss");
            }
            if (v_Invoice.InvoiceMoney == null || v_Invoice.InvoiceMoney == 0)
            {
                var totalAmoutWithTax = v_Invoice.Invoice.Where(t => t.AmountWithTax > 0).Sum(t => t.AmountWithTax);
                v_Invoice.InvoiceMoney = totalAmoutWithTax + v_Invoice.DisposalTotalMoney + v_Invoice.AdjustMoney;
                v_Invoice.ReturnMoney = v_Invoice.Invoice.Where(t => t.AmountWithTax < 0).Sum(t => t.AmountWithTax);
            }

            #region 主表赋值
            I_Invoice main = new I_Invoice();
            main.Id = Guid.NewGuid().ToString();
            main.InvoiceNo = v_Invoice.InvoiceNo;
            main.SupplyCode = v_Invoice.SupplyCode;
            main.SupplyName = v_Invoice.SupplyName;
            main.Remark = v_Invoice.Remark;
            main.BillingNo = v_Invoice.BillingNo;
            main.BankDeposit = v_Invoice.BankDeposit;
            main.BankAccount = v_Invoice.BankAccount;
            main.TotalPriceSum = v_Invoice.TotalPriceSum;
            main.TaxpayerNo = v_Invoice.TaxpayerNo;
            main.Tel = v_Invoice.Tel;
            main.Address = v_Invoice.Address;
            main.TaxAmount = v_Invoice.TaxAmount;
            main.TaxRate = v_Invoice.TaxRate;
            //main.CompanyCode = "2002"; //cc-220713注释
            main.CompanyCode = v_Invoice.CompanyCode;
            main.DisposalTotalMoney = v_Invoice.DisposalTotalMoney;
            main.InvoiceMoney = v_Invoice.InvoiceMoney;
            main.InvoiceTime = v_Invoice.InvoiceTime;
            main.Invoicer = v_Invoice.Invoicer;
            main.Status = "1";//已提报
            main.DocType = "1";//标准开票
            main.AdjustMoney = v_Invoice.AdjustMoney == null ? 0 : v_Invoice.AdjustMoney; //调整金额
            main.ReturnMoney = v_Invoice.ReturnMoney;//退货总金额
            main.CUser = userCode;
            main.CTime = DateTime.Now;
            main.BillScanPath = v_Invoice.BillScanPath;
            main.BillScanName = v_Invoice.BillScanName;
            #endregion

            #region 开票明细
            List<PO_PurchaseReceipt> lstPurchaseReceipt = new List<PO_PurchaseReceipt>(); //WMS采购入库信息
            List<PO_ReturnScanDetailed> lstPOReturn = new List<PO_ReturnScanDetailed>(); //采购退货
            List<MM_Warehousing> lstOutside = new List<MM_Warehousing>(); //WMS委外入库表
                                                                          //子表赋值

            foreach (I_InvoiceDetails detail in v_Invoice.Invoice)
            {
                detail.ParentId = main.Id;
                detail.BillingNo = main.BillingNo;
                detail.CUser = userCode;
                detail.CTime = DateTime.Now;
                detail.SupplyCode = main.SupplyCode;
                detail.SupplyName = main.SupplyName;
                detail.Unit = (detail.Unit == "PC" ? "ST" : detail.Unit);

                if (detail.OrderType == "1") //标准采购订单
                {
                    var data = DbContextForWMS.Queryable<PO_PurchaseReceipt>().InSingle(detail.PurchaseReceiptID);
                    if (data != null)
                    {
                        if (data.ApplySRM == true)
                        {
                            throw new Exception("明细中存在已提交过的单据，请重新选择提交。");
                        }
                        data.ApplySRM = true;
                        lstPurchaseReceipt.Add(data);
                    }
                }
                else if (detail.OrderType == "2")
                {
                    var data = DbContextForWMS.Queryable<MM_Warehousing>().InSingle(detail.PurchaseReceiptID);
                    if (data != null)
                    {
                        if (data.ApplySRM == true)
                        {
                            throw new Exception("明细中存在已提交过的单据，请重新选择提交。");
                        }
                        data.ApplySRM = true;
                        lstOutside.Add(data);
                    }
                }
                else if (detail.OrderType == "3")
                {
                    var data2 = DbContextForWMS.Queryable<PO_ReturnScanDetailed>().InSingle(detail.PurchaseReceiptID);
                    if (data2 != null)
                    {
                        if (data2.ApplySRM == true)
                        {
                            throw new Exception("明细中存在已提交过的单据，请重新选择提交。");
                        }
                        data2.ApplySRM = true;
                        lstPOReturn.Add(data2);
                    }
                }
            }
            #endregion

            try
            {

                #region 处置单明细
                List<S_UnqualifiedDisposal> lstUnqualifiedDisposal = new List<S_UnqualifiedDisposal>();//不合格处置单
                List<S_CompromiseDisposal> lstcompromiseDisposal = new List<S_CompromiseDisposal>();//让步接收单
                List<S_RRDF> lstRRDF = new List<S_RRDF>();//奖励/返还处置单
                List<S_DebitNotice> lstDebit = new List<S_DebitNotice>(); //扣款通知单
                //供应商处置单据赋值
                foreach (var item in v_Invoice.InvoiceDisposalDetail)
                {
                    #region 处置单赋值
                    item.Id = Guid.NewGuid().ToString();
                    item.InvoiceId = main.Id;
                    item.CTime = DateTime.Now;
                    item.CUser = userCode;

                    if (item.DocType == "1") //不合格处置单
                    {
                        var data = _unqualifiedApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstUnqualifiedDisposal.Add(data);
                    }
                    else if (item.DocType == "2") //让步接收单
                    {
                        var data = _compromiseApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstcompromiseDisposal.Add(data);
                    }
                    else if (item.DocType == "3" || item.DocType == "4")
                    {
                        var data = _rrdfApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstRRDF.Add(data);
                    }
                    else if (item.DocType == "5") //扣款通知单
                    {
                        var data = _debitApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10"; //更新为已完成
                        data.MTime = DateTime.Now;
                        data.MUser = userCode;
                        lstDebit.Add(data);
                    }
                    #endregion
                }
                #endregion

                #region 发票申请人记忆
                var userInfo = _userApp.GetFirstEntity(t => t.LoginAccount == userCode);
                if (userInfo != null && userInfo.IsSupplier == true && userInfo.FrgnName != v_Invoice.Invoicer)
                {
                    userInfo.FrgnName = v_Invoice.Invoicer;
                    _userApp.Update(userInfo);
                }
                #endregion

                #region 执行事务
                DbContext.Ado.BeginTran();//开启事务

                _detailApp.DbContext = this.DbContext;
                _disposalApp.DbContext = this.DbContext;
                _unqualifiedApp.DbContext = this.DbContext;
                _compromiseApp.DbContext = this.DbContext;
                _rrdfApp.DbContext = this.DbContext;
                _userApp.DbContext = this.DbContext;
                _debitApp.DbContext = this.DbContext; //扣款


                //提交主子表
                Insert(main);//开票主表
                _detailApp.Insert(v_Invoice.Invoice);//开票明细
                _disposalApp.Insert(v_Invoice.InvoiceDisposalDetail);//开票处置明细
                if (lstUnqualifiedDisposal.Count > 0)//不合格处置单据更新
                {
                    _unqualifiedApp.Update(lstUnqualifiedDisposal);
                }
                if (lstcompromiseDisposal.Count > 0)//让步接收单据状态更新
                {
                    _compromiseApp.Update(lstcompromiseDisposal);
                }
                if (lstRRDF.Count > 0)//奖励/返还处置单状态更新
                {
                    _rrdfApp.Update(lstRRDF);
                }
                if (lstDebit.Count > 0) //扣款通知单
                {
                    _debitApp.Update(lstDebit);
                }

                //更新WMS采购收货为已使用，拉取时直接过滤
                if (lstPurchaseReceipt.Count > 0)
                {
                    lstPurchaseReceipt.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                //采购退货
                if (lstPOReturn.Count > 0)
                {
                    lstPOReturn.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                //委外入库更新状态
                if (lstOutside.Count > 0)
                {
                    lstOutside.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }

                DbContext.Ado.CommitTran();//提交事务
                #endregion

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                return false;
            }

        }
        #endregion

        #region 开票申请修改

        /// <summary>
        /// 开票申请修改
        /// </summary>
        /// <returns></returns>
        public bool InvoiceUpdate(I_Invoice_Views v_Invoice, string userCode)
        {
            #region 表单提交后数据丢失，重新计算
            v_Invoice.AdjustMoney = (v_Invoice.AdjustMoney == null ? 0 : v_Invoice.AdjustMoney);
            if (v_Invoice.InvoiceMoney == null || v_Invoice.InvoiceMoney == 0)
            {
                var totalAmoutWithTax = v_Invoice.Invoice.Where(t => t.AmountWithTax > 0).Sum(t => t.AmountWithTax);
                v_Invoice.InvoiceMoney = totalAmoutWithTax + v_Invoice.DisposalTotalMoney + v_Invoice.AdjustMoney;
                v_Invoice.ReturnMoney = v_Invoice.Invoice.Where(t => t.AmountWithTax < 0).Sum(t => t.AmountWithTax);
            }
            #endregion

            var main = base.GetEntityByKey(v_Invoice.Id);
            var detailsOld = _detailApp.GetList(t => t.ParentId == v_Invoice.Id).ToList();
            var dispsoalsOld = _disposalApp.GetList(t => t.InvoiceId == v_Invoice.Id).ToList();

            #region 主表赋值
            main.InvoiceNo = v_Invoice.InvoiceNo;
            //main.SupplyCode = v_Invoice.SupplyCode;
            //main.SupplyName = v_Invoice.SupplyName;
            main.Remark = v_Invoice.Remark;
            //main.BankDeposit = v_Invoice.BankDeposit;
            //main.BankAccount = v_Invoice.BankAccount;
            main.TotalPriceSum = v_Invoice.TotalPriceSum;
            //main.TaxpayerNo = v_Invoice.TaxpayerNo;
            //main.Tel = v_Invoice.Tel;
            //main.Address = v_Invoice.Address;
            main.TaxAmount = v_Invoice.TaxAmount;
            main.TaxRate = v_Invoice.TaxRate;
            main.DisposalTotalMoney = v_Invoice.DisposalTotalMoney;
            main.InvoiceMoney = v_Invoice.InvoiceMoney;
            main.InvoiceTime = v_Invoice.InvoiceTime;
            main.Invoicer = v_Invoice.Invoicer;
            main.AdjustMoney = v_Invoice.AdjustMoney == null ? 0 : v_Invoice.AdjustMoney; //调整金额
            main.ReturnMoney = v_Invoice.ReturnMoney;//退货总金额
            main.MUser = userCode;
            main.MTime = DateTime.Now;
            #endregion

            #region 开票明细

            #region 原收获明细开票状态回退
            List<PO_PurchaseReceipt> lstP_Delete = new List<PO_PurchaseReceipt>(); //WMS采购入库信息
            List<PO_ReturnScanDetailed> lstR_Delete = new List<PO_ReturnScanDetailed>(); //采购退货
            List<MM_Warehousing> lstW_Delete = new List<MM_Warehousing>(); //WMS委外入库表

            List<string> oldIds = new List<string>(); //WMS委外入库表

            foreach (var item in detailsOld)
            {
                oldIds.Add(item.PurchaseReceiptID);
                if (item.OrderType == "1") //标准采购订单
                {
                    var data = DbContextForWMS.Queryable<PO_PurchaseReceipt>().InSingle(item.PurchaseReceiptID);
                    if (data != null)
                    {
                        data.ApplySRM = false;
                        lstP_Delete.Add(data);
                    }
                }
                else if (item.OrderType == "2")
                {
                    var data = DbContextForWMS.Queryable<MM_Warehousing>().InSingle(item.PurchaseReceiptID);
                    if (data != null)
                    {
                        data.ApplySRM = false;
                        lstW_Delete.Add(data);
                    }
                }
                else if (item.OrderType == "3")
                {
                    var data2 = DbContextForWMS.Queryable<PO_ReturnScanDetailed>().InSingle(item.PurchaseReceiptID);
                    if (data2 != null)
                    {
                        data2.ApplySRM = false;
                        lstR_Delete.Add(data2);
                    }
                }
            }
            #endregion


            #region 新收获明细开票状态更新
            List<PO_PurchaseReceipt> lstPurchaseReceipt = new List<PO_PurchaseReceipt>(); //WMS采购入库信息
            List<PO_ReturnScanDetailed> lstPOReturn = new List<PO_ReturnScanDetailed>(); //采购退货
            List<MM_Warehousing> lstOutside = new List<MM_Warehousing>(); //WMS委外入库表
            foreach (I_InvoiceDetails detail in v_Invoice.Invoice)
            {
                detail.ParentId = main.Id;
                detail.BillingNo = main.BillingNo;
                detail.CUser = userCode;
                detail.CTime = DateTime.Now;
                detail.SupplyCode = main.SupplyCode;
                detail.SupplyName = main.SupplyName;
                detail.Unit = (detail.Unit == "PC" ? "ST" : detail.Unit);

                if (detail.OrderType == "1") //标准采购订单
                {
                    var data = DbContextForWMS.Queryable<PO_PurchaseReceipt>().InSingle(detail.PurchaseReceiptID);
                    if (data != null)
                    {
                        if (!oldIds.Contains(detail.PurchaseReceiptID) && data.ApplySRM == true)
                        {
                            throw new Exception("明细中存在已提交过的单据，请重新选择提交。");
                        }
                        data.ApplySRM = true;
                        lstPurchaseReceipt.Add(data);
                    }
                    
                }
                else if (detail.OrderType == "2")
                {
                    var data = DbContextForWMS.Queryable<MM_Warehousing>().InSingle(detail.PurchaseReceiptID);
                    if (data != null)
                    {
                        if (!oldIds.Contains(detail.PurchaseReceiptID) && data.ApplySRM == true)
                        {
                            throw new Exception("明细中存在已提交过的单据，请重新选择提交。");
                        }
                        data.ApplySRM = true;
                        lstOutside.Add(data);
                    }
                }
                else if (detail.OrderType == "3")
                {
                    var data2 = DbContextForWMS.Queryable<PO_ReturnScanDetailed>().InSingle(detail.PurchaseReceiptID);
                    if (data2 != null)
                    {
                        if (!oldIds.Contains(detail.PurchaseReceiptID) && data2.ApplySRM == true)
                        {
                            throw new Exception("明细中存在已提交过的单据，请重新选择提交。");
                        }
                        data2.ApplySRM = true;
                        lstPOReturn.Add(data2);
                    }
                }
            }
            #endregion

            #endregion
            try
            {

                #region 处置单明细

                #region 原处置单状态回退
                List<S_UnqualifiedDisposal> lstUn_Delete = new List<S_UnqualifiedDisposal>();//不合格处置单
                List<S_CompromiseDisposal> lstCo_Delete = new List<S_CompromiseDisposal>();//让步接收单
                List<S_RRDF> lstRR_Delete = new List<S_RRDF>();//奖励/返还处置单
                List<S_DebitNotice> lstDe_Delete = new List<S_DebitNotice>(); //扣款通知单
                //供应商处置单据赋值
                foreach (var item in v_Invoice.InvoiceDisposalDetail)
                {
                    #region 处置单赋值
                    if (item.DocType == "1") //不合格处置单
                    {
                        var data = _unqualifiedApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "5";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstUn_Delete.Add(data);
                    }
                    else if (item.DocType == "2") //让步接收单
                    {
                        var data = _compromiseApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "5";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstCo_Delete.Add(data);
                    }
                    else if (item.DocType == "3" || item.DocType == "4")
                    {
                        var data = _rrdfApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "6";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstRR_Delete.Add(data);
                    }
                    else if (item.DocType == "5") //扣款通知单
                    {
                        var data = _debitApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "5"; 
                        data.MTime = DateTime.Now;
                        data.MUser = userCode;
                        lstDe_Delete.Add(data);
                    }
                    #endregion
                }
                #endregion

                #region 新处置单状态更新
                List<S_UnqualifiedDisposal> lstUnqualifiedDisposal = new List<S_UnqualifiedDisposal>();//不合格处置单
                List<S_CompromiseDisposal> lstcompromiseDisposal = new List<S_CompromiseDisposal>();//让步接收单
                List<S_RRDF> lstRRDF = new List<S_RRDF>();//奖励/返还处置单
                List<S_DebitNotice> lstDebit = new List<S_DebitNotice>(); //扣款通知单
                //供应商处置单据赋值
                foreach (var item in v_Invoice.InvoiceDisposalDetail)
                {
                    #region 处置单赋值
                    item.Id = Guid.NewGuid().ToString();
                    item.InvoiceId = main.Id;
                    item.CTime = DateTime.Now;
                    item.CUser = userCode;

                    if (item.DocType == "1") //不合格处置单
                    {
                        var data = _unqualifiedApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstUnqualifiedDisposal.Add(data);
                    }
                    else if (item.DocType == "2") //让步接收单
                    {
                        var data = _compromiseApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstcompromiseDisposal.Add(data);
                    }
                    else if (item.DocType == "3" || item.DocType == "4")
                    {
                        var data = _rrdfApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstRRDF.Add(data);
                    }
                    else if (item.DocType == "5") //扣款通知单
                    {
                        var data = _debitApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10"; //更新为已完成
                        data.MTime = DateTime.Now;
                        data.MUser = userCode;
                        lstDebit.Add(data);
                    }
                    #endregion
                }
                #endregion

                #endregion

                #region 执行事务
                DbContext.Ado.BeginTran();//开启事务

                _detailApp.DbContext = this.DbContext;
                _disposalApp.DbContext = this.DbContext;
                _unqualifiedApp.DbContext = this.DbContext;
                _compromiseApp.DbContext = this.DbContext;
                _rrdfApp.DbContext = this.DbContext;
                _userApp.DbContext = this.DbContext;
                _debitApp.DbContext = this.DbContext; //扣款

                _detailApp.HardDelete(detailsOld);//开票明细
                _disposalApp.HardDelete(dispsoalsOld);//开票-处置明细
                _unqualifiedApp.Update(lstUn_Delete); //不合格处置单
                _compromiseApp.Update(lstCo_Delete);//让步接收单
                _rrdfApp.Update(lstRR_Delete);//奖励/返还处置单状态更新
                _debitApp.Update(lstDe_Delete);//扣款通知单
                lstP_Delete.ForEach(t => { DbContextForWMS.Updateable(t).ExecuteCommand(); });//采购收货状态回退
                lstR_Delete.ForEach(t => { DbContextForWMS.Updateable(t).ExecuteCommand(); });//采购退货状态回退
                lstW_Delete.ForEach(t => { DbContextForWMS.Updateable(t).ExecuteCommand(); });//委外收获状态回退

                base.Update(main);//更新开票主表
                _detailApp.Insert(v_Invoice.Invoice);//开票明细
                _disposalApp.Insert(v_Invoice.InvoiceDisposalDetail);//开票处置明细
                _unqualifiedApp.Update(lstUnqualifiedDisposal); //不合格处置单
                _compromiseApp.Update(lstcompromiseDisposal);//让步接收单
                _rrdfApp.Update(lstRRDF);//奖励/返还处置单状态更新
                _debitApp.Update(lstDebit);//扣款通知单
                lstPurchaseReceipt.ForEach(t => { DbContextForWMS.Updateable(t).ExecuteCommand(); });//采购收获状态更新
                lstPOReturn.ForEach(t => { DbContextForWMS.Updateable(t).ExecuteCommand(); });//采购退货状态更新
                lstOutside.ForEach(t => { DbContextForWMS.Updateable(t).ExecuteCommand(); });//委外收获状态更新

                DbContext.Ado.CommitTran();//提交事务
                #endregion

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                //throw ex;
                return false;

            }

        }
        #endregion

        #region 审核开票申请单
        /// <summary>
        /// 审核开票申请单
        /// </summary>
        /// <returns></returns>
        public int CheckInvoice(string[] ids, string user)
        {

            List<I_Invoice> lstEntity = GetListByKeys(ids);
            foreach (var item in lstEntity)
            {
                item.MTime = DateTime.Now;
                item.MUser = user;
                item.Status = "2";//已审核
            }
            return UpdateWithTran(lstEntity);


        }
        #endregion

        #region 驳回开票申请单
        /// <summary>
        /// 驳回开票申请单
        /// </summary>
        /// <returns></returns>
        public bool RejectInvoice(string[] ids, string user)
        {
            try
            {
                List<S_UnqualifiedDisposal> lstUnqualifiedDisposal = new List<S_UnqualifiedDisposal>();//不合格处置单
                List<S_CompromiseDisposal> lstcompromiseDisposal = new List<S_CompromiseDisposal>();//让步接收单
                List<S_RRDF> lstRRDF = new List<S_RRDF>();//奖励/返还处置单
                List<S_DebitNotice> lstDebit = new List<S_DebitNotice>(); //扣款通知单

                List<PO_PurchaseReceipt> lstPurchaseReceipt = new List<PO_PurchaseReceipt>(); //WMS采购入库信息
                List<PO_ReturnScanDetailed> lstPOReturn = new List<PO_ReturnScanDetailed>(); //采购退货
                List<MM_Warehousing> lstOutside = new List<MM_Warehousing>(); //WMS委外入库表

                List<I_Invoice> lstEntity = GetListByKeys(ids);
                foreach (var item in lstEntity)
                {
                    item.MTime = DateTime.Now;
                    item.MUser = user;
                    item.Status = "3";//已驳回

                    #region 收货明细处理

                    var invoiceDetail = _detailApp.GetList(t => t.ParentId == item.Id).ToList();
                    foreach (I_InvoiceDetails detail in invoiceDetail)
                    {
                        if (detail.OrderType == "1") //标准采购订单
                        {
                            var data = DbContextForWMS.Queryable<PO_PurchaseReceipt>().InSingle(detail.PurchaseReceiptID);
                            if (data != null)
                            {
                                data.ApplySRM = false;
                                lstPurchaseReceipt.Add(data);
                            }
                            var data2 = DbContextForWMS.Queryable<PO_ReturnScanDetailed>().InSingle(detail.PurchaseReceiptID);
                            if (data2 != null)
                            {
                                data2.ApplySRM = false;
                                lstPOReturn.Add(data2);
                            }
                        }
                        else if (detail.OrderType == "2")
                        {
                            var data = DbContextForWMS.Queryable<MM_Warehousing>().InSingle(detail.PurchaseReceiptID);
                            if (data != null)
                            {
                                data.ApplySRM = false;
                                lstOutside.Add(data);
                            }
                        }
                    }
                    #endregion

                    #region 处置单据处理
                    //处置单据撤回原来状态
                    List<I_InvoiceDisposal> lstDisposal = _disposalApp.GetList(t => t.InvoiceId == item.Id).ToList();//获取开票-处置单信息
                    foreach (var dis in lstDisposal)
                    {
                        if (dis.DocType == "1") //不合格处置单
                        {
                            var data = _unqualifiedApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstUnqualifiedDisposal.Add(data);
                        }
                        else if (dis.DocType == "2") //让步接收单
                        {
                            var data = _compromiseApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstcompromiseDisposal.Add(data);
                        }
                        else if (dis.DocType == "3" || dis.DocType == "4")  //奖励/返还处置
                        {
                            var data = _rrdfApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "6"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstRRDF.Add(data);
                        }
                        else if (dis.DocType == "5") //扣款通知单
                        {
                            var data = _debitApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstDebit.Add(data);
                        }
                    }
                    #endregion

                }

                DbContext.Ado.BeginTran();//开启事务
                _disposalApp.DbContext = this.DbContext;
                _unqualifiedApp.DbContext = this.DbContext;
                _compromiseApp.DbContext = this.DbContext;
                _rrdfApp.DbContext = this.DbContext;
                _debitApp.DbContext = this.DbContext;

                Update(lstEntity);//更新开票信息状态
                if (lstUnqualifiedDisposal.Count > 0)//不合格处置单据更新
                {
                    _unqualifiedApp.Update(lstUnqualifiedDisposal);
                }
                if (lstcompromiseDisposal.Count > 0)//让步接收单据状态更新
                {
                    _compromiseApp.Update(lstcompromiseDisposal);
                }
                if (lstRRDF.Count > 0)//奖励/返还处置单状态更新
                {
                    _rrdfApp.Update(lstRRDF);
                }
                if (lstDebit.Count > 0) //扣款通知单
                {
                    _debitApp.Update(lstDebit);
                }

                //更新WMS采购收货为未使用，
                if (lstPurchaseReceipt.Count > 0)
                {
                    lstPurchaseReceipt.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                //采购退货
                if (lstPOReturn.Count > 0)
                {
                    lstPOReturn.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                if (lstOutside.Count > 0)//委外入库更新状态
                {
                    lstOutside.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                DbContext.Ado.CommitTran();//提交事务

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                return false;

            }
        }
        #endregion

        #region 删除开票申请单
        /// <summary>
        /// 删除开票申请单
        /// </summary>
        /// <returns></returns>
        public bool DeleteInvoice(string[] ids, string user)
        {
            try
            {
                List<S_UnqualifiedDisposal> lstUnqualifiedDisposal = new List<S_UnqualifiedDisposal>();//不合格处置单
                List<S_CompromiseDisposal> lstcompromiseDisposal = new List<S_CompromiseDisposal>();//让步接收单
                List<S_RRDF> lstRRDF = new List<S_RRDF>();//奖励/返还处置单
                List<S_DebitNotice> lstDebit = new List<S_DebitNotice>(); //扣款通知单

                List<PO_PurchaseReceipt> lstPurchaseReceipt = new List<PO_PurchaseReceipt>(); //WMS采购入库信息
                List<PO_ReturnScanDetailed> lstPOReturn = new List<PO_ReturnScanDetailed>(); //采购退货
                List<MM_Warehousing> lstOutside = new List<MM_Warehousing>(); //WMS委外入库表

                List<I_Invoice> lstEntity = GetListByKeys(ids); //获取开票主表信息
                foreach (var item in lstEntity)
                {
                    item.DUser = user;
                    item.DTime = DateTime.Now;
                    #region 收货明细处理

                    var invoiceDetail = _detailApp.GetList(t => t.ParentId == item.Id).ToList();
                    foreach (I_InvoiceDetails detail in invoiceDetail)
                    {
                        if (detail.OrderType == "1") //标准采购订单
                        {
                            var data = DbContextForWMS.Queryable<PO_PurchaseReceipt>().InSingle(detail.PurchaseReceiptID);
                            if (data != null)
                            {
                                data.ApplySRM = false;
                                lstPurchaseReceipt.Add(data);
                            }
                        }
                        else if (detail.OrderType == "2")
                        {
                            var data = DbContextForWMS.Queryable<MM_Warehousing>().InSingle(detail.PurchaseReceiptID);
                            if (data != null)
                            {
                                data.ApplySRM = false;
                                lstOutside.Add(data);
                            }
                        }
                        else if (detail.OrderType == "3")
                        {
                            var data2 = DbContextForWMS.Queryable<PO_ReturnScanDetailed>().InSingle(detail.PurchaseReceiptID);
                            if (data2 != null)
                            {
                                data2.ApplySRM = false;
                                lstPOReturn.Add(data2);
                            }
                        }
                    }
                    #endregion

                    #region 处置单据处理
                    //处置单据撤回原来状态
                    List<I_InvoiceDisposal> lstDisposal = _disposalApp.GetList(t => t.InvoiceId == item.Id).ToList();//获取开票-处置单信息
                    foreach (var dis in lstDisposal)
                    {
                        if (dis.DocType == "1") //不合格处置单
                        {
                            var data = _unqualifiedApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstUnqualifiedDisposal.Add(data);
                        }
                        else if (dis.DocType == "2") //让步接收单
                        {
                            var data = _compromiseApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstcompromiseDisposal.Add(data);
                        }
                        else if (dis.DocType == "3" || dis.DocType == "4")
                        {
                            var data = _rrdfApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "6"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstRRDF.Add(data);
                        }
                        else if (dis.DocType == "5") //扣款通知单
                        {
                            var data = _debitApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstDebit.Add(data);
                        }
                    }
                    #endregion
                }

                DbContext.Ado.BeginTran();//开启事务
                _disposalApp.DbContext = this.DbContext;
                _unqualifiedApp.DbContext = this.DbContext;
                _compromiseApp.DbContext = this.DbContext;
                _rrdfApp.DbContext = this.DbContext;
                _debitApp.DbContext = this.DbContext;

                Delete(lstEntity);//删除开票信息
                if (lstUnqualifiedDisposal.Count > 0)//不合格处置单据更新
                {
                    _unqualifiedApp.Update(lstUnqualifiedDisposal);
                }
                if (lstcompromiseDisposal.Count > 0)//让步接收单据状态更新
                {
                    _compromiseApp.Update(lstcompromiseDisposal);
                }
                if (lstRRDF.Count > 0)//奖励/返还处置单状态更新
                {
                    _rrdfApp.Update(lstRRDF);
                }
                if (lstDebit.Count > 0) //扣款通知单
                {
                    _debitApp.Update(lstDebit);
                }

                //更新WMS采购收货为未使用，
                if (lstPurchaseReceipt.Count > 0)
                {
                    lstPurchaseReceipt.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                //采购退货
                if (lstPOReturn.Count > 0)
                {
                    lstPOReturn.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                if (lstOutside.Count > 0)//委外入库更新状态
                {
                    lstOutside.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                DbContext.Ado.CommitTran();//提交事务

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                //throw ex;
                return false;

            }
        }
        #endregion

        #region 获取供应商处置单据
        /// <summary>
        /// 供应商处置单据
        /// </summary>
        /// <param name="page"></param>
        /// <param name="supplyCode">供应商编码</param>
        /// <param name="docType">处置单据类型</param>
        /// <param name="disposalNo">处置单号</param>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <returns></returns>
        public List<V_SHM_SupplierDisposalnfo> GetSupplierDisposalnfo(Pagination page, string supplyCode, string docType, string disposalNo, DateTime startTime, DateTime endTime)
        {

            //if (string.IsNullOrEmpty(page.Sort))
            //{
            //    page.Sort = "CTime desc";
            //}
            //else
            //{
            //    page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            //}
            var itemsData = this.DbContext.Queryable<V_SHM_SupplierDisposalnfo>()
                .Where(t => (string.IsNullOrEmpty(supplyCode) || t.SupplyCode == supplyCode)
                && (string.IsNullOrEmpty(docType) || t.DocType == docType)
                && (string.IsNullOrEmpty(disposalNo) || t.DisposalNo.Contains(disposalNo))
                && t.DisposalDate >= startTime
                && t.DisposalDate < endTime.AddDays(1)
                )
                .OrderBy(t => t.DisposalDate).ToList();
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<V_SHM_SupplierDisposalnfo>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        #endregion

        //根据供应商带出电话、地址、开户行已经账号
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SupplyerCode"></param>
        /// <returns></returns>
        public List<S_SupplierInfo> GetSupplyInfo(string SupplyerCode)
        {
            var list = DbContext.Queryable<S_SupplierInfo>().Where(t => t.SupplyerCode == SupplyerCode).ToList();
            return list;
        }

        /// <summary>
        /// 过账（批量）
        /// </summary>
        /// <param name="lstDto"></param>
        /// <param name="message"></param>
        /// <param name="opUser">过账人</param>
        /// <param name="postTime">过账时间</param>
        /// <returns></returns>
        public bool DoPost(List<InvoiceDto> lstDto, DateTime postTime, string opUser, out string message)
        {
            bool IsPosted;
            message = "";
            List<I_Invoice> lstMainEdit = new List<I_Invoice>();
            try
            {
                foreach (var dto in lstDto)
                {
                    var main = dto.main;

                    //获取供应商付款条件
                    var supplierInfo = _supplierApp.GetFirstEntityByFieldValue("SupplyerCode", main.SupplyCode);
                    var paymentTermCode = supplierInfo.PaymentTermCode;
                    if (string.IsNullOrEmpty(paymentTermCode))
                    {
                        paymentTermCode = "0001"; //立即付款
                    }
                    #region 预制发票
                    var detail1 = dto.detail.Where(t => t.PurchaseReceiptQty > 0).ToList();
                    if (detail1 != null && detail1.Count > 0)
                    {
                        #region 发票抬头
                        ZFGSRM004 entity = new ZFGSRM004()
                        {
                            ZID = main.InvoiceNo,
                            BUKRS = main.CompanyCode,
                            LIFNR = main.SupplyCode,
                            RMWWR = main.InvoiceMoney,
                            SGTXT = "",
                            BLDAT = main.InvoiceTime,
                            ZFBDT = DateTime.Now,
                            BUDAT = postTime,
                            ZDXPZ = "",
                            ZTERM = paymentTermCode
                        };
                        #endregion

                        #region 发票明细
                        var groupData = detail1.GroupBy(t => new { t.OrderNo, t.OrderLine, t.SapDocNum, t.SapLine, t.ItemCode, t.Unit })
                            .Select(g => new
                            {
                                g.Key.OrderNo,
                                g.Key.OrderLine,
                                g.Key.SapDocNum,
                                g.Key.SapLine,
                                g.Key.ItemCode,
                                g.Key.Unit,
                                PurchaseReceiptQty = g.Sum(s => s.PurchaseReceiptQty),
                                SettlementPrice = g.Sum(s => s.SettlementPrice)
                            }).OrderBy(t => t.OrderLine).ToList();

                        int i = 0;
                        List<ZFGSRM004_Item> _items = new List<ZFGSRM004_Item>();
                        foreach (var detail in groupData)
                        {
                            i += 1;
                            ZFGSRM004_Item item = new ZFGSRM004_Item()
                            {
                                RBLGP = i,
                                EBELN = detail.OrderNo,
                                EBELP = detail.OrderLine,
                                LFBNR = detail.SapDocNum,
                                LFPOS = detail.SapLine,
                                MATNR = detail.ItemCode,
                                MENGE = detail.PurchaseReceiptQty,
                                MEINS = detail.Unit,
                                SMWWR = detail.SettlementPrice
                            };
                            _items.Add(item);
                        }
                        entity.Items = _items;
                        #endregion

                        #region 处置、差异调整
                        List<ZFGSRM004_Item1> _items1 = new List<ZFGSRM004_Item1>();
                        //处置金额
                        if (main.DisposalTotalMoney != null && main.DisposalTotalMoney != 0)
                        {
                            string SHKZG = "";
                            if (main.DisposalTotalMoney > 0)
                                SHKZG = "S"; //借方、奖励
                            else if (main.DisposalTotalMoney < 0)
                                SHKZG = "H"; //贷方、扣款
                            ZFGSRM004_Item1 item1 = new ZFGSRM004_Item1()
                            {
                                RBLGP = 1,
                                HKONT = "7001350100",//默认值
                                MWSKZ = "J0",
                                WRBTR = Math.Abs((decimal)main.DisposalTotalMoney),
                                SHKZG = SHKZG
                            };
                            _items1.Add(item1);
                        }
                        if (main.AdjustMoney != null && main.AdjustMoney != 0)
                        {
                            string SHKZG = "";
                            if (main.AdjustMoney > 0)
                                SHKZG = "S"; //借方、奖励
                            else if (main.AdjustMoney < 0)
                                SHKZG = "H"; //贷方、扣款
                            ZFGSRM004_Item1 item1 = new ZFGSRM004_Item1()
                            {
                                RBLGP = 2,
                                HKONT = "6401080000",//默认值
                                MWSKZ = "J0",
                                WRBTR = Math.Abs((decimal)main.AdjustMoney),
                                SHKZG = SHKZG
                            };
                            _items1.Add(item1);
                        }
                        entity.Items1 = _items1;
                        #endregion

                        string sapInvoiceNo = _sap.ZFGSRM004("001", postTime, entity, out IsPosted, out message);
                        if (IsPosted)
                        {
                            main.IsPosted = true;
                            main.PostTime = postTime;
                            main.PostUser = opUser;
                            main.SAPInvoiceNo = sapInvoiceNo;
                            main.Status = "4";
                            main.MTime = DateTime.Now;
                            main.MUser = opUser;
                            lstMainEdit.Add(main);
                        }
                        else
                        {
                            return false;
                        }
                    }
                    #endregion

                    #region 贷方凭证
                    var detail2 = dto.detail.Where(t => t.PurchaseReceiptQty < 0).ToList();
                    if (detail2 != null && detail2.Count > 0)
                    {
                        #region 发票抬头
                        ZFGSRM004 entity = new ZFGSRM004()
                        {
                            ZID = main.InvoiceNo,
                            BUKRS = main.CompanyCode,
                            LIFNR = main.SupplyCode,
                            RMWWR = Math.Abs((decimal)main.ReturnMoney),
                            SGTXT = "",
                            BLDAT = main.InvoiceTime,
                            ZFBDT = DateTime.Now,
                            BUDAT = postTime,
                            ZDXPZ = "X",
                            ZTERM = paymentTermCode
                        };
                        #endregion

                        #region 发票明细
                        var groupData = detail2.GroupBy(t => new { t.OrderNo, t.OrderLine, t.SapDocNum, t.SapLine, t.ItemCode, t.Unit })
                            .Select(g => new
                            {
                                g.Key.OrderNo,
                                g.Key.OrderLine,
                                g.Key.SapDocNum,
                                g.Key.SapLine,
                                g.Key.ItemCode,
                                g.Key.Unit,
                                PurchaseReceiptQty = g.Sum(s => s.PurchaseReceiptQty),
                                SettlementPrice = g.Sum(s => s.SettlementPrice)
                            }).OrderBy(t => t.OrderLine).ToList();

                        int i = 0;
                        List<ZFGSRM004_Item> _items = new List<ZFGSRM004_Item>();
                        foreach (var detail in groupData)
                        {
                            i += 1;
                            ZFGSRM004_Item item = new ZFGSRM004_Item()
                            {
                                RBLGP = i,
                                EBELN = detail.OrderNo,
                                EBELP = detail.OrderLine,
                                LFBNR = detail.SapDocNum,
                                LFPOS = detail.SapLine,
                                MATNR = detail.ItemCode,
                                MENGE = Math.Abs((decimal)detail.PurchaseReceiptQty),
                                MEINS = detail.Unit,
                                SMWWR = Math.Abs((decimal)detail.SettlementPrice)
                            };
                            _items.Add(item);
                        }
                        entity.Items = _items;
                        #endregion

                        string SAPInvoiceNo2 = _sap.ZFGSRM004("001", postTime, entity, out IsPosted, out message);
                        if (IsPosted)
                        {
                            main.SAPInvoiceNo2 = SAPInvoiceNo2;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    #endregion
                }
                //更新过账信息
                if (lstMainEdit.Count > 0)
                {
                    UpdateWithTran(lstMainEdit);
                }

                return true;
            }
            catch (Exception ex)
            {
                message = ex.Message;
                return false;
            }
        }

        #region 提交物流开票申请

        /// <summary>
        /// 提交开票申请New
        /// </summary>
        /// <returns></returns>
        public bool InsertLogisticsInvoice(LogisticsInvoiceDto dto, string userCode)
        {
            try
            {
                var main = dto.main;
                var logisticsDetail = dto.LogisticsDetail;
                var disposalDetail = dto.disposal;

                if (string.IsNullOrEmpty(main.BillingNo) || this.Any(t => t.BillingNo == main.BillingNo))
                {
                    main.BillingNo = "IN" + DateTime.Now.ToString("yyyyMMddHHmmss");
                }

                #region 主表赋值
                I_Invoice Invoice = new I_Invoice();
                Invoice.Id = Guid.NewGuid().ToString();
                Invoice.CUser = userCode;
                Invoice.InvoiceNo = main.InvoiceNo;
                Invoice.CTime = DateTime.Now;
                Invoice.SupplyCode = main.SupplyCode;
                Invoice.SupplyName = main.SupplyName;
                Invoice.Remark = main.Remark;
                Invoice.BillingNo = main.BillingNo;
                Invoice.BankDeposit = main.BankDeposit;
                Invoice.BankAccount = main.BankAccount;
                Invoice.TotalPriceSum = main.TotalPriceSum;
                Invoice.TaxpayerNo = main.TaxpayerNo;
                Invoice.Tel = main.Tel;
                Invoice.Address = main.Address;
                Invoice.TaxAmount = main.TaxAmount;
                Invoice.TaxRate = main.TaxRate;
                Invoice.CompanyCode = "2002";
                Invoice.DisposalTotalMoney = main.DisposalTotalMoney;
                Invoice.InvoiceMoney = main.InvoiceMoney;
                Invoice.InvoiceTime = main.InvoiceTime;
                Invoice.Invoicer = main.Invoicer;
                Invoice.Status = "1";//已提报
                Invoice.DocType = "2";//物流开票
                #endregion

                //更新物流对账过账明细 的开票申请Id
                foreach (var detail in logisticsDetail)
                {
                    detail.InvoiceId = Invoice.Id;
                    detail.MTime = DateTime.Now;
                    detail.MUser = userCode;
                }
                List<S_UnqualifiedDisposal> lstUnqualifiedDisposal = new List<S_UnqualifiedDisposal>();//不合格处置单
                List<S_CompromiseDisposal> lstcompromiseDisposal = new List<S_CompromiseDisposal>();//让步接收单
                List<S_RRDF> lstRRDF = new List<S_RRDF>();//奖励/返还处置单
                //供应商处置单据赋值
                foreach (var item in disposalDetail)
                {
                    #region 处置单赋值
                    item.Id = Guid.NewGuid().ToString();
                    item.InvoiceId = Invoice.Id;
                    item.CTime = DateTime.Now;
                    item.CUser = userCode;

                    if (item.DocType == "1") //不合格处置单
                    {
                        var data = _unqualifiedApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstUnqualifiedDisposal.Add(data);
                    }
                    else if (item.DocType == "2") //让步接收单
                    {
                        var data = _compromiseApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstcompromiseDisposal.Add(data);
                    }
                    else if (item.DocType == "3" || item.DocType == "4")
                    {
                        var data = _rrdfApp.GetEntityByKey(item.DisposalId);
                        data.Id = item.DisposalId;
                        data.Status = "10";
                        data.MTime = data.MTime;
                        data.MUser = userCode;
                        lstRRDF.Add(data);
                    }
                    #endregion
                }

                #region 执行事务
                DbContext.Ado.BeginTran();//开启事务

                _noteApp.DbContext = this.DbContext;
                _disposalApp.DbContext = this.DbContext;
                _unqualifiedApp.DbContext = this.DbContext;
                _compromiseApp.DbContext = this.DbContext;
                _rrdfApp.DbContext = this.DbContext;

                //提交主子表
                Insert(Invoice);//开票主表
                _noteApp.Update(logisticsDetail);//物流开票过账明细
                _disposalApp.Insert(disposalDetail);//开票处置明细
                if (lstUnqualifiedDisposal.Count > 0)//不合格处置单据更新
                {
                    _unqualifiedApp.Update(lstUnqualifiedDisposal);
                }
                if (lstcompromiseDisposal.Count > 0)//让步接收单据状态更新
                {
                    _compromiseApp.Update(lstcompromiseDisposal);
                }
                if (lstRRDF.Count > 0)//奖励/返还处置单状态更新
                {
                    _rrdfApp.Update(lstRRDF);
                }

                DbContext.Ado.CommitTran();//提交事务
                #endregion

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                //throw ex;
                return false;

            }

        }
        #endregion

        #region 物流开票过账
        /// <summary>
        /// 过账（批量）
        /// </summary>
        /// <param name="lstDto"></param>
        /// <param name="message"></param>
        /// <param name="opUser">过账人</param>
        /// <param name="postTime">过账时间</param>
        /// <returns></returns>
        public bool LogisticsDoPost(List<LogisticsInvoiceDto> lstDto, DateTime postTime, string opUser, out string message)
        {
            try
            {
                bool IsPosted;
                message = "";
                List<I_Invoice> lstMainEdit = new List<I_Invoice>();
                foreach (var dto in lstDto)
                {
                    var main = dto.main;
                    //获取供应商付款条件
                    var supplierInfo = _supplierApp.GetFirstEntityByFieldValue("SupplyerCode", main.SupplyCode);
                    var paymentTermCode = supplierInfo.PaymentTermCode;
                    if (string.IsNullOrEmpty(paymentTermCode))
                    {
                        paymentTermCode = "0001"; //立即付款
                    }

                    #region 发票抬头
                    ZFGSRM004 entity = new ZFGSRM004()
                    {
                        ZID = main.InvoiceNo,
                        BUKRS = main.CompanyCode,
                        LIFNR = main.SupplyCode,
                        RMWWR = main.InvoiceMoney,
                        SGTXT = "",
                        BLDAT = main.InvoiceTime,
                        ZFBDT = DateTime.Now,
                        BUDAT = postTime,
                        ZDXPZ = "",
                        ZTERM = paymentTermCode
                    };
                    #endregion

                    #region 发票明细
                    var groupData = dto.LogisticsDetail.GroupBy(t => new { t.SAPDocNum, t.SAPLine, t.SAPMaterialNum, t.SAPMaterialLine, t.ItemCode, t.Unit })
                        .Select(g => new
                        {
                            OrderNo = g.Key.SAPDocNum,
                            OrderLine = g.Key.SAPLine,
                            SapDocNum = g.Key.SAPMaterialNum,
                            SapLine = g.Key.SAPMaterialLine,
                            g.Key.ItemCode,
                            g.Key.Unit,
                            PurchaseReceiptQty = g.Sum(s => s.Qty),
                            SettlementPrice = g.Sum(s => s.RowActualAmount)
                        }).ToList();

                    int i = 0;
                    List<ZFGSRM004_Item> _items = new List<ZFGSRM004_Item>();
                    foreach (var detail in groupData)
                    {
                        i += 1;
                        ZFGSRM004_Item item = new ZFGSRM004_Item()
                        {
                            RBLGP = i,
                            EBELN = detail.OrderNo,
                            EBELP = detail.OrderLine,
                            LFBNR = detail.SapDocNum,
                            LFPOS = detail.SapLine,
                            MATNR = detail.ItemCode,
                            MENGE = detail.PurchaseReceiptQty,
                            MEINS = detail.Unit,
                            SMWWR = detail.SettlementPrice
                        };
                        _items.Add(item);
                    }
                    entity.Items = _items;

                    #endregion

                    #region 处置
                    List<ZFGSRM004_Item1> _items1 = new List<ZFGSRM004_Item1>();
                    //处置金额
                    if (main.DisposalTotalMoney != null && main.DisposalTotalMoney != 0)
                    {
                        string SHKZG = "";
                        if (main.DisposalTotalMoney > 0)
                            SHKZG = "S"; //借方、奖励
                        else if (main.DisposalTotalMoney < 0)
                            SHKZG = "H"; //贷方、扣款
                        ZFGSRM004_Item1 item1 = new ZFGSRM004_Item1()
                        {
                            RBLGP = 1,
                            HKONT = "7001350100",//默认值
                            MWSKZ = "J0",
                            WRBTR = Math.Abs((decimal)main.DisposalTotalMoney),
                            SHKZG = SHKZG
                        };
                        _items1.Add(item1);
                    }
                    entity.Items1 = _items1;
                    #endregion

                    string sapInvoiceNo = _sap.ZFGSRM004("001", postTime, entity, out IsPosted, out message);
                    if (IsPosted)
                    {
                        main.IsPosted = true;
                        main.PostTime = postTime;
                        main.PostUser = opUser;
                        main.SAPInvoiceNo = sapInvoiceNo;
                        main.Status = "4";
                        main.MTime = DateTime.Now;
                        main.MUser = opUser;
                        lstMainEdit.Add(main);
                    }
                    else
                    {
                        return false;
                    }
                }
                //更新过账信息
                if (lstMainEdit.Count > 0)
                {
                    UpdateWithTran(lstMainEdit);
                }

                return true;
            }
            catch (Exception ex)
            {
                message = ex.Message;
                return false;
            }
        }
        #endregion

        #region 驳回物流开票申请单
        /// <summary>
        /// 驳回物流开票申请单
        /// </summary>
        /// <returns></returns>
        public bool RejectLogisticsInvoice(string[] ids, string user)
        {
            try
            {
                List<S_UnqualifiedDisposal> lstUnqualifiedDisposal = new List<S_UnqualifiedDisposal>();//不合格处置单
                List<S_CompromiseDisposal> lstcompromiseDisposal = new List<S_CompromiseDisposal>();//让步接收单
                List<S_RRDF> lstRRDF = new List<S_RRDF>();//奖励/返还处置单

                List<P_ConsignmentNote> lstLogisticsReceipt = new List<P_ConsignmentNote>(); //物流收货明细

                List<I_Invoice> lstEntity = GetListByKeys(ids);
                foreach (var item in lstEntity)
                {
                    item.MTime = DateTime.Now;
                    item.MUser = user;
                    item.Status = "3";//已驳回

                    #region 物流开票明细处理

                    var LogisticsInvoiceDetail = _noteApp.GetList(t => t.InvoiceId == item.Id).ToList();
                    LogisticsInvoiceDetail.ForEach(t =>
                    {
                        t.InvoiceId = null;
                        t.MTime = DateTime.Now;
                        t.MUser = user;
                    });
                    lstLogisticsReceipt.AddRange(LogisticsInvoiceDetail);

                    #endregion

                    #region 处置单据处理
                    //处置单据撤回原来状态
                    List<I_InvoiceDisposal> lstDisposal = _disposalApp.GetList(t => t.InvoiceId == item.Id).ToList();//获取开票-处置单信息
                    foreach (var dis in lstDisposal)
                    {
                        if (dis.DocType == "1") //不合格处置单
                        {
                            var data = _unqualifiedApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstUnqualifiedDisposal.Add(data);
                        }
                        else if (dis.DocType == "2") //让步接收单
                        {
                            var data = _compromiseApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstcompromiseDisposal.Add(data);
                        }
                        else if (dis.DocType == "3" || dis.DocType == "4")
                        {
                            var data = _rrdfApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "6"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstRRDF.Add(data);
                        }
                    }
                    #endregion

                }

                DbContext.Ado.BeginTran();//开启事务
                _disposalApp.DbContext = this.DbContext;
                _unqualifiedApp.DbContext = this.DbContext;
                _compromiseApp.DbContext = this.DbContext;
                _rrdfApp.DbContext = this.DbContext;
                _noteApp.DbContext = this.DbContext;

                Update(lstEntity);//更新物流开票信息状态
                if (lstUnqualifiedDisposal.Count > 0)//不合格处置单据更新
                {
                    _unqualifiedApp.Update(lstUnqualifiedDisposal);
                }
                if (lstcompromiseDisposal.Count > 0)//让步接收单据状态更新
                {
                    _compromiseApp.Update(lstcompromiseDisposal);
                }
                if (lstRRDF.Count > 0)//奖励/返还处置单状态更新
                {
                    _rrdfApp.Update(lstRRDF);
                }

                _noteApp.Update(lstLogisticsReceipt); //物流开票明细 设置为 未开票状态

                DbContext.Ado.CommitTran();//提交事务

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                //throw ex;
                return false;

            }
        }
        #endregion

        #region 删除物流开票申请单
        /// <summary>
        /// 删除物流开票申请单
        /// </summary>
        /// <returns></returns>
        public bool DeleteLogisticsInvoice(string[] ids, string user)
        {
            try
            {
                List<S_UnqualifiedDisposal> lstUnqualifiedDisposal = new List<S_UnqualifiedDisposal>();//不合格处置单
                List<S_CompromiseDisposal> lstcompromiseDisposal = new List<S_CompromiseDisposal>();//让步接收单
                List<S_RRDF> lstRRDF = new List<S_RRDF>();//奖励/返还处置单

                List<P_ConsignmentNote> lstLogisticsReceipt = new List<P_ConsignmentNote>(); //物流收货明细

                List<I_Invoice> lstEntity = GetListByKeys(ids); //获取开票主表信息
                foreach (var item in lstEntity)
                {
                    #region 物流开票明细处理

                    var LogisticsInvoiceDetail = _noteApp.GetList(t => t.InvoiceId == item.Id).ToList();
                    LogisticsInvoiceDetail.ForEach(t =>
                    {
                        t.InvoiceId = null;
                        t.MTime = DateTime.Now;
                        t.MUser = user;
                    });
                    lstLogisticsReceipt.AddRange(LogisticsInvoiceDetail);

                    #endregion

                    #region 处置单据处理
                    //处置单据撤回原来状态
                    List<I_InvoiceDisposal> lstDisposal = _disposalApp.GetList(t => t.InvoiceId == item.Id).ToList();//获取开票-处置单信息
                    foreach (var dis in lstDisposal)
                    {
                        if (dis.DocType == "1") //不合格处置单
                        {
                            var data = _unqualifiedApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstUnqualifiedDisposal.Add(data);
                        }
                        else if (dis.DocType == "2") //让步接收单
                        {
                            var data = _compromiseApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "5"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstcompromiseDisposal.Add(data);
                        }
                        else if (dis.DocType == "3" || dis.DocType == "4")
                        {
                            var data = _rrdfApp.GetEntityByKey(dis.DisposalId);
                            data.Id = dis.DisposalId;
                            data.Status = "6"; //更新为已接收
                            data.MTime = DateTime.Now;
                            data.MUser = user;
                            lstRRDF.Add(data);
                        }
                    }
                    #endregion
                }

                DbContext.Ado.BeginTran();//开启事务
                _disposalApp.DbContext = this.DbContext;
                _unqualifiedApp.DbContext = this.DbContext;
                _compromiseApp.DbContext = this.DbContext;
                _rrdfApp.DbContext = this.DbContext;
                _noteApp.DbContext = this.DbContext;

                Delete(lstEntity);//删除物流开票信息
                if (lstUnqualifiedDisposal.Count > 0)//不合格处置单据更新
                {
                    _unqualifiedApp.Update(lstUnqualifiedDisposal);
                }
                if (lstcompromiseDisposal.Count > 0)//让步接收单据状态更新
                {
                    _compromiseApp.Update(lstcompromiseDisposal);
                }
                if (lstRRDF.Count > 0)//奖励/返还处置单状态更新
                {
                    _rrdfApp.Update(lstRRDF);
                }

                _noteApp.Update(lstLogisticsReceipt); //物流开票明细 设置为 未开票状态

                DbContext.Ado.CommitTran();//提交事务

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                //throw ex;
                return false;

            }
        }
        #endregion

        /// <summary>
        /// 发票冲销 过账
        /// </summary>
        /// <param name="lstMain"></param>
        /// <param name="message"></param>
        /// <param name="opUser">过账人</param>
        /// <param name="postTime">过账时间</param>
        /// <param name="revType">冲销类型</param> 03:当月红冲 04：跨月红冲
        /// <param name="credentialType">凭证类型</param>
        /// <returns></returns>
        public bool RevDoPost(List<I_Invoice> lstMain, string revType, DateTime postTime, string opUser, string credentialType, out string message)
        {
            try
            {
                bool IsPosted;
                message = "";
                List<I_Invoice> lstMainEdit = new List<I_Invoice>();
                List<S_UnqualifiedDisposal> lstUnqualifiedDisposal = new List<S_UnqualifiedDisposal>();//不合格处置单
                List<S_CompromiseDisposal> lstcompromiseDisposal = new List<S_CompromiseDisposal>();//让步接收单
                List<S_RRDF> lstRRDF = new List<S_RRDF>();//奖励/返还处置单
                List<S_DebitNotice> lstDebit = new List<S_DebitNotice>(); //扣款通知单
                List<PO_PurchaseReceipt> lstPurchaseReceipt = new List<PO_PurchaseReceipt>(); //WMS采购入库信息
                List<MM_Warehousing> lstOutside = new List<MM_Warehousing>(); //WMS委外入库表
                List<PO_ReturnScanDetailed> lstPOReturn = new List<PO_ReturnScanDetailed>(); //采购退货

                foreach (var main in lstMain)
                {
                    #region 预制发票冲销
                    if (credentialType != "2")
                    {
                        ZFGSRM006 entity = new ZFGSRM006();
                        entity.INV_DOC_NO = main.SAPInvoiceNo;
                        entity.FISC_YEAR = main.PostTime.Value.Year;
                        entity.REASON_REV = revType;
                        if (revType == "04") //跨月
                            entity.PSTNG_DATE = postTime;
                        else
                            entity.PSTNG_DATE = null;

                        var resData = _sap.ZFGSRM006("001", postTime, entity, out IsPosted, out message);
                        if (IsPosted)
                        {
                            main.IsPostedCredit = true;
                            main.PostTimeCredit = postTime;
                            main.PostUserCredit = opUser;
                            main.SAPNoCredit = resData.INV_DOC_NO;
                            main.FISC_YEAR = resData.FISC_YEAR;
                            main.MTime = DateTime.Now;
                            main.MUser = opUser;
                            if (string.IsNullOrEmpty(main.SAPInvoiceNo2) || (!string.IsNullOrEmpty(main.SAPInvoiceNo2) && !string.IsNullOrEmpty(main.SAPNoCredit2)))
                                main.Status = "5";//冲销已完成
                            else
                                main.Status = "6";//收货已冲销

                            lstMainEdit.Add(main);

                            #region 收货明细处理

                            var invoiceDetail = _detailApp.GetList(t => t.ParentId == main.Id && t.PurchaseReceiptQty > 0).ToList();
                            foreach (I_InvoiceDetails detail in invoiceDetail)
                            {
                                if (detail.OrderType == "1") //标准采购收货
                                {
                                    var data = DbContextForWMS.Queryable<PO_PurchaseReceipt>().InSingle(detail.PurchaseReceiptID);
                                    if (data != null)
                                    {
                                        data.ApplySRM = false;
                                        lstPurchaseReceipt.Add(data);
                                    }
                                }
                                else if (detail.OrderType == "2") //委外采购收货
                                {
                                    var data = DbContextForWMS.Queryable<MM_Warehousing>().InSingle(detail.PurchaseReceiptID);
                                    if (data != null)
                                    {
                                        data.ApplySRM = false;
                                        lstOutside.Add(data);
                                    }
                                }
                                else if (detail.OrderType == "3")
                                {
                                    var data2 = DbContextForWMS.Queryable<PO_ReturnScanDetailed>().InSingle(detail.PurchaseReceiptID);
                                    if (data2 != null)
                                    {
                                        data2.ApplySRM = false;
                                        lstPOReturn.Add(data2);
                                    }
                                }
                            }
                            #endregion

                            #region 处置单据处理
                            //处置单据撤回原来状态
                            List<I_InvoiceDisposal> lstDisposal = _disposalApp.GetList(t => t.InvoiceId == main.Id).ToList();//获取开票-处置单信息
                            foreach (var dis in lstDisposal)
                            {
                                if (dis.DocType == "1") //不合格处置单
                                {
                                    var data = _unqualifiedApp.GetEntityByKey(dis.DisposalId);
                                    data.Id = dis.DisposalId;
                                    data.Status = "5"; //更新为已接收
                                    data.MTime = DateTime.Now;
                                    data.MUser = opUser;
                                    lstUnqualifiedDisposal.Add(data);
                                }
                                else if (dis.DocType == "2") //让步接收单
                                {
                                    var data = _compromiseApp.GetEntityByKey(dis.DisposalId);
                                    data.Id = dis.DisposalId;
                                    data.Status = "5"; //更新为已接收
                                    data.MTime = DateTime.Now;
                                    data.MUser = opUser;
                                    lstcompromiseDisposal.Add(data);
                                }
                                else if (dis.DocType == "3" || dis.DocType == "4") //奖励/返还处置
                                {
                                    var data = _rrdfApp.GetEntityByKey(dis.DisposalId);
                                    data.Id = dis.DisposalId;
                                    data.Status = "6"; //更新为已接收
                                    data.MTime = DateTime.Now;
                                    data.MUser = opUser;
                                    lstRRDF.Add(data);
                                }
                                else if (dis.DocType == "5") //扣款通知单
                                {
                                    var data = _debitApp.GetEntityByKey(dis.DisposalId);
                                    data.Id = dis.DisposalId;
                                    data.Status = "5"; //更新为已接收
                                    data.MTime = DateTime.Now;
                                    data.MUser = opUser;
                                    lstDebit.Add(data);
                                }
                            }
                            #endregion
                        }
                        else
                        {
                            return false;
                        }
                    }
                    #endregion

                    #region 贷方凭证冲销
                    if (credentialType != "1")
                    {
                        if (!string.IsNullOrEmpty(main.SAPInvoiceNo2))
                        {
                            #region 接口数据生成

                            ZFGSRM006 entity2 = new ZFGSRM006();
                            entity2.INV_DOC_NO = main.SAPInvoiceNo2;
                            entity2.FISC_YEAR = main.PostTime.Value.Year;
                            entity2.REASON_REV = revType;
                            if (revType == "04") //跨月
                                entity2.PSTNG_DATE = postTime;
                            else
                                entity2.PSTNG_DATE = null;

                            #endregion

                            var resData2 = _sap.ZFGSRM006("001", postTime, entity2, out IsPosted, out message);
                            if (IsPosted)
                            {
                                main.SAPNoCredit2 = resData2.INV_DOC_NO;
                                main.FISC_YEAR = resData2.FISC_YEAR;
                                main.MTime = DateTime.Now;
                                main.MUser = opUser;
                                main.IsPostedCredit = true;
                                main.PostTimeCredit = postTime;
                                main.PostUserCredit = opUser;
                                if (!string.IsNullOrEmpty(main.SAPNoCredit))
                                    main.Status = "5";//冲销已完成
                                else
                                    main.Status = "7";//退货已冲销

                                if (!lstMainEdit.Exists(t => t.Id == main.Id))
                                {
                                    lstMainEdit.Add(main);
                                }
                                #region 退货明细处理

                                var invoiceDetail = _detailApp.GetList(t => t.ParentId == main.Id && t.PurchaseReceiptQty < 0).ToList();
                                foreach (I_InvoiceDetails detail in invoiceDetail)
                                {
                                    if (detail.OrderType == "3") //标准采购退货订单
                                    {
                                        var data = DbContextForWMS.Queryable<PO_ReturnScanDetailed>().InSingle(detail.PurchaseReceiptID);
                                        if (data != null)
                                        {
                                            data.ApplySRM = false;
                                            lstPOReturn.Add(data);
                                        }
                                    }
                                }
                                #endregion
                            }
                            else
                            {
                                return false;
                            }
                        }
                    }
                    #endregion
                }

                DbContext.Ado.BeginTran();//开启事务
                _disposalApp.DbContext = this.DbContext;
                _unqualifiedApp.DbContext = this.DbContext;
                _compromiseApp.DbContext = this.DbContext;
                _rrdfApp.DbContext = this.DbContext;
                _debitApp.DbContext = this.DbContext;

                Update(lstMainEdit);//更新开票信息状态
                if (lstUnqualifiedDisposal.Count > 0)//不合格处置单据更新
                {
                    _unqualifiedApp.Update(lstUnqualifiedDisposal);
                }
                if (lstcompromiseDisposal.Count > 0)//让步接收单据状态更新
                {
                    _compromiseApp.Update(lstcompromiseDisposal);
                }
                if (lstRRDF.Count > 0)//奖励/返还处置单状态更新
                {
                    _rrdfApp.Update(lstRRDF);
                }
                if (lstDebit.Count > 0) //扣款通知单
                {
                    _debitApp.Update(lstDebit);
                }

                //更新WMS采购收货为未使用，
                if (lstPurchaseReceipt.Count > 0)
                {
                    lstPurchaseReceipt.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                if (lstOutside.Count > 0)//委外入库更新状态
                {
                    lstOutside.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                //采购退货
                if (lstPOReturn.Count > 0)
                {
                    lstPOReturn.ForEach(t =>
                    {
                        DbContextForWMS.Updateable(t).ExecuteCommand();
                    });
                }
                DbContext.Ado.CommitTran();//提交事务

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                message = ex.Message;
                return false;
            }
        }

        public Dictionary<String, String> UploadFile(string currentUserUserName)
        {
            string address = "BillScan";
            HttpRequest request = HttpContext.Current.Request;
            //获取参数信息
            string timeSpace = DateTime.Now.ToString("yyyyMMddhhmmss").ToString();
            var files = request.Files[0];
            string fileNameWithOutExtension = Path.GetFileNameWithoutExtension(files.FileName);
            string extension = Path.GetExtension(files.FileName);
            string fileName = fileNameWithOutExtension + timeSpace + extension;

            string newAddress = ConfigurationManager.AppSettings[address]?.ToString();
            string resPath = newAddress + "\\" + fileName;
            string filePath = HttpContext.Current.Server.MapPath("~/") + resPath;
            string dirPath = HttpContext.Current.Server.MapPath("~/") + newAddress;
            if (!Directory.Exists(dirPath))
            {
                DirectoryInfo directoryInfo = new DirectoryInfo(dirPath);
                directoryInfo.Create();
            }
            //对名称进行重命名
            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
            response.Headers.Add("FileName", HttpUtility.UrlEncode(fileName));

            Dictionary<String,String> dic = new Dictionary<string, string>();
            dic.Add("BillScanName", files.FileName);
            dic.Add("BillScanPath", resPath);
            //request.Files[0].SaveAs(filePath);
            MemoryStream memStream = new MemoryStream();

            CopyStream(files.InputStream, memStream);

            String FullPath = Path.Combine(dirPath, fileName);

            FileStream fs = new FileStream(FullPath, FileMode.OpenOrCreate);
            //files[0].SaveAs(FullPath);
            //string hashStr = HashData(memStream, "md5").ToString();
            memStream.WriteTo(fs);
            memStream.Close();
            fs.Close();

            return dic;
        }

        private static void CopyStream(Stream input, Stream output)
        {
            byte[] buffer = new byte[16 * 1024];
            int read;
            while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
            {
                output.Write(buffer, 0, read);
            }
        }

        public ResponseData ExclusionVerificationReceiving(ExclusionVerificationReceivingReq req, Sys_User user)
        {
            var result = new ResponseData();
            try
            {
                var returnScans = DbContextForWMS.Queryable<PO_ReturnScanDetailed>().Where(t => req.returnScanDetailedIDs.Contains(t.ReturnScanDetailedID)).ToList();
                DbContextForWMS.Ado.BeginTran();//开始事务
                foreach (var returnScan in returnScans)
                {
                    if (returnScan.CheckFlag != null && returnScan.CheckFlag.Value == req.checkFlag)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = "该记录已标记过 请勿重复标记！";
                        DbContextForWMS.Ado.RollbackTran();
                        return result;
                    }
                    returnScan.CheckFlag = req.checkFlag;
                    DbContextForWMS.Updateable(returnScan).ExecuteCommand();
                }
                DbContextForWMS.Ado.CommitTran();//提交事务

                result.Code = (int)WMSStatusCode.Success;
                result.Message = "操作成功！";
                return result;
            }
            catch (Exception e)
            {
                DbContextForWMS.Ado.RollbackTran();
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = e.Message;
                return result;
            }
        }
    }
}
