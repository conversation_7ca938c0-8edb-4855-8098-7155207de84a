using AOS.Core.Http;
using AOS.SRM.Application.PXC.LogisticsIOrderApp;
using AOS.SRM.Entity.Basic.ViewModel;
using AOS.SRM.Entity.FO;
using AOS.SRM.Entity.FO.Dto;
using AOS.SRM.Entity.FO.ViewModel;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.SAP;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using HZ.WMS.Application.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.FO
{
    /// <summary>
    /// 对账单App
    /// </summary>
    public class I_ReconciliationApp : BaseApp<I_Reconciliation>
    {
        I_ReturnNoApp returnnoteApp = new I_ReturnNoApp();
        I_ReconciliationDetailApp _detailApp = new I_ReconciliationDetailApp();
        P_LogisticsIOrderApp _logisticsOrderApp = new P_LogisticsIOrderApp();
        P_ConsignmentNoteApp _noteApp = new P_ConsignmentNoteApp();
        Sys_SAPCompanyInfoApp _sap = new Sys_SAPCompanyInfoApp();

        /// <summary>
        /// 
        /// </summary>
        public class sys_user : BaseApp<Sys_User> { };
        sys_user user = new sys_user();
        //供应商查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SupplyerCode"></param>
        /// <returns></returns>
        public List<S_SupplierInfo> GetSupplyInfo(string SupplyerCode)
        {
            var list = DbContext.Queryable<S_SupplierInfo>().Where(t => t.SupplyerCode == SupplyerCode).ToList();
            return list;

        }
        /// <summary>
        /// 获取财务应付会计
        /// </summary>
        /// <returns></returns>
        public List<Sys_User> GetReceiver()
        {
            string sql = @"SELECT *
                                FROM dbo.Sys_User
                                WHERE UserID IN
                                      (
                                          SELECT UserID
                                          FROM dbo.Sys_UserRole
                                          WHERE RoleID = '56C4F169-03AC-4005-8F15-09B384CC7E3D'
                                      )
                                ";
            var list = DbContext.Ado.SqlQuery<Sys_User>(sql).ToList();
            return list;
        }

        /// <summary>
        /// 获取物流对账接收人
        /// </summary>
        /// <returns></returns>
        public List<Sys_User> GetLogisticsReceiver()
        {
            string sql = @"SELECT * FROM dbo.Sys_User WHERE OrganizationID='C417C9D2-F1FB-4F25-86AC-38FF8DBF9A5C'";
            var list = DbContext.Ado.SqlQuery<Sys_User>(sql).ToList();
            return list;
        }


        #region 对账单


        //对账单列表查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="ReconciliationNo"></param>
        /// <param name="SupplyName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="docType"></param>
        /// <param name="Status"></param>
        /// <returns></returns>
        public List<I_Reconciliation> GetReconciliation(Pagination page, string ReconciliationNo, string SupplyName, DateTime StartTime, DateTime EndTime, string docType, string Status)
        {
            var query = this.DbContext.Queryable<I_Reconciliation>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(ReconciliationNo) || t.ReconciliationNo.Contains(ReconciliationNo))
                && (string.IsNullOrEmpty(SupplyName) || t.SupplyName.Contains(SupplyName))
                && (string.IsNullOrEmpty(Status) || t.Status == Convert.ToInt32(Status))
                && t.DocType == docType && t.IsDelete == false);

            page.Total = query.Count();
            var itemPageData = new List<I_Reconciliation>();
            itemPageData = query.ToPageList(page.PageNumber, page.PageSize).OrderByDescending(t => t.CTime).ToList();
            
            return itemPageData;
        }
        //创建对账单
        //对账单号是自动生成的，是年月日加三位流水
        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public I_Reconciliation CreateReconciliation(I_Reconciliation entity, string UserName)
        {
            entity.Status = 1;
            entity.CUser = UserName;
            entity.CTime = DateTime.Now;
            entity.DocType = "1";
            return Insert(entity);
        }

        /// <summary>
        /// 创建物流对账单信息
        /// </summary>
        /// <param name="dto">保存参数</param>
        /// <param name="userCode">用户编码</param>
        /// <returns></returns>
        public void CreateLogisticsReconciliation(LogisticsReconciliationDto dto, string userCode)
        {
            try
            {
                var main = dto.main;
                var detail = dto.detail;

                main.Id = Guid.NewGuid().ToString();
                main.Status = 1;
                main.DocType = "2";//物流对账
                main.CUser = userCode;
                main.BookTotalAmount = detail.Sum(t => t.BookAmount);
                main.SettleTotalAmount = detail.Sum(t => t.SettlementAmout);

                List<P_ConsignmentNote> lstConsignmentNote = new List<P_ConsignmentNote>();
                detail.ForEach(t =>
                {
                    t.Id = Guid.NewGuid().ToString();
                    t.ParentId = main.Id;
                    t.CUser = userCode;

                    #region 生成物流对账过账明细
                    decimal? offsetAmount = t.SettlementAmout - t.BookAmount;//差异金额
                    //物流清单明细
                    var logisticsDetail = DbContext.Queryable<V_FO_LogisticsOrderDetail>().Where(d => d.LogisticsNo == t.LogisticsNo).ToList();
                    logisticsDetail = logisticsDetail.OrderBy(o => o.RowTheoreticalAmount).ToList();//按理论金额排序
                    var theoryTotalAmount = logisticsDetail.Sum(s => s.RowTheoreticalAmount);
                    int i = 0;
                    int postLine = 10;
                    decimal? cycleTotalAmount = 0;
                    foreach (var item in logisticsDetail)
                    {
                        i += 1;
                        P_ConsignmentNote note = new P_ConsignmentNote();
                        note.PostLine = postLine * i; //过账使用  采购凭证项目
                        note.ParentId = main.Id;
                        note.SupplyCode = main.SupplyCode;
                        note.SupplyName = main.SupplyName;
                        note.LogisticsNo = item.LogisticsNo;
                        note.Line = item.Line;
                        note.SaleNo = item.SaleNo;
                        note.SaleLine = item.SaleLine;
                        note.CostCenter = item.CostCenter;
                        note.ItemCode = item.ItemCode;
                        note.ItemName = item.ItemName;
                        note.ItmsGrpCode = item.ItmsGrpCode;
                        note.ItmsGrpName = item.ItmsGrpName;
                        note.Qty = item.Qty;
                        note.Unit = item.Unit;
                        note.DeliverDate = item.DeliverDate;
                        note.RowTheoryAmount = item.RowTheoreticalAmount;
                        if (offsetAmount != 0)
                        {
                            if (i == logisticsDetail.Count)//差异金额最后一笔放到最大金额上（此种方式避免小数点差异）
                            {
                                note.RowActualAmount = note.RowTheoryAmount + (offsetAmount - cycleTotalAmount);
                            }
                            else//差异金额分摊
                            {
                                decimal rowDistributeAmount = Math.Round((decimal)(offsetAmount * (note.RowTheoryAmount / theoryTotalAmount)), 2);
                                note.RowActualAmount = note.RowTheoryAmount + rowDistributeAmount;
                                cycleTotalAmount += rowDistributeAmount;//累计分配金额
                            }
                        }
                        else
                        {
                            note.RowActualAmount = note.RowTheoryAmount;
                        }
                        note.CUser = userCode;
                        lstConsignmentNote.Add(note);
                    }

                    #endregion
                });

                //获取物流订单状态表
                var keys = detail.Select(t => t.LogisticsId).ToList().Distinct().ToArray();
                var lstLogisticsOrder = _logisticsOrderApp.GetListByKeys(keys);
                lstLogisticsOrder.ForEach(t =>
                {
                    t.IsUse = true;
                });


                DbContext.Ado.BeginTran();//开启事务
                _detailApp.DbContext = this.DbContext;
                _logisticsOrderApp.DbContext = this.DbContext;
                _noteApp.DbContext = this.DbContext;

                Insert(main);//物流对账主表
                _detailApp.Insert(detail);//物流对账明细
                _logisticsOrderApp.Update(lstLogisticsOrder);//更新物流订单状态表
                _noteApp.Insert(lstConsignmentNote);//生成物流对账过账明细

                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                throw ex;
            }
        }
        //修改
        /// <summary>
        /// 
        /// </summary>
        /// <param name="Reconciliation"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public int UpdateReconciliation(I_Reconciliation Reconciliation, string id)
        {
            var list = DbContext.Queryable<I_Reconciliation>().Where(t => t.Id == id).ToList().FirstOrDefault();
            Reconciliation.ParentId = list.ParentId;
            Reconciliation.CUser = list.CUser;
            Reconciliation.Status = list.Status;
            Reconciliation.DocType = list.DocType;
            Reconciliation.Email = list.Email;
            Reconciliation.Fax = list.Fax;
            Reconciliation.Phone = list.Phone;
            var item = DbContext.Queryable<MD_AttachmentManagement>().Where(t => t.FileId == list.ParentId).First();
            item.Address = Reconciliation.Path;
            item.FileId = list.ParentId;
            new UploadApp().Update(item);
            return Update(Reconciliation);
        }

        /// <summary>
        /// 修改物流对账单信息
        /// </summary>
        /// <param name="dto">保存参数</param>
        /// <param name="userCode">用户编码</param>
        /// <returns></returns>
        public void UpdateLogisticsReconciliation(LogisticsReconciliationDto dto, string userCode)
        {
            try
            {
                var main = dto.main;
                var detail = dto.detail;
                main.Status = 1;
                main.DocType = "2";//物流对账
                main.CUser = userCode;
                main.CTime = DateTime.Now;
                main.MUser = userCode;
                main.MTime = DateTime.Now;
                main.BookTotalAmount = detail.Sum(t => t.BookAmount);
                main.SettleTotalAmount = detail.Sum(t => t.SettlementAmout);

                var detailDel = _detailApp.GetList(t => t.ParentId == main.Id).ToList();
                var noteDetailDel = _noteApp.GetList(t => t.ParentId == main.Id).ToList();

                List<P_ConsignmentNote> lstConsignmentNote = new List<P_ConsignmentNote>();
                detail.ForEach(t =>
                {
                    t.Id = Guid.NewGuid().ToString();
                    t.ParentId = main.Id;
                    t.CUser = userCode;

                    #region 生成物流对账过账明细
                    decimal? offsetAmount = t.SettlementAmout - t.BookAmount;//差异金额
                    //物流清单明细
                    var logisticsDetail = DbContext.Queryable<V_FO_LogisticsOrderDetail>().Where(d => d.LogisticsNo == t.LogisticsNo).ToList();
                    logisticsDetail = logisticsDetail.OrderBy(o => o.RowTheoreticalAmount).ToList();//按理论金额排序
                    var theoryTotalAmount = logisticsDetail.Sum(s => s.RowTheoreticalAmount);
                    int i = 0;
                    int postLine = 10;
                    decimal? cycleTotalAmount = 0;
                    foreach (var item in logisticsDetail)
                    {
                        i += 1;
                        P_ConsignmentNote note = new P_ConsignmentNote();
                        note.PostLine = postLine * i; //过账使用  采购凭证项目
                        note.ParentId = main.Id;
                        note.SupplyCode = main.SupplyCode;
                        note.SupplyName = main.SupplyName;
                        note.LogisticsNo = item.LogisticsNo;
                        note.Line = item.Line;
                        note.CostCenter = item.CostCenter;
                        note.ItemCode = item.ItemCode;
                        note.ItemName = item.ItemName;
                        note.ItmsGrpCode = item.ItmsGrpCode;
                        note.ItmsGrpName = item.ItmsGrpName;
                        note.Qty = item.Qty;
                        note.Unit = item.Unit;
                        note.DeliverDate = item.DeliverDate;
                        note.RowTheoryAmount = item.RowTheoreticalAmount;
                        if (offsetAmount != 0)
                        {
                            if (i == logisticsDetail.Count)//差异金额最后一笔放到最大金额上（此种方式避免小数点差异）
                            {
                                note.RowActualAmount = note.RowTheoryAmount + (offsetAmount - cycleTotalAmount);
                            }
                            else//差异金额分摊
                            {
                                decimal rowDistributeAmount = Math.Round((decimal)(offsetAmount * (note.RowTheoryAmount / theoryTotalAmount)), 2);
                                note.RowActualAmount = note.RowTheoryAmount + rowDistributeAmount;
                                cycleTotalAmount += rowDistributeAmount;//累计分配金额
                            }
                        }
                        else
                        {
                            note.RowActualAmount = note.RowTheoryAmount;
                        }
                        note.CUser = userCode;
                        lstConsignmentNote.Add(note);
                    }

                    #endregion
                });

                //获取物流订单状态表
                var keys = detail.Select(t => t.LogisticsId).ToList().Distinct().ToArray();
                var lstLogisticsOrder = _logisticsOrderApp.GetListByKeys(keys);
                lstLogisticsOrder.ForEach(t =>
                {
                    t.IsUse = true;
                });


                DbContext.Ado.BeginTran();//开启事务
                _detailApp.DbContext = this.DbContext;
                _logisticsOrderApp.DbContext = this.DbContext;
                _noteApp.DbContext = this.DbContext;

                Update(main);//物流对账主表
                _detailApp.HardDelete(detailDel);//删除原有的
                _detailApp.Insert(detail);//物流对账明细
                _logisticsOrderApp.Update(lstLogisticsOrder);//更新物流订单状态表
                _noteApp.HardDelete(noteDetailDel); //删除原有的
                _noteApp.Insert(lstConsignmentNote);//生成物流对账过账明细

                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                throw ex;
            }
        }

        //修改状态为已接收
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public bool UpdateStatue(string[] ids)
        {
            try
            {
                List<I_Reconciliation> Reconciliation = GetListByKeys(ids);

                foreach (var item in Reconciliation)
                {
                    //只有状态为已提交才能执行方法
                    if (item.Status == 1)
                    {
                        item.ReceiveDate = DateTime.Now;
                        item.Status = 2;
                        Update(Reconciliation);
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                return false;
                throw ex;
            }
        }
        //取消（删除）
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public bool CancelStatue(string[] ids)
        {
            try
            {
                List<I_Reconciliation> Reconciliation = GetListByKeys(ids);
                base.DeleteWithTran(Reconciliation);
                return true;
            }
            catch (Exception ex)
            {
                return false;
                throw ex;
            }

        }
        //驳回
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="userCode"></param>
        /// <returns></returns>
        public bool ReconciliationRejected(string[] ids,string userCode,string errMsg)
        {
            try
            {
                List<I_Reconciliation> Reconciliation = GetListByKeys(ids);

                foreach (var item in Reconciliation)
                {
                    item.Status = 3;
                    item.MUser = userCode;
                    item.MTime = DateTime.Now;
                    item.ErrMsg = errMsg;
                }
                UpdateWithTran(Reconciliation);
                return true;
            }
            catch (Exception ex)
            {
                return false;
                throw ex;
            }

        }

        #region 取消物流对账单
        /// <summary>
        /// 取消物流对账单
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="userCode"></param>
        /// <returns></returns>
        public bool CancelLogisticsStatue(string[] ids, string userCode)
        {
            try
            {
                List<P_PurchaseServerOrderStatus> lstPurchaseServerOrder = new List<P_PurchaseServerOrderStatus>();

                List<I_Reconciliation> Reconciliation = GetListByKeys(ids);

                foreach (var item in Reconciliation)
                {
                    item.Status = 3;
                    item.MUser = userCode;
                    item.MTime = DateTime.Now;

                    //获取物流订单状态表
                    var detail = _detailApp.GetList(t => t.ParentId == item.Id).ToList(); //物流对账明细
                    var keys = detail.Select(t => t.LogisticsId).ToList().Distinct().ToArray(); //物流订单Id
                    var lstLogisticsOrder = _logisticsOrderApp.GetListByKeys(keys);
                    lstLogisticsOrder.ForEach(t =>
                    {
                        t.IsUse = false;
                    });
                    lstPurchaseServerOrder.AddRange(lstLogisticsOrder);

                }


                DbContext.Ado.BeginTran();//开启事务
                _logisticsOrderApp.DbContext = this.DbContext;
                _noteApp.DbContext = this.DbContext;

                Update(Reconciliation);
                _logisticsOrderApp.Update(lstPurchaseServerOrder);//更新物流订单状态表

                DbContext.Ado.CommitTran();

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                return false;
                throw ex;
            }

        }
        #endregion


        /// <summary>
        /// 对账单导出
        /// </summary>
        /// <param name="ReconciliationNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="DocType"></param>
        /// <returns></returns>
        public List<I_Reconciliation> GetAllExportData(string ReconciliationNo, string SupplyCode, string Status, DateTime StartTime, DateTime EndTime, string DocType)
        {
            //var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            //DateTime fromTime = querDateTimes[0];
            //DateTime toTime = querDateTimes[1];
            var itemsData = this.DbContext.Queryable<I_Reconciliation>()
                .Where(t => (t.ReconciliationDate >= StartTime) && (t.ReconciliationDate < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(ReconciliationNo) || t.ReconciliationNo.Contains(ReconciliationNo))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(Status) || t.Status == Convert.ToInt32(Status)))
                .Where(t => t.IsDelete == false && t.DocType == DocType)
                .ToList();
            return itemsData;
        }


        /// <summary>
        /// 过账
        /// </summary>
        /// <param name="ids">主键数组</param>
        /// <param name="postUser">过账认</param>
        /// <param name="postTime">过账时间</param>
        /// <param name="message"></param>
        /// <returns></returns>
        public bool DoPost(string[] ids, string postUser, DateTime? postTime, out string message)
        {  //加销售订单和行号  待加
            try
            {
                List<I_Reconciliation> lstRecUpdate = new List<I_Reconciliation>();
                message = "";
                var lstEntity = GetListByKeys(ids);
                foreach (var entity in lstEntity)
                {
                    var noteList = _noteApp.GetList(t => t.ParentId == entity.Id).ToList();
                    List<P_ConsignmentNote> lstNoteUpdate = new List<P_ConsignmentNote>();
                    foreach (var item in noteList.GroupBy(t => t.LogisticsNo))
                    {
                        #region 数据封装
                        ZFGSRM003 zfgsrm003 = new ZFGSRM003()
                        {
                            BLDAT = DateTime.Now,
                            BUDAT = postTime,
                            BSART = "Z009",//采购
                            LIFNR = entity.SupplyCode,
                            EKORG = "XF10",
                            EKGRP = "A08",
                            BUKRS = "2002"
                        };
                        List<ZFGSRM003_Item> lstZFGSRM003_Item = new List<ZFGSRM003_Item>();
                        foreach (var detail in item)
                        {
                            ZFGSRM003_Item zfgsrm003_Item = new ZFGSRM003_Item()
                            {
                                EBELP = detail.PostLine,
                                KNTTP = "K",
                                PSTYP = "0",
                                //MATNR = detail.ItemCode,
                                TXZ01 = "9994",
                                MENGE = detail.Qty,
                                MEINS = detail.Unit,
                                EEIND = DateTime.Now.ToString("yyyyMMdd"),
                                SAKTO = "7001360000",
                                KOSTL = detail.CostCenter,
                                AUFNR = item.Key,//物流单号？
                                ANLN1 = "",
                                MWSKZ = "J0",
                                NETPR = detail.RowActualAmount,
                                PEINH = "1",
                                MATKL = detail.ItmsGrpCode,
                                LGORT = "",
                                WERKS = "2002",
                                AFNAM = "",
                                VBELN = detail.SaleNo,
                                POSNR = detail.SaleLine,
                                ZTEXT = ""
                            };
                            lstZFGSRM003_Item.Add(zfgsrm003_Item);
                        }
                        zfgsrm003.Items = lstZFGSRM003_Item;
                        #endregion
                        bool isPosted;
                        var sapReturn = _sap.ZFGSRM003("001", postTime.Value, zfgsrm003, out isPosted, out message);
                        if (isPosted)
                        {
                            foreach (var note in noteList)
                            {
                                var re = sapReturn.Find(t => t.PostLine == note.PostLine);
                                if (re != null)
                                {
                                    note.SAPDocNum = re.SAPDocNum;
                                    note.SAPLine = re.SAPLine;
                                    note.SAPMaterialNum = re.SAPMaterialNum;
                                    note.SAPMaterialLine = re.SAPMaterialLine;
                                    note.IsPosted = true;
                                    note.PostTime = postTime;
                                    note.PostUser = postUser;
                                    lstNoteUpdate.Add(note);
                                }
                                else
                                {
                                    message = "采购凭证项目[" + note.PostLine + "]返回SAP凭证失败";
                                    return false;
                                }
                            }
                            entity.Status = 4;
                            var sapDocNum = string.Join(",", lstNoteUpdate.Select(t => t.SAPDocNum).Distinct().ToArray());
                            var sapMaterialNum = string.Join(",", lstNoteUpdate.Select(t => t.SAPMaterialNum).Distinct().ToArray());
                            entity.SAPDocNum = sapDocNum;
                            entity.SAPMaterialNum = sapMaterialNum;
                        }
                        else
                        {
                            return false;
                        }
                    }
                    DbContext.Ado.BeginTran();//开启事务
                    _noteApp.DbContext = this.DbContext;
                    //更新过账信息
                    if (lstNoteUpdate.Count > 0)
                    {
                        _noteApp.Update(lstNoteUpdate);
                        Update(entity);
                    }
                    DbContext.Ado.CommitTran();
                }

                return true;
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                throw ex;
            }
        }

        #endregion

        #region 物流对账单导出查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public List<PXC_ConsignmentNote_Export> GetLogisticsExportData(string[] ids)
        {
            var lstDetail = _detailApp.GetList(t => ids.Contains(t.ParentId)).ToList();
            var arrLogisticsNo = lstDetail.Select(t => t.LogisticsNo).Distinct().ToArray();

            var itemsData = this.DbContext.Queryable<PXC_ConsignmentNote_Export>()
                .Where(t => arrLogisticsNo.Contains(t.LogisticsNo))
                .OrderBy(t => t.CustomerName).OrderBy(t => t.DeliveryDate).ToList();

            return itemsData;
        }
        #endregion


    }
    /// <summary>
    /// 
    /// </summary>
    public class UploadApp : BaseApp<MD_AttachmentManagement>
    {

    };


}
