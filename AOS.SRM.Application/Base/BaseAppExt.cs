using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using AOS.Core.Extensions;
using AOS.SRM.Entity;
using SqlSugar;
using DbType = System.Data.DbType;

namespace AOS.SRM.Application.Base
{
    /// <summary>
    /// 扩展BaseApp基类，增加方法
    /// </summary>
    public static class BaseAppExt
    {
        #region 获取业务单号

        /// <summary>
        /// 调用存储过程，获取唯一业务单号
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="app"></param>
        /// <param name="docType"></param>
        /// <param name="fixedNum"></param>
        /// <returns></returns>
        public static string GetNewDocNum<T>(this BaseApp<T> app, string docType, string fixedNum) where T : BaseEntity, new()
        {
            var pm1 = new SugarParameter("@DocType", docType);
            var pm2 = new SugarParameter("@FixedNum", fixedNum);
            var outPm = new SugarParameter("@BarCode", null, true);
            app.DbContext.Ado.UseStoredProcedure().ExecuteCommand("Proc_WMS_GetNewNum",  pm1, pm2, outPm);
            return outPm.Value + "";
        }

        #endregion

        #region 判断是否为正确的单据号

        /// <summary>
        /// 判断是否为正确的单据号
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="app"></param>
        /// <param name="inputCode"></param>
        /// <returns></returns>
        public static bool IsWmsDocument<T>(this BaseApp<T> app, string inputCode) where T : BaseEntity, new()
        {
            List<string> docFixedPart = app.GetFixedNumList<T>().Where(t => t.Length == 2).ToList();
            string fixedPart = inputCode.ExtractLetterPart();
            return fixedPart.Length == 1 && docFixedPart.Contains(fixedPart) ? true : false;
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 获取配置表中固定前缀
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        private static List<string> GetFixedNumList<T>(this BaseApp<T> app) where T : BaseEntity, new()
        {
            // 直接执行Sql语句
            return app.DbContext.Ado.SqlQuery<string>("SELECT FixedNum FROM [dbo].[MD_NumRules]").ToList();
        }


        /// <summary>
        /// 获取配置表中业务类型
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        private static string GetDocTypeByFixedNum<T>(this BaseApp<T> app, string fixedNum) where T : BaseEntity, new()
        {
            // 直接执行Sql语句
            return app.DbContext.Ado.SqlQuery<string>("SELECT DocType FROM [dbo].[MD_NumRules] WHERE FixedNum =@FixedNum", new SugarParameter("@FixedNum", fixedNum)).FirstOrDefault();
        }

        #endregion

        #region 获取业务单号

        /// <summary>
        /// 批量条码/单号生成,生成规则同存储过程,存储过程生成数量为1,此方法可指定数量
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="app"></param>
        /// <param name="docType"></param>
        /// <param name="fixedNum"></param>
        /// <param name="count">生成条码数量</param>
        /// <returns></returns>
        public static string[] GetNewDocNumExt<T>(this BaseApp<T> app, string docType, string fixedNum, int count = 1) where T : BaseEntity, new()
        {
            string[] result = new string[count];
            //生成单号逻辑:固定+服务器日期+流水
            string sql = @" DECLARE @NN   INT;
                            DECLARE @FlowNum   INT;
                            DECLARE @date   NVARCHAR (20);
                            DECLARE @COUNT   INT;                            
                            SET @COUNT = 0;
                            SET @date = CONVERT (DATE, GETDATE (), 23);
                            SET @BARCODESTRING='';

                            WHILE (@COUNT < @BarCodeCount)
                               BEGIN
                                  IF EXISTS
                                        (SELECT 1
                                         FROM MD_NumRules
                                         WHERE     DocType = @DocType
                                               AND FixedNum = @FixedNum
                                               AND DocDate = @date)
                                     BEGIN
                                        SELECT @NN = NextNumber, @FlowNum = FlowNum
                                        FROM MD_NumRules
                                        WHERE     DocType = @DocType
                                              AND FixedNum = @FixedNum
                                              AND DocDate = @date;
                                        SET @BARCODESTRING=@BARCODESTRING+','+ @FixedNum
                                               + CONVERT (VARCHAR (100), GETDATE (), 112)
                                               + RIGHT (
                                                      REPLICATE ('0', @FlowNum)
                                                    + CAST (@NN AS VARCHAR (10)),
                                                    @FlowNum);
                                        --更新下一编号
                                        SET @NN = @NN + 1;
                                        UPDATE MD_NumRules
                                        SET NextNumber = @NN, MTime = GETDATE ()
                                        WHERE     DocType = @DocType
                                              AND FixedNum = @FixedNum
                                              AND DocDate = @date;
                                     END
                                  ELSE
                                     BEGIN
                                        SET @NN = 1;
                                        SELECT @FlowNum = FlowNum
                                        FROM MD_NumRules
                                        WHERE DocType = @DocType AND FixedNum = @FixedNum;
                                        SET @BARCODESTRING=@BARCODESTRING+','+ @FixedNum
                                               + CONVERT (VARCHAR (100), GETDATE (), 112)
                                               + RIGHT (
                                                      REPLICATE ('0', @FlowNum)
                                                    + CAST (@NN AS VARCHAR (10)),
                                                    @FlowNum);
                                        --更新下一编号
                                        SET @NN = @NN + 1;
                                        UPDATE MD_NumRules
                                        SET NextNumber = @NN, DocDate = @date, MTime = GETDATE ()
                                        WHERE DocType = @DocType AND FixedNum = @FixedNum;
                                     END;

                                  SET @COUNT = @COUNT + 1
                               END;
                               SELECT @BARCODESTRING;";
            SugarParameter barcodeString = new SugarParameter("@BARCODESTRING",null, DbType.String, ParameterDirection.Output, 1000);
            var list = app.DbContext.Ado.SqlQuery<string>(sql, new SugarParameter("@BarCodeCount", count), new SugarParameter("@FixedNum", fixedNum), new SugarParameter("@DocType", docType), barcodeString)?.ToArray();
            return barcodeString.Value?.ToString()?.TrimStart(',')?.Split(',');
        }
       
        #endregion
    }

    /// <summary>
    /// 单据类型
    /// </summary>
    public class DocType
    {
        /// <summary>
        /// 对账单
        /// </summary>
        public const string IN = "IN";

        /// <summary>
        /// 
        /// </summary>
        public const string FO = "FO";

        /// <summary>
        /// 
        /// </summary>
        public const string PO = "PO";

        /// <summary>
        /// 
        /// </summary>
        public const string RO = "RO";

        /// <summary>
        /// 
        /// </summary>
        public const string LX = "LX";
    }

    /// <summary>
    /// 固定前缀
    /// </summary>
    public class DocFixedNumDef
    {
        /// <summary>
        /// 
        /// </summary>
        public const string InvoiceNo = "IN";

        /// <summary>
        /// 
        /// </summary>
        public const string ReturnNo = "R";

        /// <summary>
        /// 
        /// </summary>
        public const string EquipPurchase = "";

        /// <summary>
        /// 品质
        /// </summary>
        public const string SHM_UnqualifiedNoticeNum = "PZ";

        /// <summary>
        /// 报检单
        /// </summary>
        public const string PO_Make_Inspection_Report = "I";

        /// <summary>
        /// 销售订单
        /// </summary>
        public const string SD_OrderNum = "SD";

        /// <summary>
        /// 退货订单
        /// </summary>
        public const string SD_ReturnOrderNum = "RD";

        /// <summary>
        /// 售后订单
        /// </summary>
        public const string SD_AfterOrderNum = "AS";

        /// <summary>
        /// 部件订单
        /// </summary>
        public const string SD_PartsOrderNum = "PD";

        /// <summary>
        /// 售后主机订单
        /// </summary>
        public const string Host_Order = "HD";

    }

    /// <summary>
    /// 格式化处理器
    /// </summary>
    public static class FormatProcessor
    {
        /// <summary>
        /// 查询/导出 时间条件格式化
        /// </summary>
        /// <param name="dateTimes"></param>
        /// <param name="isDateTime"></param>
        /// <returns></returns>
        public static DateTime[] QueryDateTimesFormat(DateTime[] dateTimes, bool isDateTime = false)
        {
            DateTime fromTime = new DateTime(2020, 1, 1).Date;
            DateTime toTime = DateTime.Now.AddDays(1).Date;
            if (dateTimes != null && dateTimes.Length >= 2)
            {
                fromTime = dateTimes[0].Date;
                toTime = dateTimes[1].AddDays(1).Date;
                if (isDateTime)
                {
                    fromTime = dateTimes[0];
                    toTime = dateTimes[1].AddDays(1);
                }
            }
            return new DateTime[] { fromTime, toTime };
        }
    }
}
