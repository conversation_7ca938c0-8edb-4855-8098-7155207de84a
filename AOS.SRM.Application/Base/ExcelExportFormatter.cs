using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using System;
using System.Collections.Generic;
using System.Linq;


namespace AOS.SRM.Application.Base
{
    /// <summary>
    /// 全局导出数据格式化器
    /// </summary>
    public static class ExcelExportFormatter
    {
        /// <summary>
        /// 性别格式化器
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object GenderFormatter(object value,object Item)
        {
            return 1.Equals(value) ? "男" : "女";
        }

        /// <summary>
        /// 性别格式化器
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object IsConsignFormatter(object value, object Item)
        {
            return "1".Equals(value) ? "寄售标签" : "非寄售标签";
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object IsEnableFormatter(object value, object Item)
        {
            return true.Equals(value) ? "可用" : "冻结";
        }

        /// <summary>
        /// True/False 转 -》是/否 格式化器
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object IsOrNoFormatterForBoolean(object value, object Item)
        {
            return true.Equals(value) ? "是" : "否";
        }

        ///// <summary>
        ///// 
        ///// </summary>
        ///// <param name="value"></param>
        ///// <param name="Item"></param>
        ///// <returns></returns>
        //public static object StatusFormatter(object value, object Item)
        //{
        //    return true.Equals(value) ? "可用" : "冻结";
        //}

        /// <summary>
        /// 组织机构格式化器
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object OrganizationFormatter(object value, object Item)
        {
            List<Sys_Organization> listOrga = new Sys_OrganizationApp().GetList().ToList();
            return listOrga.Where(t=>t.OrganizationID==value?.ToString()).FirstOrDefault()?.OrganizationDesc;
        }

        /// <summary>
        /// 采购交货状态
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object StatusFormatter_PO003(object value, object Item)
        {
            List<Sys_Dictionary> listOrga = new Sys_DictionaryApp().GetList().ToList();
            return listOrga.Where(t => t.EnumKey.ToString() == value?.ToString()&&t.TypeCode== "PO003").FirstOrDefault()?.EnumValue;
        }
        /// <summary>
        /// 是/否 格式化器
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object IsOrNoFormatter(object value, object Item)
        {
            return 1.Equals(value) ? "是" : "否";
        }

        /// <summary>
        /// 订单类型 格式化器
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object IsOrNoForOrderType(object value, object Item)
        {
            return "2".Equals(value) ? "部件" : "售后";
        }

        /// <summary>
        /// 是/否 格式化器
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object IStatusFormatter(object value, object Item)
        {
            return 1.Equals(value) ? "合格" : "不合格";
        }
        /// <summary>
        /// 时间格式化器
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object DateTimeFormatter(object value, object Item)
        {
            return DateTime.Parse(value.ToString()).ToString("yyyy-MM-dd HH:mm:ss");
        }
    }
}
