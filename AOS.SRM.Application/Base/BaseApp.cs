using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using AOS.Core.Http;
using AOS.Core.Utilities;
using AOS.SRM.Entity;
using SqlSugar;

namespace AOS.SRM.Application.Base
{
    /// <summary>
    /// Ado.net 并不支持并行事务，所以批量操作都单独重载一个带有事务参数的方法
    /// 外层添加事务的情况下，事务内处理时请不要使用带有Tran结尾的方法
    ///  where T : BaseEntity, new()
    ///
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public class BaseApp<T> : ContentBase where T : BaseEntity, new()
    {
        #region 添加(修正完毕)

        #region  单个实体添加

        /// <summary>
        /// 添加单实体，返回成功插入的实体
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public T Insert(T entity)
        {
            if (string.IsNullOrEmpty(entity.GetPrimaryKeyValue()?.ToString()))
            {
                entity.SetDefaultValueToPrimaryKey();
            }

            //获取实体自定义特性字段
            Type objType = entity.GetType();
            //取属性上的自定义特性
            foreach (PropertyInfo propInfo in objType.GetProperties())
            {
                object[] objAttrs = propInfo.GetCustomAttributes(typeof(UniqueCodeAttribute), true);
                if (objAttrs.Length > 0)
                {
                    UniqueCodeAttribute attr = objAttrs[0] as UniqueCodeAttribute;
                    if (attr != null)
                    {
                        //自定义特性唯一性校验
                        T existT = GetFirstEntityByFieldValue(propInfo.Name, propInfo.GetValue(entity));
                        if (existT != null)
                        {
                            //编号重复，请输入新的正确编号
                            throw new Exception("Common.ExistedCode");
                        }
                    }
                }
            }
            return DbContext.Insertable(entity).ExecuteReturnEntity();
        }

        #endregion

        #region 批量插入

        /// <summary>
        /// 批量插入，不带有事务控制
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int Insert(List<T> entities)
        {
            if (entities == null || entities.Count == 0) return 0;
            try
            {
                entities.ForEach(t =>
                {
                    if (string.IsNullOrEmpty(t.GetPrimaryKeyValue()?.ToString()))
                    {
                        t.SetDefaultValueToPrimaryKey();
                    }
                });
                DbContext.Insertable(entities).ExecuteCommand();
                return entities.Count;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        #endregion

        #region 批量插入带事务
        /// <summary>
        /// 批量插入带有事务处理
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int InsertWithTran(List<T> entities)
        {
            int iCount = 0;
            try
            {
                DbContext.Ado.BeginTran();
                foreach (T entity in entities)
                {
                    if (string.IsNullOrEmpty(entity.GetPrimaryKeyValue()?.ToString()))
                    {
                        entity.SetDefaultValueToPrimaryKey();
                    }
                    DbContext.Insertable<T>(entity).ExecuteCommand();
                    iCount++;
                }
                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                iCount = 0;
                throw new Exception(ex.Message);
            }

            return iCount;
        }

        #endregion

        #region 大数据批量插入

        /// <summary>
        /// 批量快速添加，不带有事务控制
        /// </summary>
        /// <param name="entities">The entities.</param>
        public void BulkInsert(List<T> entities)
        {
            base.BulkInsert(entities);
        }

        #endregion

        #endregion

        #region 更新(完毕)

        /// <summary>
        /// 单个实体更新
        /// </summary>
        /// <param name="entity"></param>
        public int Update(T entity)
        {
            return this.DbContext.Updateable(entity).ExecuteCommand();
        }

        /// <summary>
        /// 多个实体更新
        /// </summary>
        /// <param name="entities"></param>
        /// <returns>返回更新行数</returns>
        public int Update(List<T> entities)
        {
            var iCount = 0;
            try
            {
                foreach (T entity in entities)
                {
                    this.DbContext.Updateable(entity).ExecuteCommand();
                    iCount++;
                }
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        internal object GetPageList<SysUser>(Pagination page, Expression<Func<SysUser, bool>> condition)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 多个实体更新
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int UpdateWithTran(List<T> entities)
        {
            //var iCount = 0;
            //try
            //{
            //    this.DbContext.Ado.BeginTran();
            //    foreach (T entity in entities)
            //    {
            //        this.DbContext.Update(entities);
            //        iCount++;
            //    }
            //    this.DbContext.Ado.CommitTran();
            //}
            //catch (Exception ex)
            //{
            //    iCount = 0;
            //    this.DbContext.RollbackTran();
            //    throw new Exception(ex.Message);
            //}
            //return iCount;

            var iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                foreach (T entity in entities)
                {
                    if (string.IsNullOrEmpty(entity.GetPrimaryKeyValue()?.ToString()))
                    {
                        entity.SetDefaultValueToPrimaryKey();
                    }

                    this.DbContext.Updateable(entity).ExecuteCommand();
                    iCount++;
                }
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 根据条件单个实体更新
        /// <para>如：Update(entity,u =>u.id==1);</para>
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public int Update(T entity, Expression<Func<T, bool>> condition)
        {
            return this.DbContext.Updateable(entity).Where(condition).ExecuteCommand();

        }

        /// <summary>
        /// 实现按需要只更新部分更新
        /// <para>如：Update(u =>u.Id==1,u =>new User{Name="ok"});</para>
        /// </summary>
        /// <param name="where">The where.</param>
        /// <param name="entity">The entity.</param>
        public int Update(Expression<Func<T, bool>> where, Expression<Func<T, T>> entity)
        {
            return DbContext.Updateable<T>(entity).Where(where).ExecuteCommand();
        }




        #endregion

        #region 逻辑删除(完毕)

        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public int Delete(T obj)
        {
            // DbContext.TrackEntity(obj);
            obj.IsDelete = true;
            obj.DTime = DateTime.Now;
            return DbContext.Updateable<T>(obj).ExecuteCommand();
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int Delete(List<T> entities)
        {
            int iCount = 0;
            try
            {
                entities.ForEach(delegate (T t)
                {
                    // DbContext.TrackEntity(t);
                    t.IsDelete = true;
                    t.DTime = DateTime.Now;
                    DbContext.Updateable(t).ExecuteCommand();
                    iCount++;
                });
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 删除多个实体
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int DeleteWithTran(List<T> entities)
        {
            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                entities.ForEach(delegate (T t)
                {
                    // DbContext.TrackEntity(t);
                    t.IsDelete = true;
                    t.DTime = DateTime.Now;
                    DbContext.Updateable(t).ExecuteCommand();
                    iCount++;
                });
                this.DbContext.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 删除多个实体
        /// </summary>
        /// <param name="entities">待实体列表</param>
        /// <param name="deleteUser">删除者唯一账号</param>
        /// <returns></returns>
        public int Delete(List<T> entities, string deleteUser)
        {
            int iCount = 0;
            try
            {
                entities.ForEach(delegate (T t)
                {
                    // DbContext.TrackEntity(t);
                    t.IsDelete = true;
                    t.DTime = DateTime.Now;
                    t.DUser = deleteUser;
                    DbContext.Updateable(t).ExecuteCommand();
                    iCount++;
                });

            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }




        /// <summary>
        ///
        /// </summary>
        /// <param name="deleteUser"></param>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int DeleteWithTran(List<T> entities, string deleteUser)
        {
            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                entities.ForEach(delegate (T t)
                {
                    // DbContext.TrackEntity(t);
                    t.IsDelete = true;
                    t.DTime = DateTime.Now;
                    t.DUser = deleteUser;
                    DbContext.Updateable(t).ExecuteCommand();
                    iCount++;
                });
                this.DbContext.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }


        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="key"></param>
        /// <param name="deleteUser"></param>
        /// <returns></returns>
        public int DeleteByKey(object key, string deleteUser)
        {
            try
            {
                T obj = DbContext.Queryable<T>().InSingle(key);
                // T obj = DbContext.QueryByKey<T>(key, true);
                // DbContext.TrackEntity(obj);

                obj.IsDelete = true;
                obj.DUser = deleteUser;
                obj.DTime = DateTime.Now;

                return DbContext.Updateable(obj).ExecuteCommand();
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }

        }

        /// <summary>
        /// 根据主键数组逻辑删除（必须是主键，且单一主键）
        /// </summary>
        /// <param name="keys">主键数组</param>
        /// <param name="deleteUser">删除者账号</param>
        /// <returns></returns>
        public int DeleteByKeys(object[] keys, string deleteUser)
        {

            int iCount = 0;
            try
            {
                foreach (object key in keys)
                {
                    DeleteByKey(key, deleteUser);
                    iCount++;
                }
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }

            return iCount;
        }

        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="keys">主键数组</param>
        /// <param name="deleteUser">删除者账号</param>
        /// <returns></returns>
        public int DeleteByKeysWithTran(object[] keys, string deleteUser)
        {

            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                foreach (object key in keys)
                {
                    DeleteByKey(key, deleteUser);
                    iCount++;
                }
                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }

            return iCount;
        }

        /// <summary>
        /// 根据条件删除
        /// </summary>
        /// <param name="where">删除条件</param>
        /// <param name="deleteUser">删除者账号</param>
        /// <returns></returns>
        public int Delete(Expression<Func<T, bool>> where, string deleteUser)
        {
            return DbContext.Updateable(new T() { IsDelete = true, DTime = DateTime.Now, DUser = deleteUser }).Where(where).ExecuteCommand();
        }



        #endregion

        #region 物理删除(完毕)

        /// <summary>
        ///
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public int HardDelete(T entity)
        {
            return this.DbContext.Deleteable(entity).ExecuteCommand();
        }

        /// <summary>
        /// 物理删除
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns></returns>
        public int HardDelete(List<T> entities)
        {
            int iCount = 0;
            try
            {
                entities.ForEach(delegate (T entity)
                {
                    this.DbContext.Deleteable(entity).ExecuteCommand();
                    iCount++;
                });
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 物理删除
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns></returns>
        public int HardDeleteWithTran(List<T> entities)
        {
            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                entities.ForEach(delegate (T entity)
                {
                    this.DbContext.Deleteable(entity).ExecuteCommand();
                    iCount++;
                });
                this.DbContext.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 根据主键物理删除
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public int HardDeleteByKey(object key)
        {
            return this.DbContext.Deleteable<T>(key).ExecuteCommand();
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        public int HardDeleteByKeys(object[] keys)
        {
            int iCount = 0;
            try
            {
                foreach (object key in keys)
                {
                    HardDeleteByKey(key);
                    iCount++;
                }
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }


        /// <summary>
        ///
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        public int HardDeleteByKeysWithTran(object[] keys)
        {
            int iCount = 0;
            try
            {
                this.DbContext.Ado.BeginTran();
                foreach (object key in keys)
                {
                    HardDeleteByKey(key);
                    iCount++;
                }

                this.DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                iCount = 0;
                this.DbContext.Ado.RollbackTran();
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        public int HardDelete(Expression<Func<T, bool>> where)
        {
            return this.DbContext.Deleteable<T>().Where(where).ExecuteCommand();
        }

        #endregion

        #region 获取单个实体(完毕)

        /// <summary>
        /// 逻辑删除
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        public T GetEntityByKey(object key)
        {
            T entity = DbContext.Queryable<T>().InSingle(key);
            return entity==null || entity.IsDelete ? null : entity;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        public T GetFirstEntity(Expression<Func<T, bool>> where)
        {
            return this.DbContext.Queryable<T>().Where(where).Where(t => t.IsDelete == false).ToList().FirstOrDefault();
        }

        #endregion

        #region 是否存在

        /// <summary>
        /// 是否存在
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public bool Any(Expression<Func<T, bool>> condition)
        {
            var query = DbContext.Queryable<T>()
                    .Where(x => x.IsDelete == false)   //排除已经逻辑删除的记录
                    .Any(condition);
            return query;
        }


        #endregion

        #region 获取列表

        /// <summary>
        /// 按条件查询，默认按创建时间排序
        /// </summary>
        /// <param name="condition"></param>
        /// <param name="orderBy"></param>
        /// <returns></returns>
        public ISugarQueryable<T> GetList(Expression<Func<T, bool>> condition, string orderBy = null)
        {
            if (string.IsNullOrEmpty(orderBy))
            {
                orderBy = "CTime desc";
            }
            else
            {
                orderBy = orderBy.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var query = DbContext.Queryable<T>()
                    .Where(x => x.IsDelete == false)   //排除已经逻辑删除的记录
                    .Where(condition)
                    .OrderBy(orderBy);

            return query;
        }

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<T> GetList()
        {
            return DbContext.Queryable<T>()
                    .Where(x => x.IsDelete == false);   //排除已经逻辑删除的记录
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="keys"></param>
        /// <returns></returns>
        public List<T> GetListByKeys(object[] keys)
        {
            List<T> list = new List<T>();
            foreach (object key in keys)
            {
                T item = GetEntityByKey(key);
                if (item != null)
                {
                    list.Add(item);
                }

            }
            //return DbContext.Queryable<T>(t=>keys.Contains(t.))

            return list;
        }


        #endregion

        #region 获取分页列表

        /// <summary>
        ///
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <param name="lockType"></param>
        /// <returns></returns>
        public List<T> GetPageList(Pagination page, Expression<Func<T, bool>> condition ,string lockType)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }

            var query = DbContext.Queryable<T>()
                .With(lockType)
                .Where(x => x.IsDelete == false)
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 获取分页数据，默认按照创建时间降序
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public List<T> GetPageList(Pagination page, Expression<Func<T, bool>> condition)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }

            var query = DbContext.Queryable<T>()
                .Where(x => x.IsDelete == false)
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        /// 获取分页数据，默认按照创建时间降序
        /// </summary>
        /// <param name="page"></param>
        /// <returns></returns>
        public List<T> GetPageList(Pagination page)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var query = DbContext.Queryable<T>()
                .Where(x => x.IsDelete == false)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }




        #endregion

        #region 获取列表包含已删除数据

        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        public ISugarQueryable<T> GetAllList()
        {
            return DbContext.Queryable<T>();
        }


        /// <summary>
        ///
        /// </summary>
        /// <param name="condition"></param>
        /// <returns></returns>
        public ISugarQueryable<T> GetAllList(Expression<Func<T, bool>> condition)
        {
            var query = DbContext.Queryable<T>()
                    .Where(condition);
            return query;
        }



        #endregion

        #region 获取分页列表（包含删除数据）

        /// <summary>
        ///
        /// </summary>
        /// <param name="page"></param>
        /// <param name="condition"></param>
        /// <returns></returns>
        public List<T> GetAllPageList(Pagination page, Expression<Func<T, bool>> condition)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var query = DbContext.Queryable<T>()
                .Where(condition)
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="page"></param>
        /// <returns></returns>
        public List<T> GetAllPageList(Pagination page)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var query = DbContext.Queryable<T>()
                .OrderBy(page.Sort);

            int total = query.Count();
            page.Total = total;
            if (page.PageSize > total)
            {
                return query.ToPageList(page.PageNumber, total);
            }
            return query.ToPageList(page.PageNumber, page.PageSize);
        }

        #endregion

        #region 存储过程

        /// <summary>
        /// 执行存储过程返回实体列表
        /// </summary>
        /// <param name="procName"></param>
        /// <param name="cmdType"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        ///// <typeparam name="T"></typeparam>
        public object SqlQuery(string procName, CommandType cmdType, params SugarParameter[] parameters)
        {
            return this.DbContext.Ado.UseStoredProcedure().SqlQuery<object>(procName, parameters).ToList();
        }





        #endregion

        #region 通过指定字段和值，作为查询条件，获取实体

        /// <summary>
        /// 通过指定字段和值，作为查询条件，获取实体
        /// </summary>
        /// <param name="field"></param>
        /// <param name="value"></param>
        /// <returns></returns>
        public T GetFirstEntityByFieldValue(string field, object value)
        {
            return this.DbContext.Ado.SqlQuery<T>("select * from " + typeof(T).Name + " where IsDelete=0 and " + field + "=@value", new SugarParameter("@value", value)).FirstOrDefault();
        }

        #endregion

        #region 通过指定字段和值，作为查询条件，获取实体

        /// <summary>
        /// 通过指定字段和值，作为查询条件，获取实体
        /// </summary>
        /// <param name="fields"></param>
        /// <param name="values"></param>
        /// <returns></returns>
        public T GetFirstEntityByFieldsValues(string[] fields, object[] values)
        {
            var paramObj = new ExpandoObject() as IDictionary<string, object>;
            StringBuilder sb = new StringBuilder("SELECT * FROM ");
            sb.Append(typeof(T).Name).Append(" WHERE IsDelete = 0 ");

            for (int i = 0; i < fields.Length; i++)
            {
                string field = fields[i];
                sb.Append(" AND ").Append(field).Append(" = @").Append(field);
                paramObj.Add(field, values[i]);
            }

            return this.DbContext.Ado.SqlQuery<T>(sb.ToString(), paramObj).FirstOrDefault();
        }

        #endregion

        #region 返回DataTable扩展
        /// <summary>
        /// 返回DataTable扩展
        /// </summary>
        /// <param name="cmdText"></param>
        /// <param name="objParams"></param>
        /// <returns></returns>
        public DataTable ExecuteDataTable(string cmdText,object objParams)
        {
            return DbContext.Ado.GetDataTable(cmdText, objParams);
        }


        #endregion

        #region List转换成DataTable

        /// <summary>
        /// Convert a List{T} to a DataTable.
        /// </summary>
        public DataTable ToDataTable(List<T> items)

        {
            var tb = new DataTable(typeof(T).Name);

            PropertyInfo[] props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            string colDescription = "";
            foreach (PropertyInfo prop in props)
            {
                Type t = GetCoreType(prop.PropertyType);
                colDescription = AttributeUtil.GetPropertyDescriptionAttributeValue<T>(prop.Name);
                tb.Columns.Add(colDescription, t);
                colDescription = "";
            }
            foreach (T item in items)
            {
                var values = new object[props.Length];
                for (int i = 0; i < props.Length; i++)
                {
                    values[i] = props[i].GetValue(item, null);
                }
                tb.Rows.Add(values);
            }
            return tb;
        }

        /// <summary>
        /// Determine of specified type is nullable
        /// </summary>
        public static bool IsNullable(Type t)
        {
            return !t.IsValueType || (t.IsGenericType && t.GetGenericTypeDefinition() == typeof(Nullable<>));
        }

        /// <summary>
        /// Return underlying type if type is Nullable otherwise return the type
        /// </summary>
        public static Type GetCoreType(Type t)
        {
            if (t != null && IsNullable(t))
            {
                if (!t.IsValueType)
                {
                    return t;
                }
                else
                {
                    return Nullable.GetUnderlyingType(t);
                }
            }
            else
            {
                return t;
            }
        }

        #endregion

        /// <summary>
        /// 执行SQL分页查询
        /// </summary>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数</param>
        /// <param name="page">分页参数</param>
        /// <returns>分页结果</returns>
        public List<T> GetPageListBySql(string sql, object parameters, Pagination page)
        {
            try
            {
                int totalCount = 0;
                var result = DbContext.Ado.SqlQuery<T>(sql, parameters);
                totalCount = result.Count;
                page.Total = totalCount;
                
                // 应用分页
                var pagedResult = result.Skip((page.PageNumber - 1) * page.PageSize).Take(page.PageSize).ToList();
                return pagedResult;
            }
            catch (Exception ex)
            {
                throw new Exception("执行SQL分页查询出错：" + ex.Message, ex);
            }
        }

    }
}