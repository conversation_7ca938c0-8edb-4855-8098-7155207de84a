using System.Configuration;
using AOS.Core.Security;

namespace AOS.SRM.Application.Base
{
    /// <summary>
    /// 
    /// </summary>
    public class DbConfig
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="dbK<PERSON>"></param>
        /// <returns></returns>
        public static string GetDbConnectionString(string dbKey)
        {
            return DES.Decrypt(ConfigurationManager.ConnectionStrings[dbKey].ConnectionString);
        }

        /// <summary>
        /// Oracle数据库连接字符串
        /// </summary>
        /// <param name="dbKeyForOracle">Oracle数据库连接配置键名</param>
        /// <returns>Oracle连接字符串</returns>
        public static string GetDbConnectionStringForOracle(string dbKeyForOracle = "DbConnectionForOracle")
        {
            return ConfigurationManager.ConnectionStrings[dbKeyForOracle].ConnectionString;
        }
    }
}