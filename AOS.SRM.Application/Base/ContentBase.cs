using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using AOS.Core.Logging;
using AOS.Core.Security;
using AOS.SRM.Entity;
using SqlSugar;

namespace AOS.SRM.Application.Base
{
    /// <summary>
    /// 
    /// </summary>
    public abstract class ContentBase : IDisposable
    {
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (this._dbContextSqlSugar != null)
            {
                this._dbContextSqlSugar.Dispose();
            }
            this.Dispose(true);
        }
        /// <summary>
        /// 释放资源结果
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
        }

        /// <summary>
        /// 
        /// </summary>
        public class T : BaseEntity { }

        #region 连接数据库

        SqlSugarClient _dbContextSqlSugar;

        private string _dbKey;

        /// <summary>
        /// 
        /// </summary>
        protected ContentBase()
        {
            
            this._dbKey = "DbConnection";
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dbKey"></param>
        protected ContentBase(string dbKey)
        {
            this._dbKey = dbKey;
            
            
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public SqlSugarClient DbContext
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = DbConfig.GetDbConnectionString(this._dbKey),
                        DbType = DbType.SqlServer,
                        IsAutoCloseConnection = true
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK
                            Console.WriteLine(sql);

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 1500;
                    });
                return Db;
            }
            set { this._dbContextSqlSugar = value; }
        }
        
        /* 判断是否执行的查询语句 */
        private bool IsQuerySql(string sql)
        {
            string result = sql.Trim();
            // 假设 SELECT、SHOW、DESCRIBE、EXPLAIN 为查询语句
            string[] queryKeywords = { "SELECT", "SHOW", "DESCRIBE", "EXPLAIN" };

            // 使用无大小写的比较方式判断 SQL 是否以查询关键词开头
            foreach (var keyword in queryKeywords)
            {
                if (result.StartsWith(keyword, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }
        
        /* 获取操作人 */
        private string getCurrentAccount()
        {
            var request = HttpContext.Current.Request;
            var token = request.Headers.GetValues("X-Token");
            var account = "";
            if (token != null && token.Count() > 0)
            {
                account = TokenUtil.GetUid(token[0]);
            }

            return account;
        }
        
        #endregion

        #region 连接SAP中间库

        SqlSugarClient _dbContextForSAP;

        private string _dbKeyForSAP = "DbConnectionForSAP";

        /// <summary>
        /// 构造函数-SAP中间库
        /// </summary>
        public SqlSugarClient DbContextForSAP
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = DbConfig.GetDbConnectionString(this._dbKeyForSAP),
                        DbType = DbType.SqlServer,
                        IsAutoCloseConnection = true
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 1500;
                    });
                return Db;
            }
            set { this._dbContextForSAP = value; }
        }

        #endregion
        
        #region 连接WMS中间库

        SqlSugarClient _dbContextForWMS;

        private string _dbKeyForWMS = "DbConnectionForWMS";

        /// <summary>
        /// 构造函数-SAP中间库
        /// </summary>
        public SqlSugarClient DbContextForWMS
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = DbConfig.GetDbConnectionString(this._dbKeyForWMS),
                        DbType = DbType.SqlServer,
                        IsAutoCloseConnection = true
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.SqlServer,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 1500;
                    });
                return Db;
            }
            set { this._dbContextForWMS = value; }
        }

        #endregion
        
        #region 连接Oracle数据库

        SqlSugarClient _dbContextForOracle;

        private string _dbKeyForOracle = "DbConnectionForOracle";

        /// <summary>
        /// 构造函数-Oracle数据库
        /// </summary>
        public SqlSugarClient DbContextForOracle
        {
            get
            {
                SqlSugarClient Db = new SqlSugarClient(new ConnectionConfig()
                    {
                        ConnectionString = DbConfig.GetDbConnectionStringForOracle(this._dbKeyForOracle),
                        DbType = DbType.Oracle,
                        IsAutoCloseConnection = true,
                        InitKeyType = InitKeyType.Attribute,
                        MoreSettings = new ConnMoreSettings
                        {
                            IsAutoRemoveDataCache = true,
                            IsAutoToUpper=false
                        }
                    },
                    db =>
                    {
                        //5.1.3.24统一了语法和SqlSugarScope一样，老版本AOP可以写外面

                        db.Aop.OnLogExecuted = (sql, pars) =>
                        {
                            // Console.WriteLine(sql);//输出sql,查看执行sql 性能无影响

                            //获取原生SQL推荐 5.1.4.63  性能OK

                            bool isQuery = IsQuerySql(sql);
                            TimeSpan sqlTime = db.Ado.SqlExecutionTime;
                            if (!isQuery)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogInfo("Oracle数据操作记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                           sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                           "\n参数列表：\n" + pars);
                            }
                            else if (sqlTime.Seconds > 10)
                            {
                                string nativeSql = UtilMethods.GetNativeSql(sql, pars);
                                LogHelper.Instance.LogDebug("Oracle慢查询记录 - 操作人：" + getCurrentAccount() + " - 用时：" +
                                                            sqlTime.Milliseconds + " ms" + "\nSQL：" + nativeSql +
                                                            "\n参数列表：\n" + pars);
                            }

                            //获取无参数化SQL 对性能有影响，特别大的SQL参数多的，调试使用
                            //UtilMethods.GetSqlString(DbType.Oracle,sql,pars)
                        };

                        //注意多租户 有几个设置几个
                        //db.GetConnection(i).Aop

                        //设置超时时间 单位 秒
                        db.Ado.CommandTimeOut = 1500;
                    });
                return Db;
            }
            set { this._dbContextForOracle = value; }
        }

        #endregion


        /// <summary>
        /// 批量插入
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="entities"></param>
        public void BulkInsert<T>(List<T> entities)
        {

            DbContext.Insertable(entities).ExecuteCommand();
        }
    }
}
