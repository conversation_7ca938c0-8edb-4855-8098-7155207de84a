using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Application.Base
{
    /// <summary>
    /// 语言包固定目录库路径定义
    /// </summary>
    public class LanguagePackagePath
    {
        /// <summary>
        /// 共同部分路径
        /// </summary>
        public const string COMMON_PATH = "common";

        /// <summary>
        /// 
        /// </summary>
        public const string ROUTE_PATH = "route";

        /// <summary>
        /// 组件路径
        /// </summary>
        public const string COMPONENTS_PATH = "components";

        /// <summary>
        /// UI路径
        /// </summary>
        public const string UI_PATH = "ui";

        /// <summary>
        /// API设计消息路径
        /// </summary>
        public const string API_PATH = "api";

    }
}
