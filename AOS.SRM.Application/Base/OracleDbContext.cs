using System;
using System.Configuration;
using SqlSugar;

namespace AOS.SRM.Application.Base
{
    /// <summary>
    /// Oracle数据库上下文
    /// </summary>
    public class OracleDbContext
    {
        private static string _connectionString = ConfigurationManager.ConnectionStrings["DbConnectionForOracle"]?.ConnectionString;
        
        private static SqlSugarClient _db;
        
        /// <summary>
        /// 获取Oracle数据库连接客户端
        /// </summary>
        /// <returns>SqlSugar客户端</returns>
        public static SqlSugarClient GetInstance()
        {
            if (_db == null)
            {
                _db = new SqlSugarClient(new ConnectionConfig()
                {
                    ConnectionString = _connectionString,
                    DbType = DbType.Oracle,
                    IsAutoCloseConnection = true,
                    InitKeyType = InitKeyType.Attribute
                });
                
                // 添加调试日志
                _db.Aop.OnLogExecuting = (sql, pars) =>
                {
                    System.Diagnostics.Debug.WriteLine($"Oracle SQL: {sql}");
                    foreach (var parameter in pars)
                    {
                        System.Diagnostics.Debug.WriteLine($"Parameter: {parameter.ParameterName}={parameter.Value}");
                    }
                };
            }
            return _db;
        }
    }
} 