using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq.Expressions;
using System.Web;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.PLM;
using AOS.SRM.Entity.PLM.Req;

namespace AOS.SRM.Application.PLM
{
    /// <summary>
    /// 非标图纸应用服务
    /// </summary>
    public class P_Blueprint_Non_StandardApp : BaseApp<P_Blueprint_Non_Standard>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public P_Blueprint_Non_StandardApp() : base()
        {
        }

        #endregion

        #region 查询功能

        /// <summary>
        /// 根据查询条件获取分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="request">查询请求参数</param>
        /// <returns>分页列表</returns>
        public List<P_Blueprint_Non_Standard> GetPageList(Pagination page, P_Blueprint_Non_StandardQueryRequest request)
        {
            var condition = BuildQueryCondition(request);
            return base.GetPageList(page, condition);
        }

        /// <summary>
        /// 根据查询条件获取所有数据（用于导出）
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns>数据列表</returns>
        public List<P_Blueprint_Non_Standard> GetAllData(P_Blueprint_Non_StandardQueryRequest request)
        {
            var condition = BuildQueryCondition(request);
            return GetList(condition).ToList();
        }

        /// <summary>
        /// 构建查询条件
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns>查询条件表达式</returns>
        private Expression<Func<P_Blueprint_Non_Standard, bool>> BuildQueryCondition(P_Blueprint_Non_StandardQueryRequest request)
        {
            Expression<Func<P_Blueprint_Non_Standard, bool>> condition = x => true;

            if (request == null) return condition;

            if (!string.IsNullOrEmpty(request.MaterialCode))
            {
                condition = condition.And(x => x.MaterialCode.Contains(request.MaterialCode));
            }

            if (!string.IsNullOrEmpty(request.MaterialVersion))
            {
                condition = condition.And(x => x.MaterialVersion.Contains(request.MaterialVersion));
            }

            if (!string.IsNullOrEmpty(request.MaterialDesc))
            {
                condition = condition.And(x => x.MaterialDesc.Contains(request.MaterialDesc));
            }

            if (!string.IsNullOrEmpty(request.FactoryCode))
            {
                condition = condition.And(x => x.FactoryCode == request.FactoryCode);
            }

            if (!string.IsNullOrEmpty(request.SapNo))
            {
                condition = condition.And(x => x.SapNo.Contains(request.SapNo));
            }

            if (request.FileStatus.HasValue)
            {
                condition = condition.And(x => x.FileStatus == request.FileStatus.Value);
            }

            if (request.CreateTimeStart.HasValue)
            {
                condition = condition.And(x => x.CTime >= request.CreateTimeStart.Value);
            }

            if (request.CreateTimeEnd.HasValue)
            {
                condition = condition.And(x => x.CTime <= request.CreateTimeEnd.Value);
            }

            if (!string.IsNullOrEmpty(request.Keyword))
            {
                condition = condition.And(x => x.MaterialCode.Contains(request.Keyword) ||
                                              x.MaterialDesc.Contains(request.Keyword) ||
                                              x.SapNo.Contains(request.Keyword));
            }

            return condition;
        }



        #endregion

        #region 文件上传功能

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="currentUserName">当前用户名</param>
        /// <returns>上传结果</returns>
        public Dictionary<string, string> UploadFile(string currentUserName)
        {
            HttpRequest request = HttpContext.Current.Request;
            string timeSpace = DateTime.Now.ToString("yyyyMMddHHmmss");
            var files = request.Files[0];
            string fileNameWithOutExtension = Path.GetFileNameWithoutExtension(files.FileName);
            string extension = Path.GetExtension(files.FileName);
            string fileName = fileNameWithOutExtension + "_" + timeSpace + extension;

            // 获取上传路径配置
            string uploadPath = ConfigurationManager.AppSettings["P_Blueprint_Non_Standard"] ?? "UploadFiles\\files\\P_Blueprint_Non_Standard\\";
            string fullPath = HttpContext.Current.Server.MapPath("~/" + uploadPath);

            // 确保目录存在
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
            }

            string filePath = Path.Combine(fullPath, fileName);
            files.SaveAs(filePath);

            // 返回相对路径用于存储到数据库
            string relativePath = uploadPath.Replace("\\", "/") + fileName;

            return new Dictionary<string, string>
            {
                {"FilePath", "/" + relativePath},
                {"FileName", fileName},
                {"OriginalFileName", files.FileName}
            };
        }

        /// <summary>
        /// 上传文件并更新记录状态
        /// </summary>
        /// <param name="recordId">记录ID</param>
        /// <param name="currentUserName">当前用户名</param>
        /// <returns>上传结果</returns>
        public Dictionary<string, object> UploadFileAndUpdateStatus(string recordId, string currentUserName)
        {
            // 先上传文件
            var uploadResult = UploadFile(currentUserName);

            // 获取记录
            var entity = GetEntityByKey(recordId);
            if (entity == null)
            {
                throw new Exception($"未找到ID为{recordId}的记录");
            }

            // 更新记录的文件信息和状态
            entity.FilePath = uploadResult["FilePath"];
            entity.FileName = uploadResult["FileName"];
            entity.FileStatus = 1; // 设置为已上传
            entity.MUser = currentUserName;
            entity.MTime = DateTime.Now;

            // 保存更新
            bool updateSuccess = Update(entity) > 0;

            if (!updateSuccess)
            {
                throw new Exception("更新记录状态失败");
            }

            return new Dictionary<string, object>
            {
                {"FilePath", uploadResult["FilePath"]},
                {"FileName", uploadResult["FileName"]},
                {"OriginalFileName", uploadResult["OriginalFileName"]},
                {"RecordId", recordId},
                {"FileStatus", 1},
                {"UpdateSuccess", updateSuccess}
            };
        }

        #endregion

        #region 数据操作

        /// <summary>
        /// 新增非标图纸记录
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns>操作结果</returns>
        public P_Blueprint_Non_Standard AddEntity(P_Blueprint_Non_Standard entity, string currentUser)
        {
            entity.Id = Guid.NewGuid().ToString();
            entity.CUser = currentUser;
            entity.CTime = DateTime.Now;
            entity.IsDelete = false;

            return Insert(entity);
        }

        /// <summary>
        /// 更新非标图纸记录
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns>操作结果</returns>
        public bool UpdateEntity(P_Blueprint_Non_Standard entity, string currentUser)
        {
            entity.MUser = currentUser;
            entity.MTime = DateTime.Now;

            return Update(entity) > 0;
        }

        /// <summary>
        /// 删除非标图纸记录（逻辑删除）
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns>操作结果</returns>
        public bool DeleteEntity(string id, string currentUser)
        {
            var entity = GetEntityByKey(id);
            if (entity != null)
            {
                entity.IsDelete = true;
                entity.DUser = currentUser;
                entity.DTime = DateTime.Now;
                return Update(entity) > 0;
            }
            return false;
        }

        #endregion
    }
}

// 扩展方法类，用于构建查询条件
public static class PredicateExtensions
{
    public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
    {
        var parameter = Expression.Parameter(typeof(T));
        var leftVisitor = new ReplaceExpressionVisitor(expr1.Parameters[0], parameter);
        var left = leftVisitor.Visit(expr1.Body);
        var rightVisitor = new ReplaceExpressionVisitor(expr2.Parameters[0], parameter);
        var right = rightVisitor.Visit(expr2.Body);
        return Expression.Lambda<Func<T, bool>>(Expression.AndAlso(left, right), parameter);
    }

    private class ReplaceExpressionVisitor : ExpressionVisitor
    {
        private readonly Expression _oldValue;
        private readonly Expression _newValue;

        public ReplaceExpressionVisitor(Expression oldValue, Expression newValue)
        {
            _oldValue = oldValue;
            _newValue = newValue;
        }

        public override Expression Visit(Expression node)
        {
            if (node == _oldValue)
                return _newValue;
            return base.Visit(node);
        }
    }
}
