using AOS.SRM.Application.Base;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.ViewModel;
using AOS.SRM.Entity.PLM;
using AOS.SRM.Entity.Sys;
using AOS.SRM.Application.Sys;
using AOS.Core.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using AOS.SRM.Application.Plm;

namespace AOS.SRM.Application.PXC
{
    /// <summary>
    /// 图纸发放记录应用层
    /// </summary>
    public class P_Blueprint_Send_RecordApp : BaseApp<P_Blueprint_Send_Record>
    {
        private readonly Plm_BlueprintApp _blueprintApp = new Plm_BlueprintApp();
        private readonly P_Blueprint_AcceptApp _acceptApp = new P_Blueprint_AcceptApp();
        private readonly Sys_UserApp _userApp = new Sys_UserApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public P_Blueprint_Send_RecordApp() : base()
        {
        }

        #endregion

        #region 业务方法

        /// <summary>
        /// 根据图纸ID获取发放记录
        /// </summary>
        /// <param name="blueprintId">图纸ID</param>
        /// <returns></returns>
        public List<P_Blueprint_Send_Record> GetRecordsByBlueprintId(string blueprintId)
        {
            if (string.IsNullOrEmpty(blueprintId))
                return new List<P_Blueprint_Send_Record>();

            return GetList(x => x.BlueprintId == blueprintId && x.IsDelete == false).ToList();
        }

        /// <summary>
        /// 根据接收ID获取发放记录
        /// </summary>
        /// <param name="acceptId">接收ID</param>
        /// <returns></returns>
        public List<P_Blueprint_Send_Record> GetRecordsByAcceptId(string acceptId)
        {
            if (string.IsNullOrEmpty(acceptId))
                return new List<P_Blueprint_Send_Record>();

            return GetList(x => x.AcceptId == acceptId && x.IsDelete == false).ToList();
        }

        /// <summary>
        /// 根据图纸ID和接收ID获取发放记录
        /// </summary>
        /// <param name="blueprintId">图纸ID</param>
        /// <param name="acceptId">接收ID</param>
        /// <returns></returns>
        public P_Blueprint_Send_Record GetRecordByBlueprintAndAccept(string blueprintId, string acceptId)
        {
            if (string.IsNullOrEmpty(blueprintId) || string.IsNullOrEmpty(acceptId))
                return null;

            return GetFirstEntity(x => x.BlueprintId == blueprintId && x.AcceptId == acceptId && x.IsDelete == false);
        }

        /// <summary>
        /// 批量发放图纸给接收记录
        /// </summary>
        /// <param name="blueprintId">图纸ID</param>
        /// <param name="acceptIds">接收ID列表</param>
        /// <param name="createUser">创建用户</param>
        /// <returns></returns>
        public bool BatchSendToAccepts(string blueprintId, List<string> acceptIds, string createUser)
        {
            try
            {
                var records = new List<P_Blueprint_Send_Record>();
                foreach (var acceptId in acceptIds)
                {
                    // 检查是否已存在记录
                    var existRecord = GetFirstEntity(x => x.BlueprintId == blueprintId && x.AcceptId == acceptId && x.IsDelete == false);
                    if (existRecord == null)
                    {
                        records.Add(new P_Blueprint_Send_Record
                        {
                            Id = Guid.NewGuid().ToString(),
                            BlueprintId = blueprintId,
                            AcceptId = acceptId,
                            CUser = createUser,
                            CTime = DateTime.Now,
                            IsDelete = false
                        });
                    }
                }

                if (records.Any())
                {
                    return Insert(records) > 0;
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取图纸发放记录详细信息（包含图纸和用户信息）
        /// </summary>
        /// <param name="condition">查询条件</param>
        /// <returns></returns>
        public List<P_Blueprint_Send_Record_View> GetDetailList(Expression<Func<P_Blueprint_Send_Record, bool>> condition)
        {
            var sendRecords = GetList(condition).ToList();
            if (!sendRecords.Any())
                return new List<P_Blueprint_Send_Record_View>();

            // 获取所有相关的接收记录
            var acceptIds = sendRecords.Select(x => x.AcceptId).Distinct().ToList();
            var acceptRecords = _acceptApp.GetListByKeys(acceptIds.ToArray()).ToList();

            // 获取所有相关的图纸信息
            var blueprintIds = sendRecords.Select(x => x.BlueprintId).Distinct().ToList();
            var blueprints = _blueprintApp.GetListByKeys(blueprintIds.ToArray()).ToList();

            // 获取所有相关的用户信息
            var userAccounts = acceptRecords.Select(x => x.CUser).Distinct().ToList();
            var users = _userApp.GetList(x => userAccounts.Contains(x.LoginAccount)).ToList();

            // 组装详细信息
            var result = new List<P_Blueprint_Send_Record_View>();
            foreach (var sendRecord in sendRecords)
            {
                var acceptRecord = acceptRecords.FirstOrDefault(x => x.Id == sendRecord.AcceptId);
                var blueprint = blueprints.FirstOrDefault(x => x.Id == sendRecord.BlueprintId);
                var user = acceptRecord != null ? users.FirstOrDefault(x => x.LoginAccount == acceptRecord.CUser) : null;

                var view = new P_Blueprint_Send_Record_View
                {
                    // 发放记录信息
                    Id = sendRecord.Id,
                    BlueprintId = sendRecord.BlueprintId,
                    AcceptId = sendRecord.AcceptId,
                    Remark = sendRecord.Remark,
                    CUser = sendRecord.CUser,
                    CTime = sendRecord.CTime,
                    MUser = sendRecord.MUser,
                    MTime = sendRecord.MTime,

                    // 图纸信息
                    MaterialCode = blueprint?.MaterialCode,
                    BlueprintName = blueprint?.Name,
                    Version = blueprint?.Version,
                    DownUrl = blueprint?.DownUrl,
                    EffectiveDate = blueprint?.EffectiveDate,

                    // 用户信息
                    UserID = user?.UserID,
                    UserName = user?.UserName,
                    LoginAccount = user?.LoginAccount,
                    Email = user?.Email,
                    Mobile = user?.Mobile,
                    SupplyCode = user?.SupplyCode,
                    SupplyName = user?.SupplyName,

                    // 接收记录信息
                    Status = acceptRecord?.Status,
                    StatusDesc = GetStatusDescription(acceptRecord?.Status),
                    PurchaseOrder = acceptRecord?.PurchaseOrder,
                    PurchaseOrderLine = acceptRecord?.PurchaseOrderLine,
                    PurchaseOrderAndLine = acceptRecord?.PurchaseOrderAndLine,
                    Type = acceptRecord?.Type ?? 0,
                    TypeDesc = GetTypeDescription(acceptRecord?.Type ?? 0),
                    AcceptTime = acceptRecord?.CTime
                };

                result.Add(view);
            }

            return result;
        }

        /// <summary>
        /// 分页获取图纸发放记录详细信息
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="condition">查询条件</param>
        /// <returns></returns>
        public List<P_Blueprint_Send_Record_View> GetDetailPageList(Pagination page, Expression<Func<P_Blueprint_Send_Record, bool>> condition)
        {
            var sendRecords = GetPageList(page, condition).ToList();
            if (!sendRecords.Any())
                return new List<P_Blueprint_Send_Record_View>();

            // 获取所有相关的接收记录
            var acceptIds = sendRecords.Select(x => x.AcceptId).Distinct().ToList();
            var acceptRecords = _acceptApp.GetListByKeys(acceptIds.ToArray()).ToList();

            // 获取所有相关的图纸信息
            var blueprintIds = sendRecords.Select(x => x.BlueprintId).Distinct().ToList();
            var blueprints = _blueprintApp.GetListByKeys(blueprintIds.ToArray()).ToList();

            // 获取所有相关的用户信息
            var userAccounts = acceptRecords.Select(x => x.CUser).Distinct().ToList();
            var users = _userApp.GetList(x => userAccounts.Contains(x.LoginAccount)).ToList();

            // 组装详细信息
            var result = new List<P_Blueprint_Send_Record_View>();
            foreach (var sendRecord in sendRecords)
            {
                var acceptRecord = acceptRecords.FirstOrDefault(x => x.Id == sendRecord.AcceptId);
                var blueprint = blueprints.FirstOrDefault(x => x.Id == sendRecord.BlueprintId);
                var user = acceptRecord != null ? users.FirstOrDefault(x => x.LoginAccount == acceptRecord.CUser) : null;

                var view = new P_Blueprint_Send_Record_View
                {
                    // 发放记录信息
                    Id = sendRecord.Id,
                    BlueprintId = sendRecord.BlueprintId,
                    AcceptId = sendRecord.AcceptId,
                    Remark = sendRecord.Remark,
                    CUser = sendRecord.CUser,
                    CTime = sendRecord.CTime,
                    MUser = sendRecord.MUser,
                    MTime = sendRecord.MTime,

                    // 图纸信息
                    MaterialCode = blueprint?.MaterialCode,
                    BlueprintName = blueprint?.Name,
                    Version = blueprint?.Version,
                    DownUrl = blueprint?.DownUrl,
                    EffectiveDate = blueprint?.EffectiveDate,

                    // 用户信息
                    UserID = user?.UserID,
                    UserName = user?.UserName,
                    LoginAccount = user?.LoginAccount,
                    Email = user?.Email,
                    Mobile = user?.Mobile,
                    SupplyCode = user?.SupplyCode,
                    SupplyName = user?.SupplyName,

                    // 接收记录信息
                    Status = acceptRecord?.Status,
                    StatusDesc = GetStatusDescription(acceptRecord?.Status),
                    PurchaseOrder = acceptRecord?.PurchaseOrder,
                    PurchaseOrderLine = acceptRecord?.PurchaseOrderLine,
                    PurchaseOrderAndLine = acceptRecord?.PurchaseOrderAndLine,
                    Type = acceptRecord?.Type ?? 0,
                    TypeDesc = GetTypeDescription(acceptRecord?.Type ?? 0),
                    AcceptTime = acceptRecord?.CTime
                };

                result.Add(view);
            }

            return result;
        }

        /// <summary>
        /// 获取状态描述
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns></returns>
        private string GetStatusDescription(int? status)
        {
            switch (status)
            {
                case 1: return "待下载";
                case 2: return "待确认";
                case 3: return "已确认";
                default: return "未知";
            }
        }

        /// <summary>
        /// 获取类型描述
        /// </summary>
        /// <param name="type">类型值</param>
        /// <returns></returns>
        private string GetTypeDescription(int type)
        {
            switch (type)
            {
                case 1: return "自动";
                case 2: return "手动";
                default: return "未知";
            }
        }

        #endregion
    }
} 