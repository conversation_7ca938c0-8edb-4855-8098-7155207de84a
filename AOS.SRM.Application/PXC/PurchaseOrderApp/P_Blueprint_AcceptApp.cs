using AOS.SRM.Application.Base;
using AOS.SRM.Entity.PLM;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class P_Blueprint_AcceptApp : BaseApp<P_Blueprint_Accept>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public P_Blueprint_AcceptApp() : base()
        {
        }

        #endregion
    }
}