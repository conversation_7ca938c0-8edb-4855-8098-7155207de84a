using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Dto;
using AOS.SRM.Entity.PXC.ViewModel.PurchaseOrder;
using AOS.SRM.Entity.SAP;
using HZ.WMS.Application.Sys;

namespace AOS.SRM.Application.PXC
{
    /// <summary>
    /// 采购送货批次
    /// </summary>
    public class PurchaseDeliveryBatchApp : BaseApp<P_DeliveryBatch>
    {
        P_PurchaseOrderApp _purchaseorderstatusApp = new P_PurchaseOrderApp();
        P_InspectionApp _inspecDetailApp = new P_InspectionApp();
        Sys_SAPCompanyInfoApp _sap = new Sys_SAPCompanyInfoApp();

        #region 导出数据
        /// <summary>
        /// 查询导出数据
        /// </summary>
        /// <param name="OrderNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="status"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        public List<PXC_PurchaseDeliveryBatch_View> GetAllExportData(string OrderNo, string SupplyCode, string MaterialCode, string MaterialName, DateTime StartTime, DateTime EndTime, string status,string CompanyCode)
        {
            var query = this.DbContext.Queryable<PXC_PurchaseDeliveryBatch_View>()
                .Where(t => (t.EINDT >= StartTime) && (t.EINDT < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(OrderNo) || t.OrderNo.Contains(OrderNo) || t.ZTEXT.Contains(OrderNo))
                && (string.IsNullOrEmpty(SupplyCode) || t.LIFNR.Contains(SupplyCode))
                && (string.IsNullOrEmpty(MaterialCode) || t.ItemCode.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                && (string.IsNullOrEmpty(MaterialName) || t.MaterialName.Contains(MaterialName))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && t.IsDelete != true && t.BSART != "Z004");

            if (status == "0")//未(部分)制作报检单
            {
                query = query.Where(t => t.BatchQty > t.HasInspectionQty);
            }
            else if (status == "2")//已制作报检单
            {
                query = query.Where(t => t.BatchQty == t.HasInspectionQty);
            }
            var lstEntity = new List<PXC_PurchaseDeliveryBatch_View>();
            lstEntity = query.OrderByDescending(t => t.DeliveryToDate)
                .OrderBy(t => t.OrderNo)
                .OrderBy(t => t.EBELP).ToList();
            return lstEntity;
        }
        #endregion

        #region 制作送货批次
        /// <summary>
        /// 制作送货批次
        /// </summary>
        /// <param name="lstEntity"></param>
        /// <param name="userCode"></param>
        /// <returns></returns>
        public void AddDeliveryBatch(List<P_DeliveryBatch> lstEntity, string userCode)
        {
            try
            {
                List<P_PurchaseOrderStatus> lstPStatusAdd = new List<P_PurchaseOrderStatus>();
                List<P_PurchaseOrderStatus> lstPstatusUpdate = new List<P_PurchaseOrderStatus>();

                lstEntity.ForEach(t =>
                {
                    t.CUser = userCode;
                    t.Unit = (t.Unit == "PC" ? "ST" : t.Unit);
                    var pStatus = _purchaseorderstatusApp.GetFirstEntity(m => m.OrderNo == t.OrderNo && m.OrderLine == t.OrderLine);
                    if (pStatus == null || string.IsNullOrEmpty(pStatus.OrderNo))
                    {
                        var isHave = lstPStatusAdd.Where(x => x.OrderNo == t.OrderNo && x.OrderLine == t.OrderLine).FirstOrDefault();
                        if (isHave == null)
                        {
                            P_PurchaseOrderStatus status = new P_PurchaseOrderStatus();
                            status.CompanyCode = t.CompanyCode; 
                            status.FactoryCode = t.FactoryCode;
                            status.OrderNo = t.OrderNo;
                            status.OrderLine = t.OrderLine;
                            status.Status = "2";
                            status.CUser = userCode;
                            status.CTime = DateTime.Now;
                            lstPStatusAdd.Add(status);
                        }
                    }
                    else
                    {
                        pStatus.Status = "2";
                        pStatus.MUser = userCode;
                        lstPstatusUpdate.Add(pStatus);
                    }
                });

                InsertWithTran(lstEntity);
                if (lstPStatusAdd.Count > 0)
                {
                    _purchaseorderstatusApp.InsertWithTran(lstPStatusAdd);
                }
                if (lstPstatusUpdate.Count > 0)
                {
                    _purchaseorderstatusApp.UpdateWithTran(lstPstatusUpdate);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }


            //try
            //{
            //    List<P_PurchaseOrderStatus> lstPStatusAdd = new List<P_PurchaseOrderStatus>();
            //    List<P_PurchaseOrderStatus> lstPstatusUpdate = new List<P_PurchaseOrderStatus>();

            //    lstEntity.ForEach(t =>
            //    {
            //        t.CUser = userCode;
            //        t.Unit = (t.Unit == "PC" ? "ST" : t.Unit);
            //        var pStatus = _purchaseorderstatusApp.GetFirstEntity(m => m.OrderNo == t.OrderNo && m.OrderLine == t.OrderLine);
            //        if (pStatus == null || string.IsNullOrEmpty(pStatus.OrderNo))
            //        {
            //            var isHave = lstPStatusAdd.Where(x => x.OrderNo == t.OrderNo && x.OrderLine == t.OrderLine).First();
            //            if (isHave == null)
            //            {
            //                P_PurchaseOrderStatus status = new P_PurchaseOrderStatus();
            //                //status.CompanyCode = "2002"; //cc-220713注释
            //                //status.FactoryCode = "2002"; //cc-220713注释
            //                //cc-220713添加
            //                if (t.PurchaseORG == "XF10")
            //                {
            //                    status.CompanyCode = "2002"; 
            //                    status.FactoryCode = "2002";
            //                }else if (t.PurchaseORG == "XF20")
            //                {
            //                    status.CompanyCode = "2020";
            //                    status.FactoryCode = "2020";
            //                }
            //                status.OrderNo = t.OrderNo;
            //                status.OrderLine = t.OrderLine;
            //                status.Status = "2";
            //                status.CUser = userCode;
            //                status.CTime = DateTime.Now;
            //                lstPStatusAdd.Add(status);
            //            }
            //        }
            //        else
            //        {
            //            pStatus.Status = "2";
            //            pStatus.MUser = userCode;
            //            lstPstatusUpdate.Add(pStatus);
            //        }
            //    });
            //    this.DbContext.Ado.BeginTran();
            //    _purchaseorderstatusApp.DbContext = this.DbContext;

            //    Insert(lstEntity);
            //    if (lstPStatusAdd.Count > 0)
            //    {
            //        _purchaseorderstatusApp.Insert(lstPStatusAdd);
            //    }
            //    if (lstPstatusUpdate.Count > 0)
            //    {
            //        _purchaseorderstatusApp.Update(lstPstatusUpdate);
            //    }

            //    this.DbContext.Ado.CommitTran();
            //}
            //catch (Exception ex)
            //{
            //    this.DbContext.Ado.RollbackTran();
            //    throw ex;
            //}
        }

        #endregion

        #region 删除当前列表
        /// <summary>
        /// 删除当前列表   （如果已经报检，则无法进行删除）
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="deleteUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool DeleteDeliveryBatch(string[] ids, string deleteUser, out string msg)
        {
            try
            {
                msg = "";
                List<P_DeliveryBatch> deliveryList = GetListByKeys(ids);
                bool bResult = _inspecDetailApp.Any(t => ids.Contains(t.DeliveryBatchId));
                if (bResult)
                {
                    msg = "存在已制作报检单的数据，不允许删除";
                    return false;
                }
                var orderStatusList = new List<P_PurchaseOrderStatus>();
                deliveryList.ForEach(item =>
                {
                    var isExist = Any(t => t.Id != item.Id && t.OrderNo == item.OrderNo && t.OrderLine == item.OrderLine);
                    if (!isExist)
                    {
                        var data = _purchaseorderstatusApp.GetFirstEntity(t => t.OrderNo == item.OrderNo && t.OrderLine == item.OrderLine);
                        if (data != null && !string.IsNullOrEmpty(data.OrderNo))
                        {
                            data.DUser = deleteUser;
                            data.DTime = DateTime.Now;
                            orderStatusList.Add(data);
                        }
                    }
                });
                DbContext.Ado.BeginTran();
                _purchaseorderstatusApp.DbContext = this.DbContext;

                Delete(deliveryList, deleteUser);
                _purchaseorderstatusApp.Delete(orderStatusList);

                DbContext.Ado.CommitTran();

                if (orderStatusList.Count > 0)
                {
                    //#region 采购订单状态回写
                    //bool isPosted = true;
                    //var lstEntity = orderStatusList.Select(t => new ZFGSRM007() { EBELN = t.OrderNo, EBELP = (int)t.OrderLine, LOEKZ = "O" }).ToList();
                    //_sap.ZFGSRM007("001", lstEntity, out isPosted, out msg);
                    //#endregion
                }
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                msg = ex.Message;
                return false;
            }
            return true;
        }
        #endregion

        #region 编辑当前采购送货批次(只允许编辑批次和数量)
        /// <summary>
        /// 编辑当前采购送货批次  （如果已经保检，则无法进行修改）未加
        /// </summary>
        /// <param name="P_DeliveryBatch"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool UpdateDeliveryBatch(P_DeliveryBatch P_DeliveryBatch, out string error_message)
        {
            error_message = "";
            try
            {
                var query = GetList(x => x.Id == P_DeliveryBatch.Id).ToList().FirstOrDefault();
                if (query != null)
                {
                    query.BatchNum = P_DeliveryBatch.BatchNum;
                    query.BatchQty = P_DeliveryBatch.BatchQty;
                    query.DeliveryToDate = P_DeliveryBatch.DeliveryToDate;
                }
                Update(query);
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }

        }
        #endregion

        #region 分页查询
        /// <summary>
        /// 制作送货批次页面数据查询 
        /// </summary>
        /// <returns></returns>
        public List<PXC_PurchaseDeliveryBatch_View> GetPurchaseDeliveryBatch(Pagination page, string OrderNo, string SupplyCode, string MaterialCode, string MaterialName, DateTime StartTime, DateTime EndTime, string status,string CompanyCode,string userAccount,bool isSupplier)
        {
            var lstEntity = new List<PXC_PurchaseDeliveryBatch_View>();
            var query = this.DbContext.Queryable<PXC_PurchaseDeliveryBatch_View>()
                .Where(t => (t.EINDT >= StartTime) && (t.EINDT < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(OrderNo) || t.OrderNo.Contains(OrderNo) || t.ZTEXT.Contains(OrderNo))
                && ((isSupplier == false && (string.IsNullOrEmpty(SupplyCode) || t.LIFNR == SupplyCode)) || (((string.IsNullOrEmpty(SupplyCode) || t.LIFNR == SupplyCode) && (t.LLIEF == null || t.LLIEF == "")) || t.LLIEF == userAccount || t.LIFRE == userAccount))
                && (string.IsNullOrEmpty(MaterialCode) || t.ItemCode.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                && (string.IsNullOrEmpty(MaterialName) || t.MaterialName.Contains(MaterialName))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && t.BSART != "Z004" && t.IsDelete == false);

            if (status == "0")//未(部分)制作报检单
            {
                query = query.Where(t => t.BatchQty > t.HasInspectionQty);
            }
            else if (status == "2")//已制作报检单
            {
                query = query.Where(t => t.BatchQty == t.HasInspectionQty);
            }
            page.Total = query.Count();
            lstEntity = query.OrderByDescending(t => t.DeliveryToDate)
                .OrderBy(t => t.OrderNo)
                .OrderBy(t => t.EBELP)
                .ToPageList(page.PageNumber, page.PageSize).ToList();

            return lstEntity;
        }
        #endregion

        #region 选择采购订单信息和消耗数量等信息
        /// <summary>
        /// 送货批次配置信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyName"></param>
        /// <param name="OrderNo"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<PXC_PurchaseApplyQty_View> GetPurchaseApplyQty(Pagination page, string SupplyName, string OrderNo, string Status, string MaterialCode, string MaterialName, DateTime StartTime, DateTime EndTime)
        {
            var query = this.DbContext.Queryable<PXC_PurchaseApplyQty_View>()
                .Where(t => (t.EINDT >= StartTime) && (t.EINDT < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(MaterialCode) || t.MATNR.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                && (string.IsNullOrEmpty(MaterialName) || t.TXZ01.Contains(MaterialName))
                && (string.IsNullOrEmpty(SupplyName) || t.NAME1.Contains(SupplyName))
                && (string.IsNullOrEmpty(OrderNo) || t.EBELN.Contains(OrderNo))
                && t.IsDelete == false && t.BSART != "Z004");

            page.Total = query.Count();
            var itemPageData = new List<PXC_PurchaseApplyQty_View>();
            itemPageData = query.ToPageList(page.PageNumber, page.PageSize).ToList();
            return itemPageData;
        }
        #endregion

        #region 采购订单页面供应商点击接收采购需求时状态改变、自动生成送货计划
        /// <summary>
        /// 采购订单页面供应商点击接收采购需求时状态改变、自动生成送货计划（订单编号和订单行号）
        /// </summary>
        /// <returns></returns>
        public string UpdatePurchaseOrderStatus(List<DeliveryBatchDto> dto, string user)
        {
            var func_result = "success";
            try
            {
                List<P_PurchaseOrderStatus> list_P_PurchaseOrderStatus = new List<P_PurchaseOrderStatus>();
                List<P_DeliveryBatch> list_P_DeliveryBatch = new List<P_DeliveryBatch>();//送货计划

                for (int i = 0; i < dto.Count; i++)
                {
                    var EBELN = dto[i].EBELN;
                    var EBELP = dto[i].EBELP;
                    var WERKS = dto[i].WERKS;

                    var query = this.DbContext.Queryable<P_PurchaseOrderStatus>().Where(t => t.OrderNo == EBELN && t.OrderLine == EBELP && t.IsDelete == false).ToList().FirstOrDefault();
                    if (query == null || string.IsNullOrEmpty(query.OrderNo))
                    {
                        P_PurchaseOrderStatus row = new P_PurchaseOrderStatus();
                        row.CompanyCode = WERKS;
                        row.FactoryCode = WERKS;
                        row.OrderNo = EBELN;
                        row.OrderLine = EBELP;
                        row.Status = "2";
                        row.CUser = user;
                        row.CTime = DateTime.Now;
                        list_P_PurchaseOrderStatus.Add(row);
                    }

                    var detail = this.DbContext.Queryable<P_DeliveryBatch>().Where(t => t.OrderNo == EBELN && t.OrderLine == EBELP && t.IsDelete == false)
                        .ToList().FirstOrDefault();
                    if (detail == null || string.IsNullOrEmpty(detail.OrderNo))
                    {
                        var data = this.DbContext.Queryable<PXC_PurchaseOrder_View>().Where(t => t.EBELN == EBELN && t.EBELP == EBELP).ToList().FirstOrDefault();
                        if (data != null)
                        {
                            if (data.TXZ01 != null && data.TXZ01.Length > 100)
                            {
                                data.TXZ01 = data.TXZ01.Substring(0, 100);
                            }
                            P_DeliveryBatch row = new P_DeliveryBatch()
                            {
                                Id = Guid.NewGuid().ToString(),
                                OrderType = data.BSART,
                                OrderNo = data.EBELN,
                                OrderLine = data.EBELP,
                                CompanyCode = data.BUKRS,
                                FactoryCode = data.WERKS,
                                SupplyCode = data.LIFNR,
                                SupplyName = data.NAME1,
                                ItemCode = data.MATNR,
                                ItemName = data.TXZ01,
                                BatchNum = data.BatchNum,
                                ProjectType = data.PSTYP,
                                WhsCode = data.LGORT,
                                WhsAddress = data.LGOBE,
                                BatchQty = data.MENGE,
                                DeliveryDate = data.EINDT,
                                DeliveryToDate = data.EINDT,
                                SaleNo = data.SaleNo,
                                SaleLineNo = data.SaleLineNo,
                                PurchaseORG = data.PurchaseORG,
                                PurchaseGroup = data.PurchaseGroup,
                                Unit = (data.Unit == "PC" ? "ST" : data.Unit),
                                //BrushingWords = data.BrushingWords,
                                CUser = user,
                                CTime = DateTime.Now
                            };
                            list_P_DeliveryBatch.Add(row);
                        }
                    }
                }
                DbContext.Ado.BeginTran();
                _purchaseorderstatusApp.Insert(list_P_PurchaseOrderStatus);
                if (list_P_DeliveryBatch.Count > 0)
                {
                    Insert(list_P_DeliveryBatch);
                }
                DbContext.Ado.CommitTran();
                return func_result;   
            }
            catch (Exception ex)
            {
                DbContext.RollbackTran();
                return ex.ToString();
            }
           

            //try
            //{
            //    List<P_PurchaseOrderStatus> statusList = new List<P_PurchaseOrderStatus>();
            //    List<P_DeliveryBatch> lstDeliveryBatch = new List<P_DeliveryBatch>();//送货计划
            //    //supplyCode = "";
            //    //supplyName = "";

            //    for (int i = 0; i < dto.Count; i++)
            //    {
            //        var EBELN = dto[i].EBELN;
            //        var EBELP = dto[i].EBELP;
            //        var WERKS = dto[i].WERKS;

            //        var isExist = Any(t => t.OrderNo == EBELN && t.OrderLine == EBELP);
            //        if (!isExist)
            //        {
            //            var pStatus = _purchaseorderstatusApp.GetFirstEntity(m => m.OrderNo == EBELN && m.OrderLine == EBELP);
            //            if (pStatus == null || string.IsNullOrEmpty(pStatus.OrderNo))
            //            {
            //                var isHave = statusList.Where(x => x.OrderNo == EBELN && x.OrderLine == EBELP).First();
            //                if (isHave == null)
            //                {
            //                    P_PurchaseOrderStatus status = new P_PurchaseOrderStatus();
            //                    //status.CompanyCode = "2002"; //cc-220713注释
            //                    //status.FactoryCode = "2002"; //cc-220713注释
            //                    //cc-220713添加
            //                    status.CompanyCode = WERKS;
            //                    status.FactoryCode = WERKS;
            //                    status.OrderNo = EBELN;
            //                    status.OrderLine = EBELP;
            //                    status.Status = "2";
            //                    status.CUser = user;
            //                    status.CTime = DateTime.Now;
            //                    statusList.Add(status);
            //                }
            //            }

            //            #region 自动生成送货计划
            //            var data = DbContext.Queryable<PXC_PurchaseOrder_View>().Where(t => t.EBELN == EBELN && t.EBELP == EBELP).ToList().First();
            //            if (data != null)
            //            {
            //                //supplyCode = data.LIFNR;
            //                //supplyName = data.NAME1;
            //                P_DeliveryBatch batchEntity = new P_DeliveryBatch()
            //                {
            //                    Id = Guid.NewGuid().ToString(),
            //                    OrderType = data.BSART,
            //                    OrderNo = data.EBELN,
            //                    OrderLine = data.EBELP,
            //                    //CompanyCode = "2002", //cc-220713注释
            //                    //FactoryCode = "2002", //cc-220713注释
            //                    CompanyCode = data.BUKRS, //cc-220713添加
            //                    FactoryCode = data.WERKS, //cc-220713添加
            //                    SupplyCode = data.LIFNR,
            //                    SupplyName = data.NAME1,
            //                    ItemCode = data.MATNR,
            //                    ItemName = data.TXZ01,
            //                    BatchNum = data.BatchNum,
            //                    ProjectType = data.PSTYP,
            //                    WhsCode = data.LGORT,
            //                    WhsAddress = data.LGOBE,
            //                    BatchQty = data.MENGE,
            //                    DeliveryDate = data.EINDT,
            //                    DeliveryToDate = data.EINDT,
            //                    SaleNo = data.SaleNo,
            //                    SaleLineNo = data.SaleLineNo,
            //                    PurchaseORG = data.PurchaseORG,
            //                    PurchaseGroup = data.PurchaseGroup,
            //                    Unit = (data.Unit == "PC" ? "ST" : data.Unit),
            //                    //BrushingWords = data.BrushingWords,
            //                    CUser = user,
            //                    CTime = DateTime.Now
            //                };
            //                lstDeliveryBatch.Add(batchEntity);
            //            }
            //            #endregion
            //        }
            //    }
            //    DbContext.Ado.BeginTran();
            //    _purchaseorderstatusApp.DbContext = this.DbContext;

            //    _purchaseorderstatusApp.Insert(statusList);
            //    if (lstDeliveryBatch.Count > 0)
            //    {
            //        Insert(lstDeliveryBatch);
            //    }

            //    this.DbContext.Ado.CommitTran();

            //    //#region 采购订单状态回写
            //    //bool isPosted = true;
            //    //string msg = "";
            //    //var lstEntity = statusList.Select(t => new ZFGSRM007() { EBELN = t.OrderNo, EBELP = (int)t.OrderLine, LOEKZ = "X" }).ToList();
            //    //_sap.ZFGSRM007("001", lstEntity, out isPosted, out msg);
            //    //#endregion

            //}
            //catch (Exception ex)
            //{
            //    this.DbContext.Ado.RollbackTran();
            //    throw ex;
            //}
            //return true;
        }
        #endregion

        /// <summary>
        /// 采购订单解锁
        /// </summary>
        /// <param name="orderStatusList"></param>
        /// <returns></returns>
        public bool OrderUnLock(List<P_PurchaseOrderStatus> orderStatusList)
        {
            #region 采购订单状态回写
            bool isPosted = true;
            string msg = "";
            var lstEntity = orderStatusList.Select(t => new ZFGSRM007() { EBELN = t.OrderNo, EBELP = (int)t.OrderLine, LOEKZ = "O" }).ToList();
            _sap.ZFGSRM007("001", lstEntity, out isPosted, out msg);
            #endregion
            return true;
        }

    }
}
