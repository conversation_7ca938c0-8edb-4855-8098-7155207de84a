using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Dto;

namespace AOS.SRM.Application.PXC
{
    /// <summary>
    /// 标准报检单
    /// </summary>
    public class P_InspectionApp : BaseApp<P_InspectionDetail>
    {
        /// <summary>
        /// 
        /// </summary>
        public class InspectionApp : BaseApp<P_Inspection> { };
        InspectionApp app = new InspectionApp();

        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="InspectionNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="orderNo"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        public List<PXC_MakeInspection_View> GetMakeInspection(Pagination page, string InspectionNo, string SupplyCode, string MaterialCode, string orderNo, string Status, DateTime StartTime, DateTime EndTime,string CompanyCode,string userAccount,bool isSupplier)
        {
            StartTime = Convert.ToDateTime(StartTime.ToShortDateString());
            EndTime = Convert.ToDateTime(EndTime.ToShortDateString());

            var query = this.DbContext.Queryable<PXC_MakeInspection_View>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(InspectionNo) || t.InspectionNo.Contains(InspectionNo))
                && (string.IsNullOrEmpty(MaterialCode) || t.MATNR.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode) || t.TXZ01.Contains(MaterialCode))
                && (string.IsNullOrEmpty(orderNo) || t.OrderNo.Contains(orderNo) || t.ZTEXT.Contains(orderNo))
                && ((isSupplier == false && (string.IsNullOrEmpty(SupplyCode) || t.SupplyerCode == SupplyCode)) || (((string.IsNullOrEmpty(SupplyCode) || t.SupplyerCode == SupplyCode) && (t.LLIEF == null || t.LLIEF == "")) || t.LLIEF == userAccount || t.LIFRE == userAccount))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && t.BSART != "Z004" && t.IsDelete == false);

            if (!string.IsNullOrEmpty(Status))
            {
                if (Status == "1")//未检验
                {
                    query = query.Where(t => (t.QualifiedQty == null || t.QualifiedQty < t.InspectionQty));
                }
                else if (Status == "2")//未入库
                {
                    query = query.Where(t => (t.StorageQty == null || t.StorageQty < t.InspectionQty));
                }
            }
            page.Total = query.Count();
            var itemPageData = new List<PXC_MakeInspection_View>();
            itemPageData = query.OrderByDescending(t => t.CTime)
                .OrderBy(t => t.InspectionNo)
                .OrderBy(t => t.InspectionLine)
                .ToPageList(page.PageNumber, page.PageSize).ToList();
            return itemPageData;
        }
        #endregion


        #region 导出
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="InspectionNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="orderNo"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="Status"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        public List<PXC_MakeInspection_View> GetAllExportData(string InspectionNo, string SupplyCode, string MaterialCode, string orderNo, DateTime StartTime, DateTime EndTime, string Status, string CompanyCode)
        {
            var query = this.DbContext.Queryable<PXC_MakeInspection_View>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(InspectionNo) || t.InspectionNo.Contains(InspectionNo))
                && (string.IsNullOrEmpty(MaterialCode) || t.MATNR.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode) || t.TXZ01.Contains(MaterialCode))
                && (string.IsNullOrEmpty(orderNo) || t.OrderNo.Contains(orderNo) || t.ZTEXT.Contains(orderNo))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyerCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && t.BSART != "Z004" && t.IsDelete == false);
            if (!string.IsNullOrEmpty(Status))
            {
                if (Status == "1")//未检验
                {
                    query = query.Where(t => (t.QualifiedQty == null || t.QualifiedQty < t.InspectionQty));
                }
                else if (Status == "2")//未入库
                {
                    query = query.Where(t => (t.StorageQty == null || t.StorageQty < t.InspectionQty));
                }
            }
            var lstEntity = new List<PXC_MakeInspection_View>();
            lstEntity = query.OrderByDescending(t => t.CTime)
                .OrderBy(t => t.InspectionNo)
                .OrderBy(t => t.InspectionLine).ToList();
            return lstEntity;
        }
        #endregion

        #region 制作报检单页面   

        #region 删除分录行
        /// <summary>
        /// 删除分录行
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="deleteUser"></param>
        /// <returns></returns>
        public int DeleteInspectionDetail(string[] ids, string deleteUser)
        {
            List<P_InspectionDetail> deliveryList = GetListByKeys(ids);
            return DeleteWithTran(deliveryList, deleteUser);
        }
        #endregion


        #region 保存
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public bool InsertInspection(P_InspectionDetail_Views dto, string UserName)
        {
            try
            {
                P_Inspection Inspection = new P_Inspection();
                Inspection.InspectionNo = dto.InspectionNo;
                Inspection.CTime = DateTime.Now;
                Inspection.CUser = UserName;
                //Inspection.CompanyCode = "2002"; //cc-220713注释
                Inspection.Remark = dto.Remark;

                int i = 0;
                foreach (var item in dto.P_InspectionDetail)
                {
                    i++;
                    item.InspectionLine = i;
                    item.Unit = (item.Unit == "PC" ? "ST" : item.Unit);
                    //cc-220713添加
                    if (item.PurchaseORG == "XF10")
                    {
                        Inspection.CompanyCode = "2002";
                        Inspection.FactoryCode = "2002";
                        item.CompanyCode = "2002";
                        item.FactoryCode = "2002";
                    }
                    else if (item.PurchaseORG == "XF20")
                    {
                        Inspection.CompanyCode = "2020";
                        Inspection.FactoryCode = "2020";
                        item.CompanyCode = "2020";
                        item.FactoryCode = "2020";
                    }  
                }
                app.Insert(Inspection);
                Insert(dto.P_InspectionDetail);
                return true;
            }
            catch (Exception ex)
            {
                return false;
                throw ex;
            }

        }
        #endregion





        #endregion





    }
}
