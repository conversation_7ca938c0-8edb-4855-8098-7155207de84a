using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.PXC;
using SqlSugar;

namespace AOS.SRM.Application.PXC
{
    /// <summary>
    /// 图纸发放用户关联应用层
    /// </summary>
    public class P_Blueprint_Send_UserApp : BaseApp<P_Blueprint_Send_User>
    {
        /// <summary>
        /// 获取包含导航属性的图纸发放用户关联列表
        /// </summary>
        /// <param name="condition">查询条件</param>
        /// <returns>包含导航属性的图纸发放用户关联列表</returns>
        public List<P_Blueprint_Send_User> GetListWithNavigation(Expression<Func<P_Blueprint_Send_User, bool>> condition)
        {
            return DbContext.Queryable<P_Blueprint_Send_User>()
                .Where(condition)
                .Includes(x => x.UserInfo)
                .Includes(x => x.BlueprintInfo)
                .ToList();
        }
        
        /// <summary>
        /// 获取包含导航属性的分页图纸发放用户关联列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="condition">查询条件</param>
        /// <returns>包含导航属性的分页图纸发放用户关联列表</returns>
        public List<P_Blueprint_Send_User> GetPageListWithNavigation(Pagination page, Expression<Func<P_Blueprint_Send_User, bool>> condition)
        {
            int totalCount = 0;
            var result = DbContext.Queryable<P_Blueprint_Send_User>()
                .Where(condition)
                .Includes(x => x.UserInfo)
                .Includes(x => x.BlueprintInfo)
                .OrderBy(x => x.CTime, OrderByType.Desc)
                .ToPageList(page.PageNumber, page.PageSize, ref totalCount);
            page.Total = totalCount;
            return result;
        }
        
        /// <summary>
        /// 获取包含导航属性的单个图纸发放用户关联记录
        /// </summary>
        /// <param name="condition">查询条件</param>
        /// <returns>包含导航属性的单个图纸发放用户关联记录</returns>
        public P_Blueprint_Send_User GetFirstEntityWithNavigation(Expression<Func<P_Blueprint_Send_User, bool>> condition)
        {
            return DbContext.Queryable<P_Blueprint_Send_User>()
                .Where(condition)
                .Includes(x => x.UserInfo)
                .Includes(x => x.BlueprintInfo)
                .First();
        }
    }
} 