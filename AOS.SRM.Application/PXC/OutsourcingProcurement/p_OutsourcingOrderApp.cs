using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Application.Plm;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.PLM;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Dto;
using AOS.SRM.Entity.PXC.ViewModel.PurchaseOrder;
using AOS.SRM.Entity.Sys;
using static AOS.SRM.Entity.PXC.Dto.DeliveryBatchDto;

namespace AOS.SRM.Application.PXC.OutsourcingProcurement
{
    /// <summary>
    /// 委外采购订单
    /// </summary>
    public class p_OutsourcingOrderApp : BaseApp<P_PurchaseOrderStatus>
    {
        
        private P_Blueprint_AcceptApp _blueprintAcceptApp = new P_Blueprint_AcceptApp();
        private Plm_BlueprintApp _blueprintApp = new Plm_BlueprintApp();
        #region 分页查询
        /// <summary>
        /// 根据供应商、采购单号以及交期查询数据
        /// </summary>
        /// <returns></returns>
        public List<PXC_PurchaseOrder_View> GetOutsourcingOrder(Pagination page, string SupplyCode, string EBELN, string Status, string MaterialCode, string MaterialName, DateTime StartTime, DateTime EndTime, string CompanyCode, Sys_User currentUser)
        {
            var query = this.DbContext.Queryable<PXC_PurchaseOrder_View>()
                .Where(t => (t.EINDT >= StartTime) && (t.EINDT < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(MaterialCode) || t.MATNR.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                && (string.IsNullOrEmpty(MaterialName) || t.TXZ01.Contains(MaterialName))
                && (string.IsNullOrEmpty(SupplyCode) || t.LIFNR.Contains(SupplyCode))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && (string.IsNullOrEmpty(EBELN) || t.EBELN.Contains(EBELN) || t.ZTEXT.Contains(EBELN))
                && t.IsDelete == false && t.BSART == "Z004");
            page.Total = query.Count();
            var itemPageData = new List<PXC_PurchaseOrder_View>();
            itemPageData = query.OrderByDescending(t => t.EINDT)
                .OrderBy(t => t.EBELN).OrderBy(t => t.EBELP)
                .ToPageList(page.PageNumber, page.PageSize).ToList();
            
            var materialCodeList = itemPageData.Select(t => t.MATNR).ToList();
            // 获取图纸信息
            var blueprintList = _blueprintApp.GetList(t => materialCodeList.Contains(t.MaterialCode)).ToList();
            // 获取图纸接收记录
            var blueprintAcceptList = _blueprintAcceptApp.GetList(t => materialCodeList.Contains(t.MaterialCode) && t.CUser == currentUser.LoginAccount).ToList();
            // 查询所有图纸信息  AEDAT
            foreach (var item in itemPageData)
            {
                var blueprint = getPlm_Blueprint(blueprintList, item);
                if (blueprint != null)
                {
                    item.DownUrl = "/Plm/Plm_Blueprint/DownloadBlueprint?id=" + blueprint.Id;
                    item.FileName = Path.GetFileName(blueprint.DownUrl);
                    var blueprintAccepts = blueprintAcceptList.Where(t => t.MaterialCode == blueprint.MaterialCode && t.Version == blueprint.Version && t.CUser == currentUser.LoginAccount).FirstOrDefault();
                    if (blueprintAccepts != null)
                    {
                        item.AcceptStatus = blueprintAccepts.Status;
                        item.AcceptId = blueprintAccepts.Id;
                    }
                    else
                    {
                        item.AcceptStatus = 1;
                    }
                }
                else
                {
                    item.AcceptStatus = 0;
                }
            }
            // 查询接收到的图纸信息
            return itemPageData;
        }
        
        
        private Plm_Blueprint getPlm_Blueprint(List<Plm_Blueprint> plmBlueprints,PXC_PurchaseOrder_View pxcPurchaseOrderView)
        {
            // 过滤有效日期不为空且大于给定日期的记录
            return plmBlueprints.Where(t => t.MaterialCode == pxcPurchaseOrderView.MATNR).Where(b => b.EffectiveDate != null && b.EffectiveDate < pxcPurchaseOrderView.AEDAT)
                // 按生效日期降序排序
                .OrderByDescending(b => b.EffectiveDate)
                // 获取第一条记录（最新）
                .FirstOrDefault();
        }
        #endregion


        #region 选择采购订单信息和消耗数量等信息
        /// <summary>
        /// 采购订单送货批次配置信息
        /// </summary>
        ///<param name="lstEntity"></param>
        /// <returns></returns>
        public List<PXC_PurchaseApplyQty_View> GetPurchaseApplyQty(List<DeliveryBatchDto> lstEntity)
        {
            lstEntity = lstEntity.Distinct(new Compare()).ToList();
            var alllyList = new List<PXC_PurchaseApplyQty_View>();
            foreach (var item in lstEntity)
            {
                var itemsData = this.DbContext.Queryable<PXC_PurchaseApplyQty_View>()
                .Where(t => t.EBELN == item.EBELN && t.EBELP == item.EBELP).ToList();
                alllyList.AddRange(itemsData);
            }
            return alllyList;
        }
        #endregion

        #region 导出
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="EBELN"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        public List<PXC_PurchaseOrder_View> GetAllExportData(string SupplyCode, string EBELN, string Status, string MaterialCode, string MaterialName, DateTime StartTime, DateTime EndTime, string CompanyCode)
        {
            var query = this.DbContext.Queryable<PXC_PurchaseOrder_View>()
                .Where(t => (t.EINDT >= StartTime) && (t.EINDT < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(MaterialCode) || t.MATNR.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                && (string.IsNullOrEmpty(MaterialName) || t.TXZ01.Contains(MaterialName))
                && (string.IsNullOrEmpty(SupplyCode) || t.LIFNR.Contains(SupplyCode))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && (string.IsNullOrEmpty(EBELN) || t.EBELN.Contains(EBELN) || t.ZTEXT.Contains(EBELN))
                && t.IsDelete == false && t.BSART == "Z004");
           
            var lstEntity = new List<PXC_PurchaseOrder_View>();
            lstEntity = query.OrderByDescending(t => t.EINDT)
                .OrderBy(t => t.EBELN)
                .OrderBy(t => t.EBELP).ToList();
            return lstEntity;
        }
        #endregion
    }
}
