using AOS.Core.Http;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Dto;
using AOS.SRM.Entity.PXC.ViewModel.PurchaseOrder;
using AOS.SRM.Entity.SAP;
using HZ.WMS.Application.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.PXC.OutsourcingProcurement
{

    /// <summary>
    /// 采购送货批次
    /// </summary>

    public class p_DeliveryPlanApp : BaseApp<P_DeliveryBatch>
    {
        p_OutsourcingOrderApp _purchaseorderstatusApp = new p_OutsourcingOrderApp();
        P_InspectionApp _inspecDetailApp = new P_InspectionApp();
        Sys_SAPCompanyInfoApp _sap = new Sys_SAPCompanyInfoApp();

        #region 查询导出数据
        /// <summary>
        /// 查询导出数据
        /// </summary>
        /// <param name="OrderNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="status"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        public List<PXC_PurchaseDeliveryBatch_View> GetAllExportData(string OrderNo, string SupplyCode, string MaterialCode, string MaterialName, DateTime StartTime, DateTime EndTime, string status, string CompanyCode)
        {
            var query = this.DbContext.Queryable<PXC_PurchaseDeliveryBatch_View>()
                .Where(t => (t.EINDT >= StartTime) && (t.EINDT < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(OrderNo) || t.OrderNo.Contains(OrderNo) || t.ZTEXT.Contains(OrderNo))
                && (string.IsNullOrEmpty(SupplyCode) || t.LIFNR.Contains(SupplyCode))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && (string.IsNullOrEmpty(MaterialCode) || t.ItemCode.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                && (string.IsNullOrEmpty(MaterialName) || t.MaterialName.Contains(MaterialName))
                ).Where(T => T.BSART == "Z004" && T.IsDelete == false);

            if (status == "0")//未（部分）制作报检单
            {
                query = query.Where(t => t.BatchQty > t.HasInspectionQty);
            }
            else if (status == "2")//已制作报检单
            {
                query = query.Where(t => t.BatchQty == t.HasInspectionQty);
            }
            var lstEntity = new List<PXC_PurchaseDeliveryBatch_View>();
            lstEntity = query.OrderByDescending(t => t.DeliveryToDate)
                .OrderBy(t => t.OrderNo)
                .OrderBy(t => t.EBELP).ToList();
            return lstEntity;
        }
        #endregion

        #region 制作送货批次
        /// <summary>
        /// 制作送货批次
        /// </summary>
        /// <param name="lstEntity"></param>
        ///  <param name="userCode"></param>
        /// <returns></returns>
        public void AddDeliveryBatch(List<P_DeliveryBatch> lstEntity, string userCode)
        {
            try
            {
                List<P_PurchaseOrderStatus> lstPStatusAdd = new List<P_PurchaseOrderStatus>();
                List<P_PurchaseOrderStatus> lstPstatusUpdate = new List<P_PurchaseOrderStatus>();

                lstEntity.ForEach(t =>
                {
                    t.CUser = userCode;
                    t.Unit = (t.Unit == "PC" ? "ST" : t.Unit);
                    var pStatus = _purchaseorderstatusApp.GetFirstEntity(m => m.OrderNo == t.OrderNo && m.OrderLine == t.OrderLine);
                    if (pStatus == null || string.IsNullOrEmpty(pStatus.OrderNo))
                    {
                        var isHave = lstPStatusAdd.Where(x => x.OrderNo == t.OrderNo && x.OrderLine == t.OrderLine).FirstOrDefault();
                        if (isHave == null)
                        {
                            P_PurchaseOrderStatus status = new P_PurchaseOrderStatus();
                            //status.CompanyCode = "2002"; //cc-220713注释
                            //status.FactoryCode = "2002"; //cc-220713注释
                            //cc-220713添加
                            if (t.PurchaseORG == "XF10")
                            {
                                status.CompanyCode = "2002";
                                status.FactoryCode = "2002";
                            }else if (t.PurchaseORG == "XF20")
                            {
                                status.CompanyCode = "2020";
                                status.FactoryCode = "2020";
                            }
                            status.OrderNo = t.OrderNo;
                            status.OrderLine = t.OrderLine;
                            status.Status = "2";
                            status.CUser = userCode;
                            status.CTime = DateTime.Now;
                            lstPStatusAdd.Add(status);
                        }
                    }
                    else
                    {
                        pStatus.Status = "2";
                        pStatus.MUser = userCode;
                        lstPstatusUpdate.Add(pStatus);
                    }
                });
                DbContext.Ado.BeginTran();
                _purchaseorderstatusApp.DbContext = this.DbContext;

                Insert(lstEntity);
                if (lstPStatusAdd.Count > 0)
                {
                    _purchaseorderstatusApp.Insert(lstPStatusAdd);
                }
                if (lstPstatusUpdate.Count > 0)
                {
                    _purchaseorderstatusApp.Update(lstPstatusUpdate);
                }

                DbContext.Ado.CommitTran();
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                throw ex;
            }
        }
        #endregion

        #region 删除当前列表
        /// <summary>
        /// 删除当前列表   （如果已经保检，则无法进行删除）
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="deleteUser"></param>
        /// <param name="msg"></param>
        /// <returns></returns>
        public bool DeleteDeliveryBatch(string[] ids, string deleteUser, out string msg)
        {
            try
            {
                msg = "";
                List<P_DeliveryBatch> deliveryList = GetListByKeys(ids);
                bool bResult = _inspecDetailApp.Any(t => ids.Contains(t.DeliveryBatchId));
                if (bResult)
                {
                    msg = "存在已制作报检单的数据，不允许删除";
                    return false;
                }
                var orderStatusList = new List<P_PurchaseOrderStatus>();
                deliveryList.ForEach(item =>
                {
                    var isExist = Any(t => t.Id != item.Id && t.OrderNo == item.OrderNo && t.OrderLine == item.OrderLine);
                    if (!isExist)
                    {
                        var data = _purchaseorderstatusApp.GetFirstEntity(t => t.OrderNo == item.OrderNo && t.OrderLine == item.OrderLine);
                        if (data != null && !string.IsNullOrEmpty(data.OrderNo))
                        {
                            data.DUser = deleteUser;
                            data.DTime = DateTime.Now;
                            orderStatusList.Add(data);
                        }
                    }
                });
                DbContext.Ado.BeginTran();
                _purchaseorderstatusApp.DbContext = this.DbContext;

                Delete(deliveryList, deleteUser);
                _purchaseorderstatusApp.Delete(orderStatusList);

                DbContext.Ado.CommitTran();

                if (orderStatusList.Count > 0)
                {
                    //#region 采购订单状态回写
                    //bool isPosted = true;
                    //var lstEntity = orderStatusList.Select(t => new ZFGSRM007() { EBELN = t.OrderNo, EBELP = (int)t.OrderLine, LOEKZ = "O" }).ToList();
                    //_sap.ZFGSRM007("001", lstEntity, out isPosted, out msg);
                    //#endregion
                }
            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                msg = ex.Message;
                return false;
            }
            
            return true;
        }
        #endregion

        #region 编辑当前采购送货批次(只允许编辑批次和数量)
        /// <summary>
        /// 编辑当前采购送货批次  （如果已经保检，则无法进行修改）未加
        /// </summary>
        /// <param name="P_DeliveryBatch"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool UpdateDeliveryBatch(P_DeliveryBatch P_DeliveryBatch, out string error_message)
        {
            error_message = "";
            try
            {
                var query = GetList(x => x.Id == P_DeliveryBatch.Id).ToList().FirstOrDefault();
                if (query != null)
                {
                    query.BatchNum = P_DeliveryBatch.BatchNum;
                    query.BatchQty = P_DeliveryBatch.BatchQty;
                    query.DeliveryToDate = P_DeliveryBatch.DeliveryToDate;
                }
                Update(query);
                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }
        #endregion

        #region 根据供应商、采购单号以及送货日期查询数据
        /// <summary>
        /// 制作送货批次页面数据查询 
        /// </summary>
        /// <returns></returns>
        public List<PXC_PurchaseDeliveryBatch_View> GetPurchaseDeliveryBatch(Pagination page, string OrderNo, string SupplyCode,string MaterialCode, string MaterialName, DateTime StartTime, DateTime EndTime, string status, string CompanyCode)
        {
            var query = this.DbContext.Queryable<PXC_PurchaseDeliveryBatch_View>()
                .Where(t => (t.EINDT >= StartTime) && (t.EINDT < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(OrderNo) || t.OrderNo.Contains(OrderNo) || t.ZTEXT.Contains(OrderNo))
                && (string.IsNullOrEmpty(SupplyCode) || t.LIFNR.Contains(SupplyCode))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && (string.IsNullOrEmpty(MaterialCode) || t.ItemCode.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                && (string.IsNullOrEmpty(MaterialName) || t.MaterialName.Contains(MaterialName))
                && t.BSART == "Z004" && t.IsDelete == false);

            if (status == "0")//未（部分）制作报检单
            {
                query = query.Where(t => t.BatchQty > t.HasInspectionQty);
            }
            else if (status == "2")//已制作报检单
            {
                query = query.Where(t => t.BatchQty == t.HasInspectionQty);
            }
            page.Total = query.Count();
            var itemPageData = new List<PXC_PurchaseDeliveryBatch_View>();
            itemPageData = query.OrderByDescending(t => t.DeliveryToDate)
                .OrderBy(t => t.OrderNo)
                .OrderBy(t => t.EBELP)
                .ToPageList(page.PageNumber, page.PageSize).ToList();
            return itemPageData;
        }
        #endregion

        #region 选择委外采购订单信息和消耗数量等信息
        /// <summary>
        /// 送货批次配置信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyName"></param>
        /// <param name="OrderNo"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<PXC_PurchaseApplyQty_View> GetPurchaseApplyQty(Pagination page, string SupplyName, string OrderNo, string Status, string MaterialCode, string MaterialName, DateTime StartTime, DateTime EndTime)
        {
            var query = this.DbContext.Queryable<PXC_PurchaseApplyQty_View>()
                .Where(t => (t.EINDT >= StartTime) && (t.EINDT < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(MaterialCode) || t.MATNR.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode))
                && (string.IsNullOrEmpty(MaterialName) || t.TXZ01.Contains(MaterialName))
                && (string.IsNullOrEmpty(SupplyName) || t.NAME1.Contains(SupplyName))
                && (string.IsNullOrEmpty(OrderNo) || t.EBELN.Contains(OrderNo))
                && t.IsDelete == false && t.BSART == "Z004");
            page.Total = query.Count();
            var itemPageData = new List<PXC_PurchaseApplyQty_View>();
            itemPageData = query.ToPageList(page.PageNumber, page.PageSize).ToList();
            return itemPageData;
        }
        #endregion

        #region 委外采购订单页面供应商点击接收采购需求时状态改变、自动生成委外送货计划
        /// <summary>
        /// 采购订单页面供应商点击接收采购需求时状态改变（订单编号和订单行号）
        /// </summary>
        /// <returns></returns>
        public bool UpdatePurchaseOrderStatus(List<DeliveryBatchDto> dto, string user, out string supplyCode, out string supplyName)
        {
            try
            {
                List<P_PurchaseOrderStatus> statusList = new List<P_PurchaseOrderStatus>();
                List<P_DeliveryBatch> lstDeliveryBatch = new List<P_DeliveryBatch>();//送货计划
                supplyCode = "";
                supplyName = "";

                for (int i = 0; i < dto.Count; i++)
                {
                    var a = dto[i].EBELN;
                    var b = dto[i].EBELP;
                    var isExist = Any(t => t.OrderNo == a && t.OrderLine == b);
                    if (!isExist)
                    {
                        P_PurchaseOrderStatus status = new P_PurchaseOrderStatus();
                        //status.CompanyCode = "2002"; //cc-220713注释
                        //status.FactoryCode = "2002"; //cc-220713注释
                        status.OrderNo = a;
                        status.OrderLine = b;
                        status.Status = "2";
                        status.CUser = user;
                        status.CTime = DateTime.Now;
                        //statusList.Add(status); //cc-220713注释

                        #region 自动生成送货计划
                        var data = DbContext.Queryable<PXC_PurchaseOrder_View>().Where(t => t.EBELN == a && t.EBELP == b).ToList().FirstOrDefault();
                        if (data != null)
                        {
                            supplyCode = data.LIFNR;
                            supplyName = data.NAME1;

                            //cc-220713添加s
                            status.CompanyCode = data.BUKRS;
                            status.FactoryCode = data.WERKS;
                            statusList.Add(status);
                            //cc-220713添加e

                            P_DeliveryBatch batchEntity = new P_DeliveryBatch()
                            {
                                Id = Guid.NewGuid().ToString(),
                                OrderType = data.BSART,
                                OrderNo = data.EBELN,
                                OrderLine = data.EBELP,
                                //CompanyCode = "2002", //cc-220713注释
                                //FactoryCode = "2002", //cc-220713注释
                                //cc-220713添加s
                                CompanyCode = data.BUKRS,
                                FactoryCode = data.WERKS,
                                //cc-220713添加e
                                SupplyCode = data.LIFNR,
                                SupplyName = data.NAME1,
                                ItemCode = data.MATNR,
                                ItemName = data.TXZ01,
                                BatchNum = data.BatchNum,
                                ProjectType = data.PSTYP,
                                WhsCode = data.LGORT,
                                WhsAddress = data.LGOBE,
                                BatchQty = data.MENGE,
                                DeliveryDate = data.EINDT,
                                DeliveryToDate = data.EINDT,
                                SaleNo = data.SaleNo,
                                SaleLineNo = data.SaleLineNo,
                                PurchaseORG = data.PurchaseORG,
                                PurchaseGroup = data.PurchaseGroup,
                                Unit = (data.Unit == "PC" ? "ST" : data.Unit),
                                CUser = user,
                                CTime = DateTime.Now
                            };
                            lstDeliveryBatch.Add(batchEntity);
                        }

                        #endregion
                    }
                }
                DbContext.Ado.BeginTran();
                _purchaseorderstatusApp.DbContext = this.DbContext;

                _purchaseorderstatusApp.Insert(statusList);
                if (lstDeliveryBatch.Count > 0)
                {
                    Insert(lstDeliveryBatch);
                }

                DbContext.Ado.CommitTran();

                //#region 采购订单状态回写
                //bool isPosted = true;
                //string msg = "";
                //var lstEntity = statusList.Select(t => new ZFGSRM007() { EBELN = t.OrderNo, EBELP = (int)t.OrderLine, LOEKZ = "X" }).ToList();
                //_sap.ZFGSRM007("001", lstEntity, out isPosted, out msg);
                //#endregion

            }
            catch (Exception ex)
            {
                DbContext.Ado.RollbackTran();
                throw ex;
            }
            return true;

        }
        #endregion

        /// <summary>
        /// 采购订单解锁
        /// </summary>
        /// <param name="orderStatusList"></param>
        /// <returns></returns>
        public bool OrderUnLock(List<P_PurchaseOrderStatus> orderStatusList)
        {
            #region 采购订单状态回写
            bool isPosted = true;
            string msg = "";
            var lstEntity = orderStatusList.Select(t => new ZFGSRM007() { EBELN = t.OrderNo, EBELP = (int)t.OrderLine, LOEKZ = "O" }).ToList();
            _sap.ZFGSRM007("001", lstEntity, out isPosted, out msg);
            #endregion
            return true;
        }
    }
}
