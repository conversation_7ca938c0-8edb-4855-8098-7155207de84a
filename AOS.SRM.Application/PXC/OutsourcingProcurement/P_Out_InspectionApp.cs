using AOS.Core.Http;
using AOS.SRM.Entity.FO;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Dto;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.PXC.OutsourcingProcurement
{
    /// <summary>
    /// 委外报检
    /// </summary>
    public class P_Out_InspectionApp : BaseApp<P_InspectionDetail>

    {
        /// <summary>
        /// 
        /// </summary>
        public class InspectionApp : BaseApp<P_Inspection> { };
        InspectionApp app = new InspectionApp();

        #region 根据报检单号和供应商和时间查询报检单页面
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="InspectionNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="orderNo"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        public List<PXC_MakeInspection_View> GetMakeInspection(Pagination page, string InspectionNo, string SupplyCode, string MaterialCode, string orderNo, string Status, DateTime StartTime, DateTime EndTime, string CompanyCode)
        {
            var query = this.DbContext.Queryable<PXC_MakeInspection_View>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(InspectionNo) || t.InspectionNo.Contains(InspectionNo))
                && (string.IsNullOrEmpty(MaterialCode) || t.MATNR.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode) || t.TXZ01.Contains(MaterialCode))
                && (string.IsNullOrEmpty(orderNo) || t.OrderNo.Contains(orderNo) || t.ZTEXT.Contains(orderNo))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyerCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && t.BSART == "Z004" && t.IsDelete == false);
            if (!string.IsNullOrEmpty(Status))
            {
                if (Status == "1")//未检验
                {
                    query = query.Where(t => (t.QualifiedQty == null || t.QualifiedQty < t.InspectionQty));
                }
                else if (Status == "2")//未入库
                {
                    query = query.Where(t => (t.StorageQty == null || t.StorageQty < t.InspectionQty));
                }
            }
            page.Total = query.Count();
            var itemPageData = new List<PXC_MakeInspection_View>();
            itemPageData = query.OrderByDescending(t => t.CTime)
                .OrderBy(t => t.InspectionNo)
                .OrderBy(t => t.InspectionLine)
                .ToPageList(page.PageNumber, page.PageSize).ToList();
            return itemPageData;
        }
        #endregion

        #region 取消送货报检
        /// <summary>
        ///取消送货报检   删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="userCode"></param>
        /// <returns></returns>
        public int UpdateInspection(string[] ids, string userCode)
        {
            List<P_InspectionDetail> InspectionDetail = GetListByKeys(ids);
            InspectionDetail.ForEach(t =>
            {
                t.DUser = userCode;
            });
            return DeleteWithTran(InspectionDetail);
        }
        #endregion

        #region 查询导出数据
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="InspectionNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="orderNo"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="Status"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        public List<PXC_MakeInspection_View> GetAllExportData(string InspectionNo, string SupplyCode, string MaterialCode, string orderNo, DateTime StartTime, DateTime EndTime, string Status, string CompanyCode)
        {
            var query = this.DbContext.Queryable<PXC_MakeInspection_View>()
                .Where(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(InspectionNo) || t.InspectionNo.Contains(InspectionNo))
                && (string.IsNullOrEmpty(MaterialCode) || t.MATNR.Contains(MaterialCode) || t.BISMT.Contains(MaterialCode) || t.TXZ01.Contains(MaterialCode))
                && (string.IsNullOrEmpty(orderNo) || t.OrderNo.Contains(orderNo) || t.ZTEXT.Contains(orderNo))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyerCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(CompanyCode) || t.WERKS.Contains(CompanyCode))
                && t.BSART == "Z004" && t.IsDelete == false);
            if (!string.IsNullOrEmpty(Status))
            {
                if (Status == "1")//未检验
                {
                    query = query.Where(t => (t.QualifiedQty == null || t.QualifiedQty < t.InspectionQty));
                }
                else if (Status == "2")//未入库
                {
                    query = query.Where(t => (t.StorageQty == null || t.StorageQty < t.InspectionQty));
                }
            }
            var lstEntity = new List<PXC_MakeInspection_View>();
            lstEntity = query.OrderByDescending(t => t.CTime)
                 .OrderBy(t => t.InspectionNo)
                 .OrderBy(t => t.InspectionLine).ToList();
            return lstEntity;
        }
        #endregion

        #region 制作报检单页面   

        #region 删除分录行
        /// <summary>
        /// 删除分录行
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="deleteUser"></param>
        /// <returns></returns>
        public int DeleteInspectionDetail(string[] ids, string deleteUser)
        {
            List<P_InspectionDetail> deliveryList = GetListByKeys(ids);
            return DeleteWithTran(deliveryList, deleteUser);
        }
        #endregion

        #region 保存
        /// <summary>
        /// 保存功能
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public bool InsertInspection(P_InspectionDetail_Views dto, string UserName)
        {
            try
            {
                P_Inspection Inspection = new P_Inspection();
                Inspection.InspectionNo = dto.InspectionNo;
                Inspection.CTime = DateTime.Now;
                Inspection.CUser = UserName;
                //Inspection.CompanyCode = "2002"; //cc-220713注释
                Inspection.Remark = dto.Remark;

                int i = 0;
                foreach (var item in dto.P_InspectionDetail)
                {
                    i++;
                    item.InspectionLine = i;
                    item.Unit = (item.Unit == "PC" ? "ST" : item.Unit);
                    //cc-220713添加
                    if (item.PurchaseORG == "XF10")
                    {
                        Inspection.CompanyCode = "2002";
                        Inspection.FactoryCode = "2002";
                        item.CompanyCode = "2002";
                        item.FactoryCode = "2002";
                    }else if (item.PurchaseORG == "XF20")
                    {
                        Inspection.CompanyCode = "2020";
                        Inspection.FactoryCode = "2020";
                        item.CompanyCode = "2020";
                        item.FactoryCode = "2020";
                    }
                }

                app.Insert(Inspection);
                Insert(dto.P_InspectionDetail);
                return true;
            }
            catch (Exception ex)
            {
                return false;
                throw ex;
            }
        }
        #endregion

        //#region 查询供应商
        ///// <summary>
        ///// 查询供应商
        ///// </summary>
        ///// <param name="SupplierName"></param>
        ///// <param name="page"></param>
        ///// <returns></returns>
        //public List<S_SupplierInfo> GetSupplierCode(Pagination page, string SupplierName)
        //{
        //    if (string.IsNullOrEmpty(page.Sort))
        //    {
        //        page.Sort = "SupplierName desc";
        //    }
        //    else
        //    {
        //        page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
        //    }
        //    var itemsData = this.DbContext.Queryable<S_SupplierInfo>().Where(t => string.IsNullOrEmpty(SupplierName) || t.NAME1.Contains(SupplierName)).ToList();
        //    int total = itemsData.Count();
        //    page.Total = total;
        //    var itemPageData = new List<S_SupplierInfo>();
        //    if (page.PageSize > total)
        //    {
        //        itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
        //    }
        //    else
        //    {
        //        itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
        //    }
        //    return itemPageData;
        //}
        //#endregion

        #endregion

        #region 校验是否存在WMS
        /// <summary>
        /// 
        /// </summary>
        /// <param name="InspectionNo">报检单号</param>
        /// <param name="InspectionLine">行号</param>
        /// <returns></returns>
        public bool IsExistsInWMS(string InspectionNo, int InspectionLine)
        {
            var data = DbContextForWMS.Queryable<MM_Warehousing>()
                .Where(t => t.InspectionNum == InspectionNo && t.InspectionLine == InspectionLine && t.IsDelete == false).ToList();
            if (data != null && data.Count > 0) return true;
            else return false;
        }
        #endregion
    }
}
