using AOS.Core.Http;
using AOS.SRM.Entity.Basic.ViewModel;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.ViewModel;
using AOS.SRM.Entity.Sys;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.PXC.LogisticsIOrderApp
{
    /// <summary>
    /// 物流订单
    /// </summary>


    public class P_LogisticsIOrderApp : BaseApp<P_PurchaseServerOrderStatus>
    {

        #region 查询物流订单
        /// <summary>
        /// 查询物流订单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="DocNum"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="CustomerName"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<PXC_ConsignmentNote_View> GetLogisticsIOrder(Pagination page, string SupplyCode, string DocNum, string Status, string CustomerName, DateTime StartTime, DateTime EndTime)
        {
            var query = this.DbContext.Queryable<PXC_ConsignmentNote_View>()
                .Where(t => (t.DeliveryDate >= StartTime) && (t.DeliveryDate < EndTime.AddDays(1))
                 && (string.IsNullOrEmpty(SupplyCode) || t.LogisticsSupplierCode.Contains(SupplyCode))
                 && (string.IsNullOrEmpty(CustomerName) || t.CustomerName.Contains(CustomerName))
                 && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                 && (string.IsNullOrEmpty(DocNum) || t.DocNum.Contains(DocNum))
                 );
            page.Total = query.Count();
            var itemPageData = new List<PXC_ConsignmentNote_View>();
            itemPageData = query.ToPageList(page.PageNumber, page.PageSize)
                .OrderBy(t => t.CustomerName)
                .ThenBy(t => t.DeliveryDate).ToList();
            return itemPageData;
        }
        #endregion

        #region 查询物流订单明细
        /// <summary>
        /// 查询物流订单
        /// </summary>
        /// <param name="page"></param>
        /// <param name="DocNum"></param>
        /// <returns></returns>
        public List<PXC_ConsignmentNoteDetail_View> GetLogisticsIOrderDetail(Pagination page, string DocNum)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var itemsData = this.DbContext.Queryable<PXC_ConsignmentNoteDetail_View>().Where(t => t.DocNum == DocNum).ToList();
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<PXC_ConsignmentNoteDetail_View>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        #endregion

        #region 物流订单接收采购需求
        /// <summary>
        /// 物流订单接收采购需求
        /// </summary>
        /// <returns></returns>
        public int ModifyDispatchStatus(string[] orderNo, string userCode, out string supplyCode, out string supplyName)
        {
            supplyCode = "";
            supplyName = "";

            //DbContext.Ado.BeginTran();
            List<P_PurchaseServerOrderStatus> statusList = new List<P_PurchaseServerOrderStatus>();
            for (int i = 0; i < orderNo.Length; i++)
            {
                var No = orderNo[i];
                var isExist = Any(t => t.OrderNo == No);
                if (!isExist)
                {
                    var list = DbContextForWMS.Queryable<SD_ConsignmentNote>().Where(x => x.DocNum == No).ToList();
                    foreach (var item in list)
                    {
                        supplyCode = item.LogisticsSupplierCode;
                        supplyName = item.LogisticsSupplierName;
                        List<SD_ConsignmentNote> SD_ConsignmentNote = new List<SD_ConsignmentNote>();
                        item.Status = 1;
                        UpdateForWMS(item);
                    }

                    P_PurchaseServerOrderStatus status = new P_PurchaseServerOrderStatus();
                    status.CompanyCode = "2002";
                    status.FactoryCode = "2002";
                    status.OrderNo = orderNo[i];

                    status.Status = "1";
                    status.CUser = userCode;
                    status.CTime = DateTime.Now;
                    status.IsUse = false;
                    status.IsUse2 = false;
                    status.SupplyCode = supplyCode;
                    status.SupplyName = supplyName;
                    statusList.Add(status);
                }
            }
            int insertcount = InsertWithTran(statusList);
            return insertcount;
            // DbContext.Ado.CommitTran();

        }
        #endregion

        #region 点击派车
        /// <summary>
        /// 点击修改是否派车状态
        /// </summary>
        /// <param name="lstEntity"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public int UpdateDispatchStatus(List<P_PurchaseServerOrderStatus> lstEntity, string user)
        {

            foreach (var item in lstEntity)
            {
                item.CTime = DateTime.Now;
                item.Status = "2";
            }
            return UpdateWithTran(lstEntity);
        }
        #endregion

        #region 取消派车
        /// <summary>
        /// 取消派车
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public int CancelDispatch(string[] ids, string user)
        {
            List<P_PurchaseServerOrderStatus> PurchaseServerOrderStatus = GetListByKeys(ids);

            foreach (var item in PurchaseServerOrderStatus)
            {
                if (item.Status != "3" && item.Status != "4")
                {
                    item.Status = "5";
                }
            }
            return UpdateWithTran(PurchaseServerOrderStatus);
        }
        #endregion

        //#region 物流需求订单完成更新状态

        /// <summary>
        /// 多个实体更新
        /// </summary>
        /// <param name="entities"></param>
        /// <returns>返回更新行数</returns>
        public int UpdateForWMS(List<P_InspectionDetail> entities)
        {
            var iCount = 0;
            try
            {
                foreach (P_InspectionDetail entity in entities)
                {
                    DbContextForWMS.Updateable(entity).ExecuteCommand();
                    iCount++;
                }
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public int UpdateForWMS(SD_ConsignmentNote entities)
        {
            var iCount = 0;
            try
            {
                //foreach (P_InspectionDetail entity in entities)
                //{
                DbContextForWMS.Updateable(entities).ExecuteCommand();
                iCount++;
                //}
            }
            catch (Exception ex)
            {
                iCount = 0;
                throw new Exception(ex.Message);
            }
            return iCount;
        }

        //#endregion


        #region 导出数据查询
        /// <summary>
        /// 查询物流订单
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="DocNum"></param>
        /// <param name="Status"></param>
        /// <param name="CustomerName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        public List<PXC_ConsignmentNote_Export> GetAllExportData(string SupplyCode, string DocNum, string Status, string CustomerName, DateTime StartTime, DateTime EndTime)
        {
            var itemsData = this.DbContext.Queryable<PXC_ConsignmentNote_Export>().
                Where(t => (t.DeliveryDate >= StartTime) && (t.DeliveryDate < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(SupplyCode) || t.LogisticsSupplierCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(CustomerName) || t.CustomerName.Contains(CustomerName))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(DocNum) || t.LogisticsNo.Contains(DocNum)))
                .OrderBy(t => t.CustomerName).OrderBy(t => t.DeliveryDate).ToList();

            return itemsData;
        }
        #endregion

    }
}
