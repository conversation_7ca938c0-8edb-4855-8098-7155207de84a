using System;
using System.Text.RegularExpressions;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.PLM;
using SqlSugar;

namespace AOS.SRM.Application.Plm
{
    /// <summary>
    /// Oracle文件应用程序类
    /// </summary>
    public class PlmOracleFileApp
    {
        /// <summary>
        /// 获取SqlSugar客户端
        /// </summary>
        private SqlSugarClient DbContext
        {
            get { return OracleDbContext.GetInstance(); }
        }

        /// <summary>
        /// 根据文件ID获取文件信息
        /// </summary>
        /// <param name="fileId">文件ID</param>
        /// <returns>文件信息</returns>
        public PlmOracleFile GetFileById(string fileId)
        {
            try
            {
                return DbContext.Queryable<PlmOracleFile>()
                    .Where(f => f.FileId == fileId)
                    .First();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Oracle查询异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从URL中提取文件ID并获取文件信息
        /// </summary>
        /// <param name="url">文件URL</param>
        /// <returns>文件信息</returns>
        public PlmOracleFile GetFileByUrl(string url)
        {
            string fileId = ExtractFileIdFromUrl(url);
            if (string.IsNullOrEmpty(fileId))
            {
                return null;
            }

            return GetFileById(fileId);
        }

        /// <summary>
        /// 从URL中提取文件ID
        /// </summary>
        /// <param name="url">文件URL</param>
        /// <returns>文件ID</returns>
        public string ExtractFileIdFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return null;
            }

            try
            {
                string fileId = null;

                // 尝试从URL中提取ID - 例如从 http://plm.hzforward.com/Windchill/servlet/rest/DownResources/download?oid=OR:wt.content.ApplicationData:139974048
                // 提取 139974048 部分
                if (url.Contains("ApplicationData:"))
                {
                    var parts = url.Split(new[] { "ApplicationData:" }, StringSplitOptions.None);
                    if (parts.Length > 1)
                    {
                        fileId = parts[1].Trim();
                        // 移除可能的查询参数
                        if (fileId.Contains("&") || fileId.Contains("?"))
                        {
                            fileId = fileId.Split(new[] { '&', '?' })[0];
                        }
                    }
                }
                else if (url.Contains("oid="))
                {
                    var parts = url.Split(new[] { "oid=" }, StringSplitOptions.None);
                    if (parts.Length > 1)
                    {
                        var lastPart = parts[1].Trim();
                        // 移除可能的其他查询参数
                        if (lastPart.Contains("&"))
                        {
                            lastPart = lastPart.Split('&')[0];
                        }
                        
                        if (lastPart.Contains(":"))
                        {
                            var subParts = lastPart.Split(':');
                            fileId = subParts[subParts.Length - 1];
                        }
                        else
                        {
                            fileId = lastPart;
                        }
                    }
                }
                
                // 确保fileId只包含数字
                if (!string.IsNullOrEmpty(fileId))
                {
                    // 使用正则表达式提取数字部分
                    var numericPart = Regex.Match(fileId, @"\d+").Value;
                    if (!string.IsNullOrEmpty(numericPart))
                    {
                        fileId = numericPart;
                    }
                }
                
                System.Diagnostics.Debug.WriteLine($"从URL提取的文件ID: {fileId}");
                return fileId;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从URL提取文件ID异常: {ex.Message}");
                return null;
            }
        }
    }
} 