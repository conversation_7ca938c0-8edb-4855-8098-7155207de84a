using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.PLM;
using SqlSugar;

namespace AOS.SRM.Application.Plm
{
    /// <summary>
    /// 图纸应用程序类
    /// </summary>
    public class Plm_BlueprintApp : BaseApp<Plm_Blueprint>
    {
        /// <summary>
        /// 根据条件获取分页列表
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="condition">查询条件</param>
        /// <returns>图纸列表</returns>
        public List<Plm_Blueprint> GetPageList(AOS.Core.Http.Pagination page, Expression<Func<Plm_Blueprint, bool>> condition)
        {
            return base.GetPageList(page, condition);
        }

        /// <summary>
        /// 根据主键获取实体
        /// </summary>
        /// <param name="id">主键</param>
        /// <returns>图纸实体</returns>
        public Plm_Blueprint GetEntityByKey(string id)
        {
            return base.GetEntityByKey(id);
        }

        /// <summary>
        /// 根据条件获取列表
        /// </summary>
        /// <param name="condition">查询条件</param>
        /// <returns>图纸列表</returns>
        public ISugarQueryable<Plm_Blueprint> GetList(Expression<Func<Plm_Blueprint, bool>> condition)
        {
            return base.GetList(condition);
        }

        /// <summary>
        /// 根据主键数组获取实体列表
        /// </summary>
        /// <param name="keys">主键数组</param>
        /// <returns>图纸列表</returns>
        public List<Plm_Blueprint> GetListByKeys(string[] keys)
        {
            return base.GetListByKeys(keys);
        }

        /// <summary>
        /// 插入单个实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>插入后的实体</returns>
        public Plm_Blueprint Insert(Plm_Blueprint entity)
        {
            return base.Insert(entity);
        }

        /// <summary>
        /// 插入多个实体
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>插入数量</returns>
        public int Insert(List<Plm_Blueprint> entities)
        {
            return base.Insert(entities);
        }

        /// <summary>
        /// 更新单个实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>更新数量</returns>
        public int Update(Plm_Blueprint entity)
        {
            return base.Update(entity);
        }

        /// <summary>
        /// 更新多个实体
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>更新数量</returns>
        public int Update(List<Plm_Blueprint> entities)
        {
            return base.Update(entities);
        }

        /// <summary>
        /// 删除单个实体
        /// </summary>
        /// <param name="entity">实体</param>
        /// <returns>删除数量</returns>
        public int Delete(Plm_Blueprint entity)
        {
            return base.Delete(entity);
        }

        /// <summary>
        /// 删除多个实体
        /// </summary>
        /// <param name="entities">实体列表</param>
        /// <returns>删除数量</returns>
        public int Delete(List<Plm_Blueprint> entities)
        {
            return base.Delete(entities);
        }
    }
}