<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{28DBE3A0-F19A-49E4-9CFE-6F8177CB17BF}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AOS.SRM.Application</RootNamespace>
    <AssemblyName>AOS.SRM.Application</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\AOS.SRM.Application.xml</DocumentationFile>
    <PlatformTarget>x64</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=6.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.122.21.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\packages\Oracle.ManagedDataAccess.21.18.0\lib\net462\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="sapnco, Version=3.0.0.42, Culture=neutral, PublicKeyToken=50436dca5c7f7d23, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Sap\sapnco.dll</HintPath>
    </Reference>
    <Reference Include="sapnco_utils, Version=3.0.0.42, Culture=neutral, PublicKeyToken=50436dca5c7f7d23, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Sap\sapnco_utils.dll</HintPath>
    </Reference>
    <Reference Include="SqlSugar, Version=5.1.4.185, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SqlSugar.5.1.4.185\lib\SqlSugar.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Formats.Asn1, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Formats.Asn1.8.0.1\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.6.0.0\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=6.0.0.10, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.6.0.10\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\BaseApp.cs" />
    <Compile Include="Base\BaseAppExt.cs" />
    <Compile Include="Base\ContentBase.cs" />
    <Compile Include="Base\DbConfig.cs" />
    <Compile Include="Base\ExcelExportFormatter.cs" />
    <Compile Include="Base\LanguagePackagePath.cs" />
    <Compile Include="IX\IX_NoticeApp.cs" />
    <Compile Include="FO\I_InvoiceApp.cs" />
    <Compile Include="FO\I_InvoiceDetailApp.cs" />
    <Compile Include="FO\I_InvoiceDisposalApp.cs" />
    <Compile Include="FO\I_ReconciliationApp.cs" />
    <Compile Include="FO\I_ReconciliationDetailApp.cs" />
    <Compile Include="FO\I_ReturnNoApp.cs" />
    <Compile Include="FO\P_ConsignmentNoteApp.cs" />
    <Compile Include="IX\IX_NoticeReceiptApp.cs" />
    <Compile Include="Plm\Plm_BlueprintApp.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PXC\LogisticsIOrderApp\P_LogisticsDispatchCarApp.cs" />
    <Compile Include="PXC\LogisticsIOrderApp\P_LogisticsIOrderApp.cs" />
    <Compile Include="PXC\OutsourcingProcurement\p_DeliveryPlanApp.cs" />
    <Compile Include="PXC\OutsourcingProcurement\p_OutsourcingOrderApp.cs" />
    <Compile Include="PXC\OutsourcingProcurement\P_Out_InspectionApp.cs" />
    <Compile Include="PXC\PurchaseOrderApp\P_Blueprint_AcceptApp.cs" />
    <Compile Include="PXC\P_Blueprint_Non_StandardApp.cs" />
    <Compile Include="PXC\P_Blueprint_Send_RecordApp.cs" />
    <Compile Include="PXC\P_Blueprint_Send_UserApp.cs" />
    <Compile Include="PXC\PurchaseOrderApp\P_InspectionApp.cs" />
    <Compile Include="PXC\PurchaseOrderApp\P_PurchaseDeliveryBatchApp.cs" />
    <Compile Include="PXC\PurchaseOrderApp\P_PurchaseOrderApp.cs" />
    <Compile Include="RPT\RPT_InvoiceDetailsApp.cs" />
    <Compile Include="RPT\PurchaseDeliveryApp.cs" />
    <Compile Include="RPT\RPT_FTT_ViewApp.cs" />
    <Compile Include="RPT\RPT_PLineOutPutApp.cs" />
    <Compile Include="RPT\RPT_PO_InOut_ViewApp.cs" />
    <Compile Include="RPT\RPT_PO_Inspection_ViewApp.cs" />
    <Compile Include="RPT\RPT_SD_Delivery_ViewApp.cs" />
    <Compile Include="RPT\RPT_StockMove_ViewApp.cs" />
    <Compile Include="RPT\RPT_SupplierItem_ViewApp.cs" />
    <Compile Include="RPT\V_NotPostStockMoveApp.cs" />
    <Compile Include="SAP\SAPApp.cs" />
    <Compile Include="SHM\DisposalManagement\CompromiseDisposalApp.cs" />
    <Compile Include="SHM\DisposalManagement\RRDFApp.cs" />
    <Compile Include="SHM\DisposalManagement\DebitNoticeApp.cs" />
    <Compile Include="SHM\DisposalManagement\UnqualifiedNoticeApp.cs" />
    <Compile Include="SHM\SupplierAuditApp.cs" />
    <Compile Include="SHM\SupplierInfoApp.cs" />
    <Compile Include="SHM\S_ContractApp.cs" />
    <Compile Include="SHM\S_DisqualifySupplyApp.cs" />
    <Compile Include="SHM\S_DrawingDistributionRecyclingApp.cs" />
    <Compile Include="SHM\S_EquipPurchaseApp.cs" />
    <Compile Include="SHM\S_PerformanceAppraisalApp.cs" />
    <Compile Include="SHM\S_PerformanceAppraisalBaseApp.cs" />
    <Compile Include="SHM\S_ResumeSupplyQualificationApp.cs" />
    <Compile Include="SHM\S_TechnicalNoticeApp.cs" />
    <Compile Include="SHM\S_StandardsOfBankApp.cs" />
    <Compile Include="SHM\S_SupplierChangeApp.cs" />
    <Compile Include="SHM\S_SupplierWarningLetterApp.cs" />
    <Compile Include="SHM\ViewModel\SupplierDto.cs" />
    <Compile Include="SHM\ViewModel\V_SHM_SupplierDisposalnfo.cs" />
    <Compile Include="Store\StoreSupplierApp.cs" />
    <Compile Include="Store\StoreSupplierDetailApp.cs" />
    <Compile Include="Sys\MD_AttachmentManagementApp.cs" />
    <Compile Include="Sys\Sys_ApiLogConfigApp.cs" />
    <Compile Include="Sys\Sys_AppVersionApp.cs" />
    <Compile Include="Sys\Sys_DbBackupApp.cs" />
    <Compile Include="Sys\Sys_DbBackupConfigApp.cs" />
    <Compile Include="Sys\Sys_DictionaryBaseApp.cs" />
    <Compile Include="Sys\Sys_LogApp.cs" />
    <Compile Include="Sys\Sys_MailApp.cs" />
    <Compile Include="Sys\Sys_MailServerConfigApp.cs" />
    <Compile Include="Sys\Sys_MessageApp.cs" />
    <Compile Include="Sys\Sys_MessageNotifySettingApp.cs" />
    <Compile Include="Sys\Sys_MessageTypeApp.cs" />
    <Compile Include="Sys\Sys_OrganizationApp.cs" />
    <Compile Include="Sys\Sys_DictionaryApp.cs" />
    <Compile Include="Sys\Sys_ResourceApp.cs" />
    <Compile Include="Sys\Sys_RoleApp.cs" />
    <Compile Include="Sys\Sys_RoleResourceApp.cs" />
    <Compile Include="Sys\Sys_SAPCompanyInfoApp.cs" />
    <Compile Include="Sys\Sys_SwithConfigApp.cs" />
    <Compile Include="Sys\Sys_UserApp.cs" />
    <Compile Include="Sys\Sys_UserMessageApp.cs" />
    <Compile Include="Sys\Sys_UserRoleApp.cs" />
    <Compile Include="Sys\Sys_UserSapAccountApp.cs" />
    <Compile Include="Util\HttpUtil.cs" />
    <Compile Include="WMS\PO_PurchaseReceiptApp.cs" />
    <Compile Include="WMS\WMSApp.cs" />
    <Compile Include="Plm\PlmOracleFileApp.cs" />
    <Compile Include="Base\OracleDbContext.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AOS.Core\AOS.Core.csproj">
      <Project>{62d9f685-5537-494e-8d51-4daf877f8af1}</Project>
      <Name>AOS.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\AOS.SRM.Entity\AOS.SRM.Entity.csproj">
      <Project>{a56c0618-c820-42e7-89b5-e5da01c72729}</Project>
      <Name>AOS.SRM.Entity</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Oracle.DataAccess.Common.Configuration.Section.xsd" />
    <Content Include="Oracle.ManagedDataAccess.Client.Configuration.Section.xsd" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>