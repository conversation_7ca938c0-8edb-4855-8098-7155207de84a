using System;
using System.Collections.Generic;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.RPT.ViewModel;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.RPT
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_SupplierItem_ViewApp : ContentBase
    {
        #region 分页查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <param name="currLoginUser"></param>
        /// <returns></returns>
        public List<RPT_SupplierItem_View> GetPageList(Pagination page, string keyword, DateTime[] dateTimes, Sys_User currLoginUser)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            //if (string.IsNullOrEmpty(page.Sort))
            //{
            //    page.Sort = "";
            //}
            //else
            //{
            //    page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            //}
            var itemsData = this.DbContext.Queryable<RPT_SupplierItem_View>().Where(t =>
                    string.IsNullOrEmpty(keyword)
                    || t.ItemCode.Contains(keyword)
                    || t.SupplierCode.Contains(keyword)).Where(t => (currLoginUser.IsSupplier == false || t.SupplierCode == currLoginUser.LoginAccount) && (t.CTime >= fromTime && t.CTime < toTime))
                         .OrderBy(x => x.SupplierCode).OrderBy(x => x.ItemCode).OrderBy(x => x.CTime);
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<RPT_SupplierItem_View>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        #endregion

        #region 导出数据查询
        /// <summary>
        /// 导出数据查询
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <param name="currLoginUser"></param>
        /// <returns></returns>
        public List<RPT_SupplierItem_View> GetAllExportData(string keyword, DateTime[] dateTimes, Sys_User currLoginUser)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            var itemsData = this.DbContext.Queryable<RPT_SupplierItem_View>().Where(t =>
                string.IsNullOrEmpty(keyword)
                || t.ItemCode.Contains(keyword)
                || t.SupplierCode.Contains(keyword)).Where(t => (currLoginUser.IsSupplier == false || t.SupplierCode == currLoginUser.LoginAccount) && (t.CTime >= fromTime && t.CTime < toTime)).ToList();
            return itemsData;
        }
        #endregion
    }
}
