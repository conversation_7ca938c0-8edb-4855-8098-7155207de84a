using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.RPT.ViewModel;

namespace AOS.SRM.Application.RPT
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_SD_Delivery_ViewApp : ContentBase
    {
        #region 分页查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        public List<RPT_SD_Delivery_View> GetPageList(Pagination page, string keyword, DateTime[] dateTimes)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var itemsData = this.DbContext.Queryable<RPT_SD_Delivery_View>().Where(t =>
                string.IsNullOrEmpty(keyword)
                || t.ItemCode.Contains(keyword)
                || t.ItemName.Contains(keyword)
                || t.BaseNum.Contains(keyword)
                || t.CustomerCode.Contains(keyword)).Where(t => (t.CTime >= fromTime && t.CTime < toTime)).OrderBy(page.Sort).ToList();
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<RPT_SD_Delivery_View>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        #endregion

        #region 导出数据查询
        /// <summary>
        /// 导出数据查询
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        public List<RPT_SD_Delivery_View> GetAllExportData(string keyword, DateTime[] dateTimes)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            var itemsData = this.DbContext.Queryable<RPT_SD_Delivery_View>().Where(t =>
                string.IsNullOrEmpty(keyword)
                || t.ItemCode.Contains(keyword)
                || t.ItemName.Contains(keyword)).Where(t => (t.CTime >= fromTime && t.CTime < toTime)).ToList();
            return itemsData;
        }
        #endregion
    }
}
