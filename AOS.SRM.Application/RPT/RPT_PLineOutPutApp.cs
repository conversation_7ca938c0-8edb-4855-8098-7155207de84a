using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.RPT.ViewModel;
using SqlSugar;

namespace AOS.SRM.Application.RPT
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_PLineOutPutApp : ContentBase
    {
        #region 分页查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="PLine"></param>
        /// <param name="ItemCode"></param>
        /// <param name="dateTimes"></param>
        /// <param name="DType"></param>
        /// <returns></returns>
        public List<RPT_PLineOutPut> GetPageList(Pagination page, string PLine, string ItemCode, DateTime[] dateTimes, string DType)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes, true);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var itemsData = PROC_GetAllList(fromTime, toTime, PLine, ItemCode, DType);
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<RPT_PLineOutPut>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        #endregion

        #region 导出数据查询
        /// <summary>
        /// 导出数据查询
        /// </summary>
        /// <param name="PLine"></param>
        /// <param name="ItemCode"></param>
        /// <param name="dateTimes"></param>
        /// <param name="DType"></param>
        /// <returns></returns>
        public List<RPT_PLineOutPut> GetAllExportData(string PLine, string ItemCode, DateTime[] dateTimes, string DType)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            var itemsData = PROC_GetAllList(fromTime, toTime, PLine, ItemCode, DType);
            return itemsData;
        }
        #endregion

        #region 获取存储过程数据
        /// <summary>
        /// 获取存储过程数据
        /// </summary>
        /// <param name="STime"></param>
        /// <param name="ETime"></param>
        /// <param name="PLine"></param>
        /// <param name="ItemCode"></param>
        /// <param name="DType"></param>
        /// <returns></returns>
        public List<RPT_PLineOutPut> PROC_GetAllList(DateTime STime, DateTime ETime, string PLine, string ItemCode, string DType)
        {
            SugarParameter _STime = new SugarParameter("@STime", STime);
            SugarParameter _ETime = new SugarParameter("@ETime", ETime);
            SugarParameter _PLine = new SugarParameter("@PLine", PLine == null ? string.Empty : PLine);
            SugarParameter _ItemCode = new SugarParameter("@ItemCode", ItemCode == null ? string.Empty : ItemCode);
            SugarParameter[] dbParams = { _STime, _ETime, _PLine, _ItemCode };
            string commandText = "PROC_RPT_PLineOutPut";
            return this.DbContext.Ado.SqlQuery<RPT_PLineOutPut>(commandText, dbParams).Where(x => string.IsNullOrEmpty(DType) || x.DType == bool.Parse(DType)).ToList();
        }
        #endregion
    }
}
