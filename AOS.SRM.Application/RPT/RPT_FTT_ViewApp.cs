using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.RPT;

namespace AOS.SRM.Application.RPT
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_FTT_ViewApp : ContentBase
    {
        #region 分页查询报表
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <param name="FType"></param>
        /// <returns></returns>
        public List<RPT_FTT_View> GetPageListByType(Pagination page, string keyword, DateTime[] dateTimes, string FType)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CreateDate desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var itemsData = this.DbContext.Queryable<RPT_FTT_View>().Where(t =>
                string.IsNullOrEmpty(keyword)
                || t.FTTID.Contains(keyword)
                || t.HandlingRecommendations.Contains(keyword)
                || t.NCConditionDescription.Contains(keyword)
                || t.ProductID.Contains(keyword)
                || t.ProductionLineID.Contains(keyword)).Where(t => (string.IsNullOrEmpty(FType) || t.FType == FType) && (t.CreateDate >= fromTime && t.CreateDate < toTime)).OrderBy(page.Sort).ToList();
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<RPT_FTT_View>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        #endregion

        #region 查询导出数据
        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <param name="FType"></param>
        /// <returns></returns>
        public List<RPT_FTT_View> GetAllExportData(string keyword, DateTime[] dateTimes, string FType)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            var itemsData = this.DbContext.Queryable<RPT_FTT_View>().Where(t =>
                string.IsNullOrEmpty(keyword)
                || t.FTTID.Contains(keyword)
                || t.HandlingRecommendations.Contains(keyword)
                || t.NCConditionDescription.Contains(keyword)
                || t.ProductID.Contains(keyword)
                || t.ProductionLineID.Contains(keyword)).Where(t => (string.IsNullOrEmpty(FType) || t.FType == FType) && (t.CreateDate >= fromTime && t.CreateDate < toTime)).ToList();
            return itemsData;
        }
        #endregion
    }
}
