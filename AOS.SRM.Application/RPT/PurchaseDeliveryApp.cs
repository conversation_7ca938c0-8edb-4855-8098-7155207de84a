using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity;
using AOS.SRM.Entity.RPT;
using SqlSugar;

namespace AOS.SRM.Application.RPT
{
    /// <summary>
    /// 采购交货相关报表
    /// </summary>
    public class PurchaseDeliveryApp : BaseApp<BaseEntity>
    {
        /// <summary>
        /// 采购交货进度报表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="deliveryPlanNo"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public IEnumerable<object> GetPurchaseDeliverySchedule(Pagination page, string deliveryPlanNo, DateTime startTime, DateTime endTime)
        {
            SugarParameter paramDeliveryPlanNo = new SugarParameter("@DeliveryDocNum", deliveryPlanNo);
            SugarParameter paramSupplierCode = new SugarParameter("@SupplierCode", "");
            SugarParameter paramStartDate = new SugarParameter("@StartDate", startTime);
            SugarParameter paramEndDate = new SugarParameter("@EndDate", endTime);
            SugarParameter paramPageNumber = new SugarParameter("@PageNumber", page.PageNumber);
            SugarParameter paramPageSize = new SugarParameter("@PageSize", page.PageSize);
            SugarParameter paramTotalCount = new SugarParameter("@TotalCount", null, true) ;
            SugarParameter[] stParams = new SugarParameter[] { paramDeliveryPlanNo, paramSupplierCode, paramStartDate, paramEndDate, paramPageNumber, paramPageSize, paramTotalCount };
            return base.DbContext.Ado.UseStoredProcedure().SqlQuery<object>("PROC_RPT_PurchaseDeliverySchedule", stParams) as IEnumerable<object>;
        }
        /// <summary>
        /// 采购交货进度报表 导出
        /// </summary>
        /// <param name="deliveryPlanNo"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public List<RPT_PurchaseDeliverySchedule> GetPurchaseDeliverySchedule(string deliveryPlanNo, DateTime startTime, DateTime endTime)
        {
            SugarParameter paramDeliveryPlanNo = new SugarParameter("@DeliveryDocNum", deliveryPlanNo);
            SugarParameter paramSupplierCode = new SugarParameter("@SupplierCode", "");
            SugarParameter paramStartDate = new SugarParameter("@StartDate", startTime);
            SugarParameter paramEndDate = new SugarParameter("@EndDate", endTime);
            SugarParameter paramPageNumber = new SugarParameter("@PageNumber", 1);
            SugarParameter paramPageSize = new SugarParameter("@PageSize", 99999);
            SugarParameter paramTotalCount = new SugarParameter("@TotalCount", null, true);
            SugarParameter[] stParams = new SugarParameter[] { paramDeliveryPlanNo, paramSupplierCode, paramStartDate, paramEndDate, paramPageNumber, paramPageSize, paramTotalCount };
            var itemData = this.DbContext.Ado.UseStoredProcedure().SqlQuery<RPT_PurchaseDeliverySchedule>("PROC_RPT_PurchaseDeliverySchedule", stParams)?.ToList();
            return itemData;
        }

        /// <summary>
        /// 采购交货及时率
        /// </summary>
        /// <param name="page"></param>
        /// <param name="supplierCode"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public IEnumerable<object> GetPurchaseOnTimeDeliveryRate(Pagination page, string supplierCode, DateTime startTime, DateTime endTime)
        {
            SugarParameter paramSupplierCode = new SugarParameter("@SupplierCode", supplierCode);
            SugarParameter paramSupplierName = new SugarParameter("@SupplierName", "");
            SugarParameter paramItemCode = new SugarParameter("@ItemCode", "");
            SugarParameter paramItemName = new SugarParameter("@ItemName", "");
            SugarParameter paramStartDate = new SugarParameter("@StartDate", startTime);
            SugarParameter paramEndDate = new SugarParameter("@EndDate", endTime);

            SugarParameter paramPageNumber = new SugarParameter("@PageNumber", page.PageNumber);
            SugarParameter paramPageSize = new SugarParameter("@PageSize", page.PageSize);
            SugarParameter paramTotalCount = new SugarParameter("@TotalCount", null, true);

            SugarParameter[] dbParams = new SugarParameter[] { paramSupplierCode, paramSupplierName, paramItemCode, paramItemName, paramStartDate, paramEndDate, paramPageNumber, paramPageSize, paramTotalCount };
            return base.DbContext.Ado.UseStoredProcedure().SqlQuery<object>("PROC_RPT_PurchaseOnTimeDelivery", dbParams) as IEnumerable<object>;
        }
        /// <summary>
        /// 采购交货及时率导出
        /// </summary>
        /// <param name="supplierCode"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public List<PRT_PurchaseDeliveryOnTimeRate> GetPurchaseOnTimeDeliveryRate(string supplierCode, DateTime startTime, DateTime endTime)
        {
            SugarParameter paramSupplierCode = new SugarParameter("@SupplierCode", supplierCode);
            SugarParameter paramSupplierName = new SugarParameter("@SupplierName", "");
            SugarParameter paramItemCode = new SugarParameter("@ItemCode", "");
            SugarParameter paramItemName = new SugarParameter("@ItemName", "");
            SugarParameter paramStartDate = new SugarParameter("@StartDate", startTime);
            SugarParameter paramEndDate = new SugarParameter("@EndDate", endTime);

            SugarParameter paramPageNumber = new SugarParameter("@PageNumber", 1);
            SugarParameter paramPageSize = new SugarParameter("@PageSize", 99999);
            SugarParameter paramTotalCount = new SugarParameter("@TotalCount", null, true);

            SugarParameter[] dbParams = new SugarParameter[] { paramSupplierCode, paramSupplierName, paramItemCode, paramItemName, paramStartDate, paramEndDate, paramPageNumber, paramPageSize, paramTotalCount };
            var itemData = this.DbContext.Ado.UseStoredProcedure().SqlQuery<PRT_PurchaseDeliveryOnTimeRate>("PROC_RPT_PurchaseOnTimeDelivery", dbParams)?.ToList();
            return itemData;
        }
    }
}
