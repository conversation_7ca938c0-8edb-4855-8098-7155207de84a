using System;
using System.Collections.Generic;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.RPT.ViewModel;
using SqlSugar;

namespace AOS.SRM.Application.RPT
{
    /// <summary>
    /// 
    /// </summary>
    public class V_NotPostStockMoveApp : ContentBase
    {
        #region 查询分页
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        public List<V_NotPostStockMove> GetPageList(Pagination page, string keyword, DateTime[] dateTimes)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var itemsData = this.GetAllData(keyword, dateTimes).OrderBy(page.Sort);
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<V_NotPostStockMove>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        #endregion

        #region 查询所有数据
        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        public ISugarQueryable<V_NotPostStockMove> GetAllData(string keyword, DateTime[] dateTimes)
        {
            var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
            DateTime fromTime = querDateTimes[0];
            DateTime toTime = querDateTimes[1];
            return this.DbContext.Queryable<V_NotPostStockMove>()
                     .Where(t =>
                         string.IsNullOrEmpty(keyword)
                        || t.DocNum.Contains(keyword)
                        || t.BaseNum.Contains(keyword)
                        || t.ItemCode.Contains(keyword)
                        || t.ItemName.Contains(keyword)
                        || t.BarCode.Contains(keyword)
                        || t.BinLocationCode.Contains(keyword)
                        || t.BinLocationName.Contains(keyword)
                        || t.OperationType.Contains(keyword)
                        || t.WhsCode.Contains(keyword)
                        || t.WhsName.Contains(keyword)
                        || t.RegionCode.Contains(keyword)
                        || t.RegionName.Contains(keyword)
                        ).Where(t => (t.CTime >= fromTime && t.CTime <= toTime));
        }
        #endregion

        #region 未过账数据定时自动过账
        /// <summary>
        /// 
        /// </summary>
        public void AutoPostData()
        {
            
        }
        #endregion
    }
}
