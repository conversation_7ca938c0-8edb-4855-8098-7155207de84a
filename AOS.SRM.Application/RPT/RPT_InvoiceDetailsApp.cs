using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity;
using AOS.SRM.Entity.RPT.ViewModel;

namespace AOS.SRM.Application.RPT
{
    /// <summary>
    /// 财务相关报表
    /// </summary>
    public class RPT_InvoiceDetailsApp : BaseApp<BaseEntity>
    {
        /// <summary>
        /// 开票明细报表查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="supplyCode">供应商编码</param>
        /// <param name="billingNo">单据编号</param>
        /// <param name="invoiceNo">发票号</param>
        /// <param name="inspectionNo">报检单号</param>
        /// <param name="orderNo">采购单号</param>
        /// <param name="itemCode">物料编码</param>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        public List<V_RPT_InvoiceDetails> GetInvoiceDetails(Pagination page, string supplyCode, string billingNo, string invoiceNo, string inspectionNo, string orderNo, string itemCode, DateTime startTime, DateTime endTime, string status)
        {
            var query = this.DbContext.Queryable<V_RPT_InvoiceDetails>()
                .Where(t => (t.CTime >= startTime) && (t.CTime < endTime.AddDays(1))
                && (string.IsNullOrEmpty(supplyCode) || t.SupplyCode.Contains(supplyCode))
                && (string.IsNullOrEmpty(billingNo) || t.BillingNo.Contains(billingNo))
                && (string.IsNullOrEmpty(invoiceNo) || t.InvoiceNo.Contains(invoiceNo))
                && (string.IsNullOrEmpty(inspectionNo) || t.InspectionNo.Contains(inspectionNo))
                && (string.IsNullOrEmpty(orderNo) || t.OrderNo.Contains(orderNo) || t.ZTEXT.Contains(orderNo))
                && (string.IsNullOrEmpty(itemCode) || t.ItemCode.Contains(itemCode) || t.ItemName.Contains(t.ItemCode))
                && (string.IsNullOrEmpty(status) || t.Status == status)
                );

            page.Total = query.Count();
            var itemPageData = new List<V_RPT_InvoiceDetails>();
            itemPageData = query.OrderBy(t => t.BillingNo)
                .OrderBy(t => t.InspectionNo)
                .OrderBy(t => t.InspectionLine)
                .ToPageList(page.PageNumber, page.PageSize).ToList();
            return itemPageData;
        }

        /// <summary>
        /// 开票明细报表导出
        /// </summary>
        /// <param name="supplyCode">供应商编码</param>
        /// <param name="billingNo">单据编号</param>
        /// <param name="invoiceNo">发票号</param>
        /// <param name="inspectionNo">报检单号</param>
        /// <param name="orderNo">采购单号</param>
        /// <param name="itemCode">物料编码</param>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        public List<V_RPT_InvoiceDetails> GetAllExportData(string supplyCode, string billingNo, string invoiceNo, string inspectionNo, string orderNo, string itemCode, DateTime startTime, DateTime endTime, string status)
        {
            var itemsData = this.DbContext.Queryable<V_RPT_InvoiceDetails>()
                .Where(t => (t.CTime >= startTime) && (t.CTime < endTime.AddDays(1))
                && (string.IsNullOrEmpty(supplyCode) || t.SupplyCode.Contains(supplyCode))
                && (string.IsNullOrEmpty(billingNo) || t.BillingNo.Contains(billingNo))
                && (string.IsNullOrEmpty(invoiceNo) || t.InvoiceNo.Contains(invoiceNo))
                && (string.IsNullOrEmpty(inspectionNo) || t.InspectionNo.Contains(inspectionNo))
                && (string.IsNullOrEmpty(orderNo) || t.OrderNo.Contains(orderNo) || t.ZTEXT.Contains(orderNo))
                && (string.IsNullOrEmpty(itemCode) || t.ItemCode.Contains(itemCode) || t.ItemName.Contains(t.ItemCode))
                 && (string.IsNullOrEmpty(status) || t.Status == status)
                ).OrderBy(t => t.BillingNo)
                .OrderBy(t => t.InspectionNo)
                .OrderBy(t => t.InspectionLine).ToList();
            return itemsData;
        }

        /// <summary>
        /// 收获记录开票状态
        /// </summary>
        /// <param name="page"></param>
        /// <param name="supplyCode">供应商编码</param>
        /// <param name="inspectionNo">报检单号</param>
        /// <param name="orderNo">采购单号</param>
        /// <param name="itemCode">物料编码</param>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        public List<V_RPT_ReceiptAndInvoiceState> GetReceiptAndInvoiceState(Pagination page, string supplyCode, string inspectionNo, string orderNo, string itemCode, DateTime startTime, DateTime endTime, string status)
        {
            var query = this.DbContext.Queryable<V_RPT_ReceiptAndInvoiceState>()
                .Where(t => (t.CTime >= startTime) && (t.CTime < endTime.AddDays(1))
                && (string.IsNullOrEmpty(supplyCode) || t.SupplierCode.Contains(supplyCode))
                && (string.IsNullOrEmpty(inspectionNo) || t.InspectionNum.Contains(inspectionNo))
                && (string.IsNullOrEmpty(orderNo) || t.BaseNum.Contains(orderNo) || t.ZTEXT.Contains(orderNo))
                && (string.IsNullOrEmpty(itemCode) || t.ItemCode.Contains(itemCode) || t.ItemName.Contains(t.ItemCode))
                && (string.IsNullOrEmpty(status) || t.Status == status)
                );

            page.Total = query.Count();
            var itemPageData = new List<V_RPT_ReceiptAndInvoiceState>();
            itemPageData = query.OrderBy(t => t.InspectionNum)
                .OrderBy(t => t.InspectionLine)
                .OrderByDescending(t => t.CTime)
                .ToPageList(page.PageNumber, page.PageSize).ToList();
            return itemPageData;
        }

        /// <summary>
        /// 收获记录开票状态导出
        /// </summary>
        /// <param name="supplyCode">供应商编码</param>
        /// <param name="inspectionNo">报检单号</param>
        /// <param name="orderNo">采购单号</param>
        /// <param name="itemCode">物料编码</param>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        public List<V_RPT_ReceiptAndInvoiceState> ReceiptAndInvoiceStateExport(string supplyCode, string inspectionNo, string orderNo, string itemCode, DateTime startTime, DateTime endTime, string status)
        {
            var itemsData = this.DbContext.Queryable<V_RPT_ReceiptAndInvoiceState>()
                .Where(t => (t.CTime >= startTime) && (t.CTime < endTime.AddDays(1))
                && (string.IsNullOrEmpty(supplyCode) || t.SupplierCode.Contains(supplyCode))
                && (string.IsNullOrEmpty(inspectionNo) || t.InspectionNum.Contains(inspectionNo))
                && (string.IsNullOrEmpty(orderNo) || t.BaseNum.Contains(orderNo) || t.ZTEXT.Contains(orderNo))
                && (string.IsNullOrEmpty(itemCode) || t.ItemCode.Contains(itemCode) || t.ItemName.Contains(t.ItemCode))
                 && (string.IsNullOrEmpty(status) || t.Status == status)
                ).OrderBy(t => t.InspectionNum)
                .OrderBy(t => t.InspectionLine)
                .OrderByDescending(t => t.CTime).ToList();
            return itemsData;
        }
    }
}
