using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net.Mail;
using AOS.Core;
using AOS.Core.Configuration;
using AOS.Core.Log;
using AOS.Core.Logging;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Sys;
using SqlSugar;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class Sys_MessageApp : BaseApp<Sys_Message>
    {
        private Sys_MessageTypeApp _messageTypeApp = new Sys_MessageTypeApp();
        private Sys_MessageNotifySettingApp _notifyApp = new Sys_MessageNotifySettingApp();
        private Sys_UserMessageApp _userMessageApp = new Sys_UserMessageApp();
        private Sys_MailServerConfigApp _mailConfigApp = new Sys_MailServerConfigApp();
        private Sys_UserApp _userApp = new Sys_UserApp();
        //private Sys_MailApp mailApp = new Sys_MailApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_MessageApp() : base()
        {
        }

        #endregion

        #region 添加通知消息

        /// <summary>
        /// 添加消息：根据配置自动到用户消息表
        /// </summary>
        /// <param name="entity">消息体</param>
        /// <param name="bAutoSendEMail">是否同时自动发送邮件 默认:false 不自动发送</param>
        /// <returns></returns>
        public bool AddNotifyMessage(Sys_Message entity, bool bAutoSendEMail = false)
        {
            try
            {
                Sys_MessageType messageType = _messageTypeApp.GetEntityByKey(entity.MessageTypeID);
                // 获取当前消息分类下需要通知的用户清单
                List<Sys_User> notifyUserList = base.DbContext.Queryable<Sys_MessageNotifySetting>().Where(t => t.IsDelete == false && t.MessageTypeID == entity.MessageTypeID)
                    .InnerJoin<Sys_User>((setting, user) => setting.UserID == user.UserID && !user.IsDelete)
                    .Select((setting, user) => user)
                    .ToList();

                List<Sys_UserMessage> userMessageList = new List<Sys_UserMessage>();
                notifyUserList.ForEach((item) =>
                {
                    userMessageList.Add(new Sys_UserMessage
                    {
                        MessageID = entity.MessageID,
                        MessageTitle = entity.MessageTitle,
                        MessageContent = entity.MessageBody,
                        UserID = item.UserID,
                        UserName = item.UserName,
                        MessageTypeID = messageType.MessageTypeID,
                        MessageTypeDesc = messageType.MessageTypeDesc,
                        NotifyType = messageType.IsNotifyByEmail == true ? "站内信 | 邮件" : "站内信",
                        Publisher = "",
                        PublishTime = DateTime.Now,
                        IsReaded = false,
                        ReadTime = null
                    });
                });
                base.DbContext.Ado.BeginTran();
                base.Insert(entity);
                _userMessageApp.DbContext = this.DbContext;
                _userMessageApp.Insert(userMessageList);
                base.DbContext.Ado.CommitTran();

                // 发送邮件采用异步方式，不放入事务，因为本身无法保证发送成功
                Sys_MailServerConfig mailConfig = _mailConfigApp.GetMailServerConfig();
                if (messageType.IsNotifyByEmail == true && mailConfig != null && bAutoSendEMail)
                {
                    Email myMail = new Email(mailConfig.MailServerHost, (int)mailConfig.MailServerPort, mailConfig.MailServerAccount, mailConfig.MailServerPassword);
                    myMail.SenderDisplayName = mailConfig.SenderDisplayName ?? mailConfig.MailServerAccount;
                    // 获取需要发送邮件的用户列表
                    List<string> toMailList = notifyUserList.Select(t => t.Email).ToList();
                    myMail.ActionSendCompletedCallback += new SendCompletedEventHandler(MailSendComputeCallBack); // 注册回调事件
                    myMail.SendEmailAsync(toMailList, null, entity.MessageTitle, entity.MessageBody, userMessageList); // userToken =>e.UserState userMessageList
                }

                return true;
            }
            catch (Exception ex)
            {
                if (DbContext.Ado.IsAnyTran())
                {
                    base.DbContext.RollbackTran();
                }

                return false;
            }
        }

        #endregion

        #region 邮件发送成功回调事件

        /// <summary>
        /// 不能同时多次执行
        /// </summary>
        /// <param name="sender">用户消息</param>
        /// <param name="e"></param>
        private void MailSendComputeCallBack(object sender, AsyncCompletedEventArgs e)
        {
            try
            {
                //Task.Run((Action)(() => {


                //}));

                // 回调参数
                if (e.UserState != null)
                {
                    List<Sys_UserMessage> messageList = e.UserState as List<Sys_UserMessage>;
                    if (messageList != null && messageList.Count > 0)
                    {
                        string messageTypeId = messageList[0].MessageTypeID;
                        List<Sys_Mail> mailList = new List<Sys_Mail>();
                        Sys_MailServerConfig mailConfig = _mailConfigApp.GetMailServerConfig();
                        List<Sys_User> listUser = base.DbContext.Queryable<Sys_MessageNotifySetting>().Where(t => t.IsDelete == false && t.MessageTypeID == messageTypeId)
                            .InnerJoin<Sys_User>((setting, user) => setting.UserID == user.UserID && user.IsDelete == false)
                            .Select((setting, user) => user)
                            .ToList();

                        messageList.ForEach((item) =>
                        {
                            Sys_User user = _userApp.GetEntityByKey(item.UserID);
                            mailList.Add(new Sys_Mail
                            {
                                MessageID = item.MessageID,
                                ReceiverMail = user.Email,
                                UserID = item.UserID,
                                UserName = item.UserName,
                                MessageTypeID = item.MessageTypeID,
                                MessageTypeDesc = item.MessageTypeDesc,
                                MailSubject = item.MessageTitle,
                                MailBody = item.MessageContent,
                                SenderMail = mailConfig.MailServerAccount,
                                SendTime = DateTime.Now,
                            });
                        });

                        new Sys_MailApp().Insert(mailList);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogDebug(ex.Message);
            }
        }

        #endregion


        #region 自动发送消息邮件

        /// <summary>
        /// 自动发送消息邮件:
        ///    一天之内，需要发送给特定用户通知的消息，一天之内发送失败，则重复发送，
        /// </summary>
        public void AutoSendMessageByEMail()
        {
            try
            {
                //获取需要发送邮件的消息
                List<Sys_UserMessage> _needSendEmailMessageList = CreateUserMessageBySetting();

                if (_needSendEmailMessageList == null || _needSendEmailMessageList.Count <= 0) return;
                Sys_MailServerConfig mailConfig = _mailConfigApp.GetMailServerConfig();


                string[] toMailList = _needSendEmailMessageList.Select(x => x.UserID).Distinct().ToArray();
                List<Sys_User> notifyUserList = base.DbContext.Queryable<Sys_User>().Where(x => toMailList.Contains(x.UserID)).ToList();
                List<Sys_Mail> sendedUserMailList = new List<Sys_Mail>();

                if (mailConfig != null && _needSendEmailMessageList != null && _needSendEmailMessageList.Count() > 0)
                {
                    _needSendEmailMessageList.ForEach((u) =>
                    {
                        Sys_Mail mail = new Sys_Mail();
                        Sys_User user = notifyUserList.Where(x => x.UserID == u.UserID).FirstOrDefault();
                        mail.UserID = u.UserID;
                        mail.MessageID = u.MessageID;
                        mail.MessageTypeID = u.MessageTypeID;
                        mail.MessageTypeDesc = u.MessageTypeDesc;
                        mail.MailSubject = u.MessageTitle;
                        mail.MailBody = u.MessageContent;
                        mail.IsDelete = false;
                        mail.UserName = user?.UserName;
                        mail.ReceiverMail = user?.Email;
                        mail.SenderMail = mailConfig.MailServerAccount;
                        mail.SendTime = DateTime.Now;
                        mail.CUser = "WebJob";
                        mail.MUser = "WebJob";

                        sendedUserMailList.Add(mail);
                    });
                    new Sys_MailApp().Insert(sendedUserMailList);


                    //myMail.ActionSendCompletedCallback += new System.Net.Mail.SendCompletedEventHandler(MailSendComputeCallBack);       // 注册回调事件
                    var messageList = _needSendEmailMessageList.GroupBy(x => x.MessageID);
                    foreach (var item in messageList)
                    {
                        Email myMail = new Email(mailConfig.MailServerHost, (int)mailConfig.MailServerPort, mailConfig.MailServerAccount, mailConfig.MailServerPassword);
                        myMail.SenderDisplayName = mailConfig.SenderDisplayName ?? mailConfig.MailServerAccount;

                        // 获取需要发送邮件的用户列表
                        string[] userList = item.Select(x => x.UserID).ToArray();
                        List<Sys_User> mailUserList = notifyUserList.Where(x => userList.Contains(x.UserID)).ToList();

                        myMail.SendEmailAsync(mailUserList.Select(x => x.Email).ToList(), null, item.FirstOrDefault().MessageTitle, item.FirstOrDefault().MessageContent, null); // userToken =>e.UserState userMessageList
                    }
                }
            }
            catch (Exception ex)
            {
                LogUtil.WriteLog("AutoSendMessageByEMail:Message" + ex.Message);
                LogUtil.WriteLog("AutoSendMessageByEMail:StackTrace" + ex.StackTrace.ToString());
            }
        }

        #endregion

        #region 生成特定用户通知消息

        /// <summary>
        /// 供应商提醒，针对供应商特殊设置
        /// </summary>
        /// <returns></returns>
        public List<Sys_UserMessage> CreateUserMessageBySetting()
        {
            //string[] supplierMessageTypeID = new string[] { "7EFAF0A8-25EB-4C7F-A254-9CEFD5AF832E", "7KFAF0A8-25EB-4C7F-A254-9CEFD5AF832E" };
            string[] supplierMessageTypeID = AppConfigHelper.ReadSetting("SupplierNotifyMessageTypeIDList")?.Split(',');

            List<Sys_UserMessage> innerUserMesssageList = new List<Sys_UserMessage>();
            List<Sys_UserMessage> supplierUserMesssageList = new List<Sys_UserMessage>();
            //1、内部用户消息
            innerUserMesssageList = this.DbContext.Queryable<Sys_Message, Sys_MessageNotifySetting, Sys_MessageType, Sys_User, Sys_UserMessage>(
                    (m, mns, mt, u, um) => new JoinQueryInfos(
                        JoinType.Inner, m.MessageTypeID == mns.MessageTypeID,
                        JoinType.Inner, m.MessageTypeID == mt.MessageTypeID,
                        JoinType.Inner, mns.UserID == u.UserID && u.IsDelete == false && u.IsEnable == true,
                        JoinType.Left, mns.UserID == um.UserID && m.MessageID == um.MessageID
                    ))
                .Where((m, mns, mt, u, um) => !supplierMessageTypeID.Contains(m.MessageTypeID) && um.UserMessageID == null && m.CTime > DateTime.Now.AddDays(-7))
                .Select((m, mns, mt, u, um) => new Sys_UserMessage
                {
                    MessageID = m.MessageID,
                    MessageTypeID = m.MessageTypeID,
                    MessageTypeDesc = mt.MessageTypeDesc,
                    UserID = mns.UserID,
                    UserName = u.UserName,
                    MessageTitle = m.MessageTitle,
                    MessageContent = m.MessageBody,
                    NotifyType = mt.IsNotifyByEmail == true ? "站内信 | 邮件" : "站内信",
                    IsReaded = false,
                    CUser = "WebJob",
                    CTime = DateTime.Now,
                    MUser = null,
                    MTime = null,
                    DUser = null,
                    DTime = null
                })
                .ToList();

// 2、供应商消息
            supplierUserMesssageList = this.DbContext.Queryable<Sys_Message, Sys_MessageNotifySetting, Sys_MessageType, Sys_User, Sys_UserMessage>(
                    (m, mns, mt, u, um) => new JoinQueryInfos(
                        JoinType.Inner, m.MessageTypeID == mns.MessageTypeID,
                        JoinType.Inner, m.MessageTypeID == mt.MessageTypeID,
                        JoinType.Inner, mns.UserID == u.UserID && u.IsDelete == false && u.IsEnable == true && u.IsSupplier == true && m.Remark == u.LoginAccount,
                        JoinType.Left, mns.UserID == um.UserID && m.MessageID == um.MessageID
                    ))
                .Where((m, mns, mt, u, um) => supplierMessageTypeID.Contains(m.MessageTypeID) && um.UserMessageID == null)
                .Select((m, mns, mt, u, um) => new Sys_UserMessage
                {
                    MessageID = m.MessageID,
                    MessageTypeID = m.MessageTypeID,
                    MessageTypeDesc = mt.MessageTypeDesc,
                    UserID = mns.UserID,
                    UserName = u.UserName,
                    MessageTitle = m.MessageTitle,
                    MessageContent = m.MessageBody,
                    NotifyType = mt.IsNotifyByEmail == true? "站内信 | 邮件" : "站内信",
                    CUser = "WebJob",
                    CTime = DateTime.Now,
                    IsReaded = false,
                    MUser = null,
                    MTime = null,
                    DUser = null,
                    DTime = null
                })
                .ToList();


            innerUserMesssageList.AddRange(supplierUserMesssageList);

            innerUserMesssageList.ForEach((item) => { item.UserMessageID = Guid.NewGuid().ToString().ToUpper(); });

            try
            {
                _userMessageApp.Insert(innerUserMesssageList);
            }
            catch (Exception ex)
            {
                LogHelper.Instance.LogDebug(ex.Message);
            }


            return innerUserMesssageList;
        }

        #endregion
    }
}