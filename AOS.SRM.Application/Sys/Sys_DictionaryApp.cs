using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class Sys_DictionaryApp : BaseApp<Sys_Dictionary>
    {
        Sys_DictionaryBaseApp _baseApp = new Sys_DictionaryBaseApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_DictionaryApp() : base()
        {
        }

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<Sys_Dictionary> GetPageList(Pagination page, string keyword)
        {
            return base.GetPageList(page, t =>
                string.IsNullOrEmpty(keyword)
                || t.TypeCode.Contains(keyword)
                || t.TypeDisc.Contains(keyword)
            );
        }

        #endregion

        #region 添加

        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool Insert(Sys_Dictionary entity, out string error_message)
        {
            error_message = "";
            try
            {
                var isExist = GetList(t => t.TypeCode == entity.TypeCode && t.EnumKey == entity.EnumKey).ToList().FirstOrDefault();
                if (isExist != null)
                {
                    error_message = "存在重复键";
                    return false;
                }

                Insert(entity);

                return true;
            }
            catch (Exception ex)
            {
                error_message = ex.Message;
                return false;
            }
        }

        #endregion

        #region 删除

        ///// <summary>
        ///// 删除
        ///// </summary>
        ///// <param name="entity"></param>
        ///// <returns></returns>
        //public int Delete(Sys_Dictionary entity)
        //{
        //    return base.Delete(entity);
        //}

        #endregion

        #region 批量删除

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="deleteUser"></param>
        /// <returns></returns>
        public int DeleteByIDS(object[] ids, string deleteUser)
        {
            return base.DeleteByKeys(ids, deleteUser);
        }

        #endregion

        #region 更新

        ///// <summary>
        /////
        ///// </summary>
        ///// <param name="entity"></param>
        ///// <returns></returns>
        /////// 更新，只能修改备注，是否已读，已读时间，修改时间，修改人Id这几个字段
        //public int Update(Sys_Dictionary entity)
        //{
        //    return base.Update(entity);
        //}

        #endregion


        /// <summary>
        /// 获取字典项
        /// </summary>
        /// <param name="enumKey"></param>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        public Sys_Dictionary GetEntity(int enumKey, string typeCode)
        {
            return base.GetList(x => x.EnumKey == enumKey && x.TypeCode == typeCode).ToList().FirstOrDefault();
        }

        /// <summary>
        /// 获取字典类型
        /// </summary>
        /// <returns></returns>
        public List<Sys_DictionaryBase> GetTypeList()
        {
            var data = _baseApp.GetList().ToList();
            return data;
        }

        /// <summary>
        /// 类型新增
        /// </summary>
        /// <param name="baseEntity"></param>
        public Sys_DictionaryBase InsertTypeData(Sys_DictionaryBase baseEntity)
        {
            return _baseApp.Insert(baseEntity);
        }

        /// <summary>
        /// 获取部门负责人实体
        /// </summary>
        /// <param name="dept"></param>
        /// <returns></returns>
        public Sys_Dictionary GetDeptLeader(string dept)
        {
            var lstEntity = this.GetList(t => t.TypeCode == "DeptLeader").ToList();
            var entity = lstEntity.Find(t => t.EnumValue == dept);
            return entity;
        }
    }
}