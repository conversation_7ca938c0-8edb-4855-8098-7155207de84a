using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_MailApp : BaseApp<Sys_Mail>
    {
        Sys_MailServerConfigApp _serverApp = new Sys_MailServerConfigApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_MailApp() : base()
        {
        }




        #endregion

        #region 获取分页列表

        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="fromTime"></param>
        /// <param name="toTime"></param>
        /// <param name="keyword"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public List<Sys_Mail> GetPageList(Pagination page, DateTime fromTime, DateTime toTime, string keyword = "", string currentUser = "")
        {
            // 无关键字查询所有数据
            return base.GetPageList(page, t =>
                (string.IsNullOrEmpty(keyword) || t.MailSubject.Contains(keyword) || t.MailBody.Contains(keyword) || t.ReceiverMail.Contains(keyword) || t.UserName.Contains(keyword))
                 && (string.IsNullOrEmpty(currentUser) || t.UserID == currentUser)
                 && (t.CTime >= fromTime && t.CTime <= toTime)
            ).ToList();

        }

        #endregion

        #region 邮件发送
        /// <summary>
        /// 邮件发送(西子发送给供应商)
        /// </summary>
        /// <param name="mail">邮件信息</param>
        /// <param name="message">返回消息</param>
        public bool SendEmail(Sys_Mail mail, out string message)
        {
            message = "";

            var server = _serverApp.GetFirstEntityByFieldValue("SenderDisplayName", "admin");
            if (server == null)
            {
                message = "邮箱服务器配置信息不存在";
                return false;
            }
            mail.SenderMail = server.MailServerAccount;

            MailMessage mailMessage = new MailMessage();
            mailMessage.Priority = MailPriority.Normal;
            mailMessage.From = new MailAddress(mail.SenderMail, mail.SenderDisplayName);
            var arrMailTo = mail.ReceiverMail.Split(';');
            foreach (var item in arrMailTo)
            {
                mailMessage.To.Add(item);//收件人
            }
            mailMessage.IsBodyHtml = false;
            mailMessage.Subject = mail.MailSubject;//主题
            mailMessage.Body = mail.MailBody; //正文
            if (!string.IsNullOrEmpty(mail.AttachmentAddr))
            {
                mailMessage.Attachments.Add(new Attachment(mail.AttachmentAddr));//上传附件功能可以使用
                //mailMessage.Attachments.Add(new Attachment(@"E:\供应商档案属性.xlsx"));//上传附件功能可以使用
            }

            SmtpClient client = new SmtpClient(server.MailServerHost, server.MailServerPort);
            client.UseDefaultCredentials = false;
            NetworkCredential credential = new NetworkCredential(server.MailServerAccount, server.MailServerPassword);//邮箱账号和密码
            client.Credentials = credential;
            client.EnableSsl = true;
            client.Send(mailMessage);

            //插入邮件记录
            Insert(mail);
            return true;
        }

        #endregion

        #region  获取收件人
        /// <summary>
        /// 获取收件人信息
        /// </summary>
        /// <param name="typeDesc"></param>
        /// <returns></returns>
        public string GetReceiveEmail(string typeDesc)
        {
            List<V_EmailUserInfo> data = DbContext.Queryable<V_EmailUserInfo>()
                .Where(t => t.MessageTypeDesc == typeDesc).ToList();
            if (data == null || data.Count == 0) return "";

            var lstEmail = data.Select(t => t.Email).Distinct().ToList();
            if (lstEmail == null || lstEmail.Count == 0) return "";

            return string.Join(";", lstEmail.ToArray());

        }
        #endregion
    }
}

