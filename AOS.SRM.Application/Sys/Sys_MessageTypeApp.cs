using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_MessageTypeApp : BaseApp<Sys_MessageType>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_MessageTypeApp() : base()
        {
        }

        
        

        #endregion

    }
}

