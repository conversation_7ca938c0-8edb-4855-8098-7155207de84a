using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Enum;
using AOS.SRM.Entity.Sys;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 
    /// </summary>
    public class MD_AttachmentManagementApp : BaseApp<MD_AttachmentManagement>
    {
        /// <summary>
        /// 附件管理列表查询
        /// </summary>
        /// <param name="page"></param>
        /// <returns></returns>
        public List<MD_AttachmentManagement> GetList(Pagination page)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var itemsData = this.DbContext.Queryable<MD_AttachmentManagement>().Where(x => x.IsDelete == false).ToList();
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<MD_AttachmentManagement>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
        /// <summary>
        /// 附件管理列表删除
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public int Delete(string[] id)
        {
            List<MD_AttachmentManagement> list = GetListByKeys(id);
            return DeleteWithTran(list);
        }

        #region 附件查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="supplierCode"></param>
        /// <param name="referenceDocNumber"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public List<MD_AttachmentManagement> GetList(string supplierCode, string referenceDocNumber, string type = "")
        {
            string typeCode = "";
            //string addr = "";
            if (!string.IsNullOrEmpty(type))
            {
                typeCode = GetTypeCode(Convert.ToInt32(type));
            }
            var itemDatas = base.GetList(x =>
            (string.IsNullOrEmpty(typeCode) || x.AttachTypeCode == typeCode));
            if (string.IsNullOrEmpty(supplierCode))
            {
                itemDatas = itemDatas.Where(x => x.ReferenceDocNumber == referenceDocNumber);
            }
            else
            {
                itemDatas = itemDatas.Where(x => x.SupplyCode == supplierCode && x.ReferenceDocNumber == referenceDocNumber);
            }
            return itemDatas?.ToList();
        }
        #endregion

        #region 新增附件信息
        /// <summary>
        /// 新增附件
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        ///// <param name="supplierCode"></param>
        ///// <param name="supplierName"></param>
        ///// <param name="TypeOrder"></param>
        ///// <param name="type"></param>
        public MD_AttachmentManagement Add(string user)
        {
            MD_AttachmentManagement attachmentmanagement = Upload(user);
            this.Insert(attachmentmanagement);
            return attachmentmanagement;
        }

        /// <summary>
        /// 上传附件
        /// </summary>
        /// <param name="user"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        ///// <param name="supplierCode"></param>
        public MD_AttachmentManagement Upload(string user, HttpRequest request = null)
        {
            string address = "";
            if (request == null)
            {
                request = HttpContext.Current.Request;
            }
            string type = request.Params["type"].ToString();
            MD_AttachmentManagement attachmentmanagement = ChoiceType(int.Parse(request.Params["type"].ToString()), out address);
            //获取参数信息
            string timeSpace = DateTime.Now.ToString("yyyyMMddhhmmss").ToString();
            var files = request.Files[0];
            string fileNameWithOutExtension = Path.GetFileNameWithoutExtension(files.FileName);
            string extension = Path.GetExtension(files.FileName);
            string fileName = fileNameWithOutExtension + timeSpace + extension;

            string newAddress = ConfigurationManager.AppSettings[address]?.ToString() + request.Params["supplierName"].ToString();
            string resPath = newAddress + "\\" + fileName;
            string filePath = HttpContext.Current.Server.MapPath("~/") + resPath;
            string dirPath = HttpContext.Current.Server.MapPath("~/") + newAddress;
            if (!Directory.Exists(dirPath))
            {
                DirectoryInfo directoryInfo = new DirectoryInfo(dirPath);
                directoryInfo.Create();
            }
            //对名称进行重命名
            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
            response.Headers.Add("FileName", HttpUtility.UrlEncode(fileName));
            //存入附件管理表
            attachmentmanagement.FileName = files.FileName;
            attachmentmanagement.FileCodeName = resPath.Replace(Path.GetFileNameWithoutExtension(fileName), timeSpace);
            attachmentmanagement.Address = resPath;
            attachmentmanagement.SupplyCode = request.Params["supplierCode"].ToString();
            attachmentmanagement.SupplyName = request.Params["supplierName"].ToString();
            attachmentmanagement.TypeOrder = request.Params["TypeOrder"].ToString();
            attachmentmanagement.CUser = user;
            attachmentmanagement.CTime = DateTime.Now;
            //request.Files[0].SaveAs(filePath);
            MemoryStream memStream = new MemoryStream();

            CopyStream(files.InputStream, memStream);

            String FullPath = Path.Combine(dirPath, fileName);

            FileStream fs = new FileStream(FullPath, FileMode.OpenOrCreate);
            //files[0].SaveAs(FullPath);
            //string hashStr = HashData(memStream, "md5").ToString();
            memStream.WriteTo(fs);
            memStream.Close();
            fs.Close();

            return attachmentmanagement;
        }

        private static void CopyStream(Stream input, Stream output)
        {
            byte[] buffer = new byte[16 * 1024];
            int read;
            while ((read = input.Read(buffer, 0, buffer.Length)) > 0)
            {
                output.Write(buffer, 0, read);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <param name="address"></param>
        /// <returns></returns>
        public MD_AttachmentManagement ChoiceType(int type, out string address)
        {
            address = "";
            string typecode = "";
            string typename = "";
            string newid = System.Guid.NewGuid().ToString();
            string MenuType = "";
            switch ((BusinessTypeEnum)type)
            {
                case BusinessTypeEnum.Register:
                    address = "S_Upload";
                    typecode = BusinessTypeEnum.Register.ToString();
                    //typename = BusinessTypeEnum.Register.GetDescription();
                    MenuType = "供应商管理-注册";
                    break;
                case BusinessTypeEnum.Access:
                    address = "S_Upload";
                    typecode = BusinessTypeEnum.Access.ToString();
                    //typename = BusinessTypeEnum.Access.GetDescription();
                    MenuType = "供应商管理-准入";
                    break;
                case BusinessTypeEnum.Change:
                    address = "S_Upload";
                    typecode = BusinessTypeEnum.Change.ToString();
                    //typename = BusinessTypeEnum.Change.GetDescription();
                    MenuType = "供应商管理-变更申请";
                    break;
                case BusinessTypeEnum.Announce:
                    address = "Announce";
                    typecode = BusinessTypeEnum.Announce.ToString();
                    //typename = BusinessTypeEnum.Announce.GetDescription();
                    MenuType = "信息往来-公告管理";
                    break;
                case BusinessTypeEnum.Receipt:
                    address = "Receipt";
                    typecode = BusinessTypeEnum.Receipt.ToString();
                    //typename = BusinessTypeEnum.Receipt.GetDescription();
                    MenuType = "信息往来-公告回执";
                    break;
                case BusinessTypeEnum.Contract:
                    address = "Contract";
                    typecode = BusinessTypeEnum.Contract.ToString();
                    //typename = BusinessTypeEnum.Contract.GetDescription();
                    MenuType = "合同管理-合同管理";
                    break;
                case BusinessTypeEnum.ProjectData:
                    address = "ProjectData";
                    typecode = BusinessTypeEnum.ProjectData.ToString();
                    //typename = BusinessTypeEnum.ProjectData.GetDescription();
                    MenuType = "合同管理-设备采购跟踪";
                    break;
                case BusinessTypeEnum.FindSource:
                    address = "FindSource";
                    typecode = BusinessTypeEnum.FindSource.ToString();
                    //typename = BusinessTypeEnum.FindSource.GetDescription();
                    MenuType = "合同管理-设备采购跟踪";
                    break;
                case BusinessTypeEnum.AnswerQuestions:
                    address = "AnswerQuestions";
                    typecode = BusinessTypeEnum.AnswerQuestions.ToString();
                    //typename = BusinessTypeEnum.AnswerQuestions.GetDescription();
                    MenuType = "合同管理-设备采购跟踪";
                    break;
                case BusinessTypeEnum.Bidding:
                    address = "Bidding";
                    typecode = BusinessTypeEnum.Bidding.ToString();
                    //typename = BusinessTypeEnum.Bidding.GetDescription();
                    MenuType = "合同管理-设备采购跟踪";
                    break;
                case BusinessTypeEnum.TechnicalNotice:
                    address = "TechnicalNotice";
                    typecode = BusinessTypeEnum.TechnicalNotice.ToString();
                    //typename = BusinessTypeEnum.TechnicalNotice.GetDescription();
                    MenuType = "技术资料管理-技术通知单";
                    break;
                case BusinessTypeEnum.StandardsOfBank:
                    address = "StandardsOfBank";
                    typecode = BusinessTypeEnum.StandardsOfBank.ToString();
                    //typename = BusinessTypeEnum.StandardsOfBank.GetDescription();
                    MenuType = "技术资料管理-国行企标准";
                    break;
                default:
                    break;
            }
            MD_AttachmentManagement attachmentmanagement = new MD_AttachmentManagement();
            attachmentmanagement.AttachTypeCode = typecode;
            attachmentmanagement.AttachType = typename;
            attachmentmanagement.FileId = newid;
            attachmentmanagement.MenuType = MenuType;
            return attachmentmanagement;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public string GetTypeCode(int type)
        {
            string typecode = "";
            //string typename = "";

            switch ((BusinessTypeEnum)type)
            {
                case BusinessTypeEnum.Register:
                    typecode = BusinessTypeEnum.Register.ToString();
                    //typename = BusinessTypeEnum.Register.GetDescription();
                    break;
                case BusinessTypeEnum.Access:
                    typecode = BusinessTypeEnum.Access.ToString();
                    //typename = BusinessTypeEnum.Access.GetDescription();
                    break;
                case BusinessTypeEnum.Change:
                    typecode = BusinessTypeEnum.Change.ToString();
                    //typename = BusinessTypeEnum.Change.GetDescription();
                    break;
                default:
                    break;
            }
            return typecode;
        }
        #endregion

        #region 更新附件信息
        /// <summary>
        /// 
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool Update(string user)
        {
            HttpRequest request = HttpContext.Current.Request;
            var updateModel = request.Params["entity"].ToString();
            MD_AttachmentManagement entity = JsonConvert.DeserializeObject<MD_AttachmentManagement>(updateModel);
            MD_AttachmentManagement attachmentManagement = Upload(user, request);
            entity.MTime = DateTime.Now;
            entity.MUser = user;
            entity.FileName = attachmentManagement.FileName;
            entity.FileCodeName = attachmentManagement.FileCodeName;
            entity.Address = attachmentManagement.Address;
            return this.Update(entity) > 0;
        }
        #endregion

        #region 附件删除（资源池用）
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="user"></param>
        /// <returns></returns>
        public bool DeleteFiles(string[] ids, string user)
        {
            var entitys = base.GetList(x => ids.Contains(x.Id))?.ToList();
            var isNotResourcesFiles = entitys.Where(x => x.AttachTypeCode != "Resources")?.FirstOrDefault();
            if (isNotResourcesFiles != null)
            {
                throw new Exception("包含其他模块上传的附件，不允许删除！");
            }
            return base.Delete(entitys, user) > 0;
        }
        #endregion

    }
}
