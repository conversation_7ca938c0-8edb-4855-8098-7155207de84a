using AOS.Core.Http;
using AOS.SRM.Entity.Sys;
using System.Collections.Generic;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_DictionaryBaseApp : BaseApp<Sys_DictionaryBase>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_DictionaryBaseApp() : base()
        {
        }




        #endregion

        #region 获取分页列表

        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<Sys_DictionaryBase> GetPageList(Pagination page,string keyword)
        {
            return base.GetPageList(page, t =>
                string.IsNullOrEmpty(keyword) 
                || t.TypeCode.Contains(keyword)
                || t.TypeDisc.Contains(keyword)
                
            );
        }

        #endregion

        #region 删除

        ///// <summary>
        ///// 删除
        ///// </summary>
        ///// <param name="entity"></param>
        ///// <returns></returns>
        //public int Delete(Sys_DictionaryBase entity)
        //{
        //    return base.Delete(entity);
        //}

        #endregion

        #region 批量删除

        /// <summary>
        /// 批量删除
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="deleteUser"></param>
        /// <returns></returns>
        public int DeleteByIDS(object[] ids, string deleteUser)
        {
            return base.DeleteByKeys(ids, deleteUser);
        }

        #endregion

        #region 更新

        ///// <summary>
        /////
        ///// </summary>
        ///// <param name="entity"></param>
        ///// <returns></returns>
        /////// 更新，只能修改备注，是否已读，已读时间，修改时间，修改人Id这几个字段
        //public int Update(Sys_DictionaryBase entity)
        //{
        //    return base.Update(entity);
        //}

        #endregion


    }
}

