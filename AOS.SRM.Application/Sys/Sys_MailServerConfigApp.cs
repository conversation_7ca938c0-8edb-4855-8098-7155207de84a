using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_MailServerConfigApp : BaseApp<Sys_MailServerConfig>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_MailServerConfigApp() : base()
        {
        }




        #endregion

        #region 获取邮件配置(唯一性，不存在多行数据)

        /// <summary>
        /// 获取邮件服务器配置
        /// </summary>
        /// <returns></returns>
        public Sys_MailServerConfig GetMailServerConfig()
        {
            return GetList().ToList().FirstOrDefault();
        }

        #endregion

        #region
        /// <summary>
        /// 根据邮箱账号和发件人显示名查询数据
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<Sys_MailServerConfig> GetMailServer(Pagination page,string keyword)
        {
            if (string.IsNullOrEmpty(page.Sort))
            {
                page.Sort = "CTime desc";
            }
            else
            {
                page.Sort = page.Sort.Replace("ascending", "asc").Replace("descending", "desc");
            }
            var itemsData = this.DbContext.Queryable<Sys_MailServerConfig>().Where(t => string.IsNullOrEmpty(keyword) ||
             t.MailServerAccount.Contains(keyword) || t.SenderDisplayName.Contains(keyword)
            ).Where(t=>t.IsDelete==false)
            .ToList();
            int total = itemsData.Count();
            page.Total = total;
            var itemPageData = new List<Sys_MailServerConfig>();
            if (page.PageSize > total)
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(total).ToList();
            }
            else
            {
                itemPageData = itemsData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();
            }
            return itemPageData;
        }
       #endregion

        #region 删除当前列表
        /// <summary>
        /// 删除当前列表  
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public int DeleteMailServer(string[] ids)
        {
            List<Sys_MailServerConfig> deliveryList = GetListByKeys(ids);
            return DeleteWithTran(deliveryList);
        }

        #endregion

        #region 新增邮件配置数据
        /// <summary>
        /// 新增邮件配置数据 
        /// </summary>
        /// <param name="MailServerConfig"></param>
        /// <param name="error_message"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public Sys_MailServerConfig AddMailServer(Sys_MailServerConfig MailServerConfig, out string error_message,string UserName)
        {
             error_message = "";
            try
            {
                if (MailServerConfig.MailServerAccount == null)
                {
                    error_message = "请确认是否填写邮箱";
                    return null;
                }
                 return Insert(MailServerConfig);
            }
            catch (Exception ex)
            {
                
                throw ex;
            }
            

        }
        //修改当前邮件配置
        /// <summary>
        /// 
        /// </summary>
        /// <param name="MailServerConfig"></param>
        /// <param name="ids"></param>
        /// <param name="UserName"></param>
        /// <returns></returns>
        public int UpdateMailServer(Sys_MailServerConfig MailServerConfig, string[] ids, string UserName)
        {
            try
            {
                return Update(MailServerConfig);
            }
            catch (Exception ex)
            {
                throw ex;
            }


        }
        #endregion



    }
}

