using System;
using System.Collections.Generic;
using System.Linq;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.SAP;
using HZ.WMS.Entity.Sys;
using SAP.Middleware.Connector;

namespace HZ.WMS.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_SAPCompanyInfoApp : BaseApp<Sys_SAPCompanyInfo>
    {

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_SAPCompanyInfoApp() : base()
        {
        }




        #endregion

        #region sap链接
        /// <summary>
        /// sap链接
        /// </summary>
        /// <param name="companycode">公司代码</param>
        /// <returns></returns>
        public RfcConfigParameters GetSAPLink(string companycode)
        {
            //连接 sap
            RfcConfigParameters rfc = new RfcConfigParameters();

            ////测试：
            //rfc.Add(RfcConfigParameters.Name, "FED");//mycon
            //rfc.Add(RfcConfigParameters.AppServerHost, "**********");//IP address
            //rfc.Add(RfcConfigParameters.Client, "200");
            //rfc.Add(RfcConfigParameters.User, "sys-wms");//username
            //rfc.Add(RfcConfigParameters.Password, "123456");//password
            //rfc.Add(RfcConfigParameters.SystemNumber, "10");//00
            Sys_SAPCompanyInfo info = base.GetList(x => x.CompanyCode == companycode).ToList().FirstOrDefault();
            if (info == null)
                return null;

            rfc.Add(RfcConfigParameters.Name, info.SAPName);//mycon
            rfc.Add(RfcConfigParameters.AppServerHost, info.SAPAppServerHost);//IP address
            rfc.Add(RfcConfigParameters.Client, info.SAPClient);
            rfc.Add(RfcConfigParameters.User, info.SAPUser);//username
            rfc.Add(RfcConfigParameters.Password, info.SAPPassword);//password
            rfc.Add(RfcConfigParameters.SystemNumber, info.SAPSystemNumber);
            return rfc;
        }
        #endregion

        #region ZFGSRM002 获取SAP采购价格清单
        /// <summary>
        /// 获取SAP采购价格清单
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="PostTime"></param>
        /// <param name="entity"></param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public ZFGSRM002Return ZFGSRM002(string CompanyCode, DateTime PostTime, HEADZFGSRM002 entity, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = true;
            rtnErrMsg = "";
            ZFGSRM002Return sapReturn = new ZFGSRM002Return();
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode));//初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository;//连接SAP
            IRfcFunction myfun = null;//初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGSRM002"); //RFC函数名
                                                        //组织函数的参数
            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("MATNR", entity.MATNR);
            HEAD.SetValue("LIFNR", entity.LIFNR);
            HEAD.SetValue("EKORG", entity.EKORG);//采购组织   （采购订单主表）
            HEAD.SetValue("ESOKZ", entity.ESOKZ);//采购记录信息分类 （明细表-项目类别）
            HEAD.SetValue("WERKS", entity.WERKS);//工厂
            HEAD.SetValue("ZZSTACODE", entity.ZZSTACODE);  //业务状态码（采购订单主表）
            HEAD.SetValue("ZZZGJ", entity.ZZZGJ);//如果要获取暂估价，赋值为“X”
            HEAD.SetValue("ZSRRQ", entity.ZSRRQ);//采购订单的创建日期
            HEAD.SetValue("EBELN", entity.EBELN);//采购订单号
            HEAD.SetValue("EBELP", entity.EBELP);//采购订单行号

            myfun.Invoke(rfcdest);//调用函数(调用函数后才能获取返回结构或者table)

            IRfcStructure ter = myfun.GetStructure("E_RETURN");
            string ZTYPE = ter.GetValue("ZTYPE").ToString();//消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString();//消息文本

            if (ZTYPE == "S")
            {
                IRfcTable zh = myfun.GetTable("IT_ITEM");
                string KBETR = zh[0].GetValue("ZKBETR").ToString();//获取到的价格
                string ZZZGJ = zh[0].GetValue("ZZZGJ").ToString(); //是否暂估价 （X为暂估价）

                sapReturn.SettleUnitPrice = Convert.ToDecimal(KBETR);//结算单价  
            }
            else
            {
                isPosted = false;
                rtnErrMsg = ZMESSAGE;
                return null;
            }

            return sapReturn;

        }
        #endregion

        #region ZFGSRM003  调用 sap 采购订单创建&采购订单收货 物流对账
        /// <summary>
        ///
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="PostTime"></param>
        /// <param name="entity">数据</param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        /////ZFGSRM003  调用 sap 采购订单创建&采购订单收货 物流对账
        public List<ZFGSRM003Return> ZFGSRM003(string CompanyCode, DateTime PostTime, ZFGSRM003 entity, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;
            List<ZFGSRM003Return> lstSapReturn = new List<ZFGSRM003Return>();
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode));//初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository;//连接SAP
            IRfcFunction myfun = null;//初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGSRM003"); //RFC函数名
                                                        //组织函数的参数
            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");


            HEAD.SetValue("BLDAT", entity.BLDAT);//凭证日期 待确认
            HEAD.SetValue("BUDAT", entity.BUDAT);//过账日期   
            HEAD.SetValue("BSART", "Z009");//订单类型（采购）
            HEAD.SetValue("LIFNR", entity.LIFNR);//供应商或债权人的帐号
            HEAD.SetValue("EKORG", "XF10");//采购组织
            HEAD.SetValue("EKGRP", "A08");//采购组
            HEAD.SetValue("BUKRS", entity.BUKRS);//公司代码

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var item in entity.Items)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("EBELP", item.EBELP);//行号item.Line
                zh.CurrentRow.SetValue("KNTTP", "K");
                zh.CurrentRow.SetValue("PSTYP", "0");
                zh.CurrentRow.SetValue("MATNR", "");//item.MATNR物料不需要填写
                zh.CurrentRow.SetValue("TXZ01", "服务费");//描述，是否手工填，比如运费的描述（20长度）
                zh.CurrentRow.SetValue("MENGE", 1);
                zh.CurrentRow.SetValue("MEINS", "PC");//基本计量单位
                zh.CurrentRow.SetValue("EEIND", DateTime.Now.ToString("yyyyMMdd"));//交货日期
                zh.CurrentRow.SetValue("SAKTO", "7001360000");//总帐科目编号
                zh.CurrentRow.SetValue("KOSTL", item.KOSTL);//成本中心  
                zh.CurrentRow.SetValue("AUFNR", "");
                zh.CurrentRow.SetValue("ANLN1", "");
                zh.CurrentRow.SetValue("MWSKZ", "J2");//购买税代码
                zh.CurrentRow.SetValue("NETPR", item.NETPR);//净价
                zh.CurrentRow.SetValue("PEINH", 1);
                zh.CurrentRow.SetValue("MATKL", "9994");//物料组(后期可能要改，待定)
                zh.CurrentRow.SetValue("LGORT", "");
                zh.CurrentRow.SetValue("WERKS", "2002");//默认2002
                zh.CurrentRow.SetValue("AFNAM", "");
                zh.CurrentRow.SetValue("VBELN", item.VBELN);//销售凭证
                zh.CurrentRow.SetValue("POSNR", item.POSNR);//销售凭证项目
                zh.CurrentRow.SetValue("ZTEXT", "");//备注
            }

            myfun.Invoke(rfcdest);//调用函数
            IRfcTable ter = myfun.GetTable("ET_RETURN");
            foreach (var info in ter)
            {
                string SAPDocNum = info.GetValue("EBELN").ToString();//采购凭证编号
                string SAPLine = info.GetValue("EBELP").ToString();//采购凭证的项目编号  
                string SAPMaterialNum = info.GetValue("MBLNR").ToString();//物料凭证编号
                string SAPMaterialLine = info.GetValue("ZEILE").ToString();//物料凭证中的项目
                string ZTYPE = info.GetValue("ZTYPE").ToString();//消息类型
                string ZMESSAGE = info.GetValue("ZMESSAGE").ToString();//消息文本

                if (ZTYPE == "S")
                {
                    ZFGSRM003Return sapReturn = new ZFGSRM003Return();
                    sapReturn.PostLine = Convert.ToInt32(SAPLine);
                    sapReturn.SAPDocNum = SAPDocNum;
                    sapReturn.SAPLine = Convert.ToInt32(SAPLine);
                    sapReturn.SAPMaterialNum = SAPMaterialNum;
                    sapReturn.SAPMaterialLine = Convert.ToInt32(SAPMaterialLine);

                    lstSapReturn.Add(sapReturn);
                }
                else
                {
                    rtnErrMsg = ZMESSAGE;
                    isPosted = false;
                    return null;
                }
            }
            isPosted = true;
            rtnErrMsg = "";
            return lstSapReturn;

        }
        #endregion

        #region ZFGSRM004  调用sap 财务预制发票接口
        /// <summary>
        ///ZFGSRM004  调用sap 财务预制发票接口
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="PostTime"></param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        public string ZFGSRM004(string CompanyCode, DateTime PostTime, ZFGSRM004 entity, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode));//初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository;//连接SAP
            IRfcFunction myfun = null;//初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGSRM004"); //RFC函数名

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("ZID", entity.ZID);//SRM发票单号
            HEAD.SetValue("BUKRS", entity.BUKRS);//公司代码
            HEAD.SetValue("LIFNR", entity.LIFNR);//供应商代码
            HEAD.SetValue("RMWWR", entity.RMWWR);//发票总额  待确定
            HEAD.SetValue("SGTXT", entity.SGTXT);//文本
            HEAD.SetValue("BLDAT", entity.BLDAT);//发票日期
            HEAD.SetValue("ZFBDT", entity.ZFBDT);//发票基准日
            HEAD.SetValue("BUDAT", entity.BUDAT);//发票过账日期
            HEAD.SetValue("ZDXPZ", entity.ZDXPZ);//贷项凭证 标识    空：预制发票 X：贷方凭证
            HEAD.SetValue("ZTERM", entity.ZTERM);//付款条件

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var item in entity.Items)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("RBLGP", item.RBLGP);//发票凭证中的凭证项目
                zh.CurrentRow.SetValue("EBELN", item.EBELN);//采购凭证号
                zh.CurrentRow.SetValue("EBELP", item.EBELP);//凭证项目号  
                zh.CurrentRow.SetValue("LFBNR", item.LFBNR);//物料凭证号
                zh.CurrentRow.SetValue("LFPOS", item.LFPOS);//物料凭证项目
                zh.CurrentRow.SetValue("MATNR", item.MATNR);//物料号
                zh.CurrentRow.SetValue("MENGE", item.MENGE);//数量
                zh.CurrentRow.SetValue("MEINS", item.MEINS);//基本计量单位
                zh.CurrentRow.SetValue("SMWWR", item.SMWWR);//发票金额
            }

            if (entity.Items1 != null && entity.Items1.Count > 0)
            {
                IRfcTable zh1 = myfun.GetTable("IT_ITEM1");
                foreach (var item in entity.Items1)
                {
                    zh1.Insert();
                    zh1.CurrentRow.SetValue("RBLGP", item.RBLGP);//发票凭证中的凭证项目
                    zh1.CurrentRow.SetValue("HKONT", item.HKONT);//总账科目
                    zh1.CurrentRow.SetValue("MWSKZ", item.MWSKZ);//税码
                    zh1.CurrentRow.SetValue("WRBTR", item.WRBTR);//凭证货币金额
                    zh1.CurrentRow.SetValue("SHKZG", item.SHKZG);//借方/贷方标识
                }
            }

            myfun.Invoke(rfcdest);//调用函数
            IRfcStructure ter = myfun.GetStructure("E_RETURN");
            string BELNR = ter.GetValue("BELNR").ToString();//预制发票号
            string ZTYPE = ter.GetValue("ZTYPE").ToString();//消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString();//消息文本   


            if (ZTYPE == "S")
            {
                isPosted = true;
                rtnErrMsg = "";
                return BELNR;
            }
            else
            {
                rtnErrMsg = ZMESSAGE;
                isPosted = false;
                return "";
            }
        }
        #endregion

        #region ZFGSRM001供应商同步SAP接口
        /// <summary>
        /// ZFGSRM001供应商同步SAP系统接口（一步）
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="entity"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public bool ZFGSRM001(string CompanyCode, ZFGSRM001 entity, out string rtnErrMsg)
        {
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode));//初始化SAP连接配置
                                                                                                   //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository;//连接SAP
            IRfcFunction myfun = null;//初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGSRM001"); //RFC函数名
                                                        //组织函数的参数

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("LIFNR", entity.LIFNR);
            HEAD.SetValue("NAME1", entity.NAME1);
            HEAD.SetValue("NAME2", entity.NAME2);
            HEAD.SetValue("SORTL", entity.SORTL);
            HEAD.SetValue("KTOKK", entity.KTOKK);
            HEAD.SetValue("LAND1", entity.LAND1);
            HEAD.SetValue("ORT01", entity.ORT01);
            HEAD.SetValue("STRAS", entity.STRAS);
            HEAD.SetValue("ORT02", entity.ORT02);
            HEAD.SetValue("SPRAS", entity.SPRAS);
            HEAD.SetValue("PSTLZ", entity.PSTLZ);
            HEAD.SetValue("TELF1", entity.TELF1);
            HEAD.SetValue("TELF2", entity.TELF2);
            HEAD.SetValue("TELFX", entity.TELFX);
            HEAD.SetValue("TELBX", entity.TELBX);
            HEAD.SetValue("STENR", entity.STENR);
            HEAD.SetValue("KOINH", entity.KOINH);
            HEAD.SetValue("BANKA", entity.BANKA);
            HEAD.SetValue("BANKS", entity.BANKS);
            HEAD.SetValue("BANKN", entity.BANKN);
            HEAD.SetValue("NODEL", entity.NODEL);
            HEAD.SetValue("EKORG", entity.EKORG);
            HEAD.SetValue("WAERS", entity.WAERS);
            HEAD.SetValue("ZTERM", entity.ZTERM);
            HEAD.SetValue("BUKRS", entity.BUKRS);
            HEAD.SetValue("AKONT", entity.AKONT);
            HEAD.SetValue("WEBRE", entity.WEBRE);
            HEAD.SetValue("REMARK", entity.REMARK);

            myfun.Invoke(rfcdest);//调用函数

            IRfcStructure ter = myfun.GetStructure("E_RETURN");

            string ZTYPE = ter.GetValue("ZTYPE").ToString();//消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString();//消息文本
            string LIFNR = ter.GetValue("LIFNR").ToString();//供应商或债权人的账号
            if (ZTYPE == "S")
            {
                rtnErrMsg = "";
                return true;
            }
            else
            {
                rtnErrMsg = LIFNR + ZMESSAGE;
                return false;
            }
        }
        #endregion

        #region ZFGWMS019 获取生产订单状态
        /// <summary>
        /// 获取生产订单状态
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="lstEntity"></param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<ZFGWMS019Return> ZFGWMS019(string CompanyCode, List<ZFGWMS019> lstEntity, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;
            List<ZFGWMS019Return> lstSapReturn = new List<ZFGWMS019Return>();
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode));//初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository;//连接SAP
            IRfcFunction myfun = null;//初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGWMS019"); //RFC函数名

            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var item in lstEntity)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("AUFNR", item.AUFNR);//生产订单号
            }
            myfun.Invoke(rfcdest);//调用函数
            IRfcTable ter = myfun.GetTable("IT_ITEM");
            foreach (var item in ter)
            {
                var aufnr = item.GetValue("AUFNR").ToString();//预制发票号
                string status = item.GetValue("STATUS").ToString();//消息类型

                ZFGWMS019Return entity = new ZFGWMS019Return()
                {
                    AUFNR = aufnr,
                    STATUS = status
                };
                lstSapReturn.Add(entity);
            }

            isPosted = true;
            rtnErrMsg = "";
            return lstSapReturn;
        }
        #endregion

        #region ZFGSRM005 采购申请查询
        /// <summary>
        /// 采购申请查询
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="entity"></param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public List<ZFGSRM005Return> ZFGSRM005(string CompanyCode, ZFGSRM005 entity, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;
            List<ZFGSRM005Return> lstSapReturn = new List<ZFGSRM005Return>();
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode));//初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository;//连接SAP
            IRfcFunction myfun = null;//初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGSRM005"); //RFC函数名

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("FLIEF", entity.I_FLIEF);
            HEAD.SetValue("MATNR", entity.I_MATNR);
            HEAD.SetValue("DAT_STA", entity.I_DAT_STA);
            HEAD.SetValue("DAT_END", entity.I_DAT_END);
            myfun.Invoke(rfcdest);//调用函数

            IRfcStructure ter = myfun.GetStructure("E_RETURN");
            string ZTYPE = ter.GetValue("ZTYPE").ToString();//消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString();//消息文本
                                                                  //if (ZTYPE == "S")
                                                                  //{
            rtnErrMsg = "";
            isPosted = true;

            IRfcTable items = myfun.GetTable("IT_ITEM");
            foreach (var item in items)
            {
                var supplyCode = item.GetValue("FLIEF").ToString();//供应商
                var itemCode = item.GetValue("MATNR").ToString();//物料编码
                var itemName = item.GetValue("TXZ01").ToString();//物料名称
                var deliveryDate = Convert.ToDateTime(item.GetValue("LFDAT").ToString());//交货日期
                var qty = Convert.ToDecimal(item.GetValue("MENGE").ToString());//数量
                var unit = item.GetValue("MEINS").ToString();//计量单位
                var zxtsz = item.GetValue("ZXTSZ").ToString(); //刷字物料

                ZFGSRM005Return resData = new ZFGSRM005Return()
                {
                    SupplyCode = supplyCode,
                    ItemCode = itemCode,
                    ItemName = itemName,
                    DeliveryDate = deliveryDate,
                    BrushingWords = zxtsz,
                    Qty = qty,
                    Unit = unit
                };
                lstSapReturn.Add(resData);
            }
            //}
            //else
            //{
            //    rtnErrMsg = ZMESSAGE;
            //    isPosted = false;
            //    return null;
            //}

            return lstSapReturn;
        }
        #endregion

        #region ZFGSRM006  预制发票冲销
        /// <summary>
        /// ZFGSRM006  预制发票冲销
        /// </summary>
        /// <param name="CompanyCode"></param>
        /// <param name="PostTime"></param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <param name="entity"></param>
        /// <returns></returns>
        public ZFGSRM006Return ZFGSRM006(string CompanyCode, DateTime PostTime, ZFGSRM006 entity, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = false;
            var res = new ZFGSRM006Return();

            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(CompanyCode));//初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository;//连接SAP
            IRfcFunction myfun = null;//初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGSRM006"); //RFC函数名

            IRfcStructure HEAD = myfun.GetStructure("I_HEAD");
            HEAD.SetValue("INV_DOC_NO", entity.INV_DOC_NO);//预制发票凭证
            HEAD.SetValue("FISC_YEAR", entity.FISC_YEAR);//会计年度
            HEAD.SetValue("REASON_REV", entity.REASON_REV);//冲销原因
            HEAD.SetValue("PSTNG_DATE", entity.PSTNG_DATE);//过账日期
            myfun.Invoke(rfcdest);//调用函数

            IRfcStructure ter = myfun.GetStructure("E_RETURN");
            string ZTYPE = ter.GetValue("ZTYPE").ToString();//消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString();//消息文本
            string INV_DOC_NO = ter.GetValue("INV_DOC_NO").ToString();//贷方凭证
            string FISC_YEAR = ter.GetValue("FISC_YEAR").ToString();//会计年度

            if (ZTYPE == "S")
            {
                isPosted = true;
                rtnErrMsg = "";

                res.ZTYPE = ZTYPE;
                res.ZMESSAGE = ZMESSAGE;
                res.INV_DOC_NO = INV_DOC_NO;
                res.FISC_YEAR = Convert.ToInt32(FISC_YEAR);
            }
            else
            {
                rtnErrMsg = ZMESSAGE;
                isPosted = false;
            }
            return res;
        }
        #endregion

        #region ZFGSRM007 采购订单状态回写
        /// <summary>
        /// 获取SAP采购价格清单
        /// </summary>
        /// <param name="companyCode"></param>
        /// <param name="lstEntity"></param>
        /// <param name="isPosted"></param>
        /// <param name="rtnErrMsg"></param>
        /// <returns></returns>
        public void ZFGSRM007(string companyCode, List<ZFGSRM007> lstEntity, out bool isPosted, out string rtnErrMsg)
        {
            isPosted = true;
            rtnErrMsg = "";
            RfcDestination rfcdest = RfcDestinationManager.GetDestination(GetSAPLink(companyCode));//初始化SAP连接配置
            //RFC调用函数
            RfcRepository rfcrep = rfcdest.Repository;//连接SAP
            IRfcFunction myfun = null;//初始化RFC函数调用方法
            myfun = rfcrep.CreateFunction("ZFGSRM007"); //RFC函数名
                                                        //组织函数的参数
            IRfcTable zh = myfun.GetTable("IT_ITEM");
            foreach (var item in lstEntity)
            {
                zh.Insert();
                zh.CurrentRow.SetValue("EBELN", item.EBELN);//采购凭证号
                zh.CurrentRow.SetValue("EBELP", item.EBELP);//凭证项目号  
                zh.CurrentRow.SetValue("LOEKZ", item.LOEKZ);//状态
            }
            myfun.Invoke(rfcdest);//调用函数(调用函数后才能获取返回结构或者table)

            IRfcStructure ter = myfun.GetStructure("E_RETURN");
            string ZTYPE = ter.GetValue("ZTYPE").ToString();//消息类型
            string ZMESSAGE = ter.GetValue("ZMESSAGE").ToString();//消息文本

            if (ZTYPE == "S")
            {
                isPosted = true;
                rtnErrMsg = "";
            }
            else
            {
                isPosted = false;
                rtnErrMsg = ZMESSAGE;
            }
        }
        #endregion

    }
}

