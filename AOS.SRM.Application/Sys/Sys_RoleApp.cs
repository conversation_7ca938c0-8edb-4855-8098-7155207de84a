using System.Collections.Generic;
using System.Linq;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Sys;
using SqlSugar;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>
    public class Sys_RoleApp : BaseApp<Sys_Role>
    {
        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_RoleApp() : base()
        {
        }

        #endregion

        #region 获取用户角色列表

        /// <summary>
        /// 获取用户角色列表
        /// </summary>
        /// <param name="userid">用户ID</param>
        /// <returns></returns>
        public List<Sys_Role> GetUserRoles(string userid)
        {
            return base.DbContext.Queryable<Sys_Role>().Where(t => t.IsDelete == false)
                .InnerJoin<Sys_UserRole>((role, userrole) => role.RoleID == userrole.RoleID && userrole.UserID == userid)
                .Select((role, userrole) => role)
                .ToList();
        }

        #endregion

        #region 删除

        /// <summary>
        /// 
        /// </summary>
        /// <param name="deleteIDS"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public void Delete(string[] deleteIDS, string opUser, out string error_message)
        {
            error_message = "";
            var query = this.DbContext.Queryable<Sys_Role, Sys_UserRole, Sys_User>(
                    (role, userrole, user) => new JoinQueryInfos(
                        JoinType.Inner,
                        role.RoleID == userrole.RoleID &&
                        userrole.IsDelete == false &&
                        deleteIDS.Contains(role.RoleID),
                        JoinType.Inner,
                        userrole.UserID == user.UserID &&
                        user.IsDelete == false
                    ))
                .Select(role => role)
                .ToList();

            if (query != null && query.Count() > 0)
            {
                error_message = "Common.CanNotDeleteInUse";
            }
            else
            {
                DeleteByKeys(deleteIDS, opUser);
            }
        }

        #endregion
    }
}