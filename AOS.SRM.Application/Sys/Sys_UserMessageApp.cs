using System;
using System.Collections.Generic;
using System.Linq;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_UserMessageApp : BaseApp<Sys_UserMessage>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_UserMessageApp() : base()
        {
        }

        
        

        #endregion

        #region 获取分页列表

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="fromTime"></param>
        /// <param name="toTime"></param>
        /// <param name="keyword"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public List<Sys_UserMessage> GetPageList(Pagination page, DateTime fromTime, DateTime toTime,string keyword = "",string userId="")
        {
            // 无关键字查询所有数据
            return GetPageList(page, t => (
             (string.IsNullOrEmpty(keyword) || t.MessageTitle.Contains(keyword) || t.MessageContent.Contains(keyword)))
             && (string.IsNullOrEmpty(userId) || t.UserID == userId)
             && (t.CTime >= fromTime && t.CTime <= toTime)
            ).ToList()
            .OrderBy(t => t.IsReaded)
            .ThenByDescending(t => t.PublishTime)
            .ToList(); 
            
        }

        #endregion

        #region 获取用户未读消息列表
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userID"></param>
        /// <param name="unReadUserMessageIds"></param>
        /// <returns></returns>
        public List<Sys_UserMessage> GetUserUnReadMessageList(string userID,string[] unReadUserMessageIds)
        {
            // 无关键字查询所有数据
            return GetList(x => x.UserID == userID && x.IsReaded == false && !unReadUserMessageIds.Contains(x.UserMessageID)).ToList();
        }

        #endregion


        #region 用户读取消息

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userMessageID"></param>
        public void ReadUserMessage(string userMessageID)
        {
            Update(x => x.UserMessageID == userMessageID,x=> new Sys_UserMessage { IsReaded = true,ReadTime = DateTime.Now });
        }

        #endregion


    }
}

