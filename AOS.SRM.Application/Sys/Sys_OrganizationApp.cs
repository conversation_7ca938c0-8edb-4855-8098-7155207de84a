using System.Collections.Generic;
using System.Linq;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Sys;
using SqlSugar;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_OrganizationApp : BaseApp<Sys_Organization>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_OrganizationApp() : base()
        {
        }




        #endregion


        #region 添加

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public Sys_Organization Add(Sys_Organization entity,out string error_message)
        {
            error_message = "";
            if (GetList(x => x.OrganizationCode == entity.OrganizationCode).Any())
            {
                error_message = "Common.ExistedCode";
                return null;
            }
            else
            {
                base.Insert(entity);
            }
            return entity;
        }

        #endregion

        #region 删除

        /// <summary>
        /// 
        /// </summary>
        /// <param name="deleteIDS"></param>
        /// <param name="opUser"></param>
        /// <param name="error_message"></param>
        public void Delete(string[] deleteIDS,string opUser, out string error_message)
        {
            error_message = "";
            var useingOrganization = this.DbContext.Queryable<Sys_Organization, Sys_User>(
                    (orga, user) => new JoinQueryInfos(
                        JoinType.Inner, 
                        orga.OrganizationID == user.OrganizationID && 
                        user.IsDelete == false && 
                        deleteIDS.Contains(orga.OrganizationID)
                    ))
                .Select((orga, user) => orga)
                .ToList();
            
            if(useingOrganization!=null && useingOrganization.Count()>0)
            {
                error_message = "Common.CanNotDeleteInUse";
            }
            else
            {
                DeleteByKeys(deleteIDS, opUser);

                string[] FathIDs = deleteIDS;
                for (int i = 0; i < 10; i++)
                {
                    List<Sys_Organization> orgaList = GetList(x => FathIDs.Contains(x.FathOrganizationID))?.ToList();
                    if (orgaList.Count > 0)
                    {
                        FathIDs = orgaList.Select(x => x.OrganizationID).ToArray();
                        DeleteByKeys(FathIDs, opUser);
                    }
                    else
                        return;
                }
            }
            
        }

        #endregion


        #region 获取组织机构及其子机构

        /// <summary>
        /// 
        /// </summary>
        /// <param name="list"></param>
        /// <param name="organizationID"></param>
        /// <returns></returns>
        public List<Sys_Organization> GetWithChildrenList(List<Sys_Organization> list, string organizationID)
        {
            Sys_Organization currentOrga = GetList(x => x.OrganizationID == organizationID).ToList().FirstOrDefault();
            List<Sys_Organization> orgaList = new List<Sys_Organization>();
            if (currentOrga != null)
            {
                orgaList = GetChildrenList(list, organizationID);
            }
            orgaList.Add(currentOrga);
            return orgaList;

        }

        #endregion


        #region 获取组织机构子机构

        /// <summary>
        /// 
        /// </summary>
        /// <param name="list"></param>
        /// <param name="organizationID"></param>
        /// <returns></returns>
        public List<Sys_Organization> GetChildrenList(List<Sys_Organization> list,string organizationID)
        {
            var query= this.GetList(x =>  x.FathOrganizationID == organizationID).ToList();
            return query.Concat(query.SelectMany(t => GetChildrenList(list, t.OrganizationID))).ToList();

        }

        #endregion

    }
}

