using AOS.SRM.Entity.Sys;
using AOS.SRM.Application.Base;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_SwithConfigApp : BaseApp<Sys_SwithConfig>
    {

        private const string PO_INSPECTION_AUTOPOST = "PO_AP_001";       //采购质检自动过账
        private const string PO_SELFSCAN_AUTOPOST = "PO_AP_002";         //采购上架扫描自动过账
        private const string PO_RETURNSCAN_AUTOPOST = "PO_AP_003";       //采购退货扫描自动过账
        private const string PO_TRANSFERSCAN_AUTOPOST = "PO_AP_004";     //退供区物料移动自动过账
        private const string PO_INSPECTIONSCAN_AUTOPOST = "PO_AP_005";    //退供封存区质检自动过账


        private const string PP_STOCKINGSCAN_AUTOPOST = "PP_AP_001";     //生产备料自动过账开关
        private const string PP_SCRAPPED_AUTOPOST = "PP_AP_002";     //生产报废自动过账开关
        private const string PP_RETURNSCAN_AUTOPOST = "PP_AP_003";     //生产退料自动过账开关
        private const string PP_INSCAN_AUTOPOST = "PP_AP_004";     //生产报交自动过账开关

        private const string MM_OUTSCAN_AUTOPOST = "MM_AP_001";     //仓库其他发货自动过账开关
        private const string MM_INSCAN_AUTOPOST = "MM_AP_002";     //仓库其他收货自动过账开关
        private const string MM_TRANSFERSCAN_AUTOPOST = "MM_AP_003";     //仓库货物移动自动过账开关


        private const string SD_DeliverySCAN_AUTOPOST = "SD_AP_001";     //销售发货自动过账开关
        private const string SD_ReturnSCAN_AUTOPOST = "SD_AP_002";     //销售退货自动过账开关

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_SwithConfigApp() : base()
        {
        }




        #endregion

        #region 获取采购送检自动过账设置

        /// <summary>
        /// 采购送检是否自动过账
        /// </summary>
        public bool IsPoInspectionAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PO_INSPECTION_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 采购上架扫描自动过账

        /// <summary>
        /// 采购上架扫描自动过账
        /// </summary>
        public bool IsPoSelfScanAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 采购退货扫描自动过账

        /// <summary>
        /// 采购退货扫描自动过账
        /// </summary>
        public bool IsPoReturnScanAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == PO_RETURNSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 退供区物料移动自动过账

        /// <summary>
        /// 退供区物料移动自动过账
        /// </summary>
        public bool IsPoInspectionScannAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == PO_TRANSFERSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 退供区质检扫描自动过账
        /// <summary>
        /// 
        /// </summary>
        public bool IsPoTransferScanAutoPost
        {
            get
            {
                //return GetFirstEntity(x => x.ConfigCode == PO_SELFSCAN_AUTOPOST)?.SwitchValue==true;
                var config = GetFirstEntity(x => x.ConfigCode == PO_INSPECTIONSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion


        #region 生产退料自动过账

        /// <summary>
        /// 生产退料自动过账开关
        /// </summary>
        public bool IsPPReturnScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_RETURNSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

       

        #region 生产备料自动过账

        /// <summary>
        /// 生产备料自动过账开关
        /// </summary>
        public bool IsPPStockingScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_STOCKINGSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 生产报交自动过账

        /// <summary>
        /// 生产报交自动过账开关
        /// </summary>
        public bool IsPPInScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_INSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 生产报废自动过账

        /// <summary>
        /// 生产报交自动过账开关
        /// </summary>
        public bool IsPPScrappedAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == PP_SCRAPPED_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库其他发货自动过账

        /// <summary>
        /// 生产发料自动过账开关
        /// </summary>
        public bool IsMMOutScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == MM_OUTSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库其他收货自动过账

        /// <summary>
        /// 生产发料自动过账开关
        /// </summary>
        public bool IsMMInScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == MM_INSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 仓库货物移动自动过账

        /// <summary>
        /// 生产发料自动过账开关
        /// </summary>
        public bool IsMMTransferScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == MM_TRANSFERSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion


        #region 销售发货自动过账开关

        /// <summary>
        /// 销售发货自动过账开关
        /// </summary>
        public bool IsSDDeliveryScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == SD_DeliverySCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

        #region 销售退货自动过账开关

        /// <summary>
        /// 销售退货自动过账开关
        /// </summary>
        public bool IsSDReturnScanAutoPost
        {
            get
            {
                var config = GetFirstEntity(x => x.ConfigCode == SD_ReturnSCAN_AUTOPOST);
                return config == null ? false : config.SwitchValue == true;
            }
        }

        #endregion

    }
}

