using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using AOS.Core.Http;
using AOS.Core.Security;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.Sys
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class Sys_UserApp : BaseApp<Sys_User>
    {
        private const string _default_password = "123";                          // 系统默认密码
        private const string _lang_api_base_path = LanguagePackagePath.API_PATH + ".Sys.Sys_User";             // 本功能模块固定前缀（对应前端多语言包JSON文件路径）
        //private SupplierInfoApp _supplierApp = new SupplierInfoApp();
        private Sys_OrganizationApp _organizationApp = new Sys_OrganizationApp();

        #region 默认构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public Sys_UserApp() : base()
        {
        }




        #endregion

        #region 获取分页列表

        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<Sys_User> GetPageList(Pagination page, string keyword = "")
        {
            // 无关键字查询所有数据.Where(t => string.IsNullOrEmpty(keyword) || t.UserName.Contains(keyword) || t.LoginAccount.Contains(keyword));
            var query = base.GetPageList(page, t => string.IsNullOrEmpty(keyword) || t.UserName.Contains(keyword) || t.LoginAccount.Contains(keyword));
            return query.ToList();
        }
        #endregion

        #region 添加

        /// <summary>
        /// 添加新用户
        /// </summary>
        /// <param name="entity"></param>  
        /// <param name="error_message"></param>
        /// <returns></returns>
        public Sys_User Insert(Sys_User entity, out string error_message)
        {
            error_message = "";
            if (GetUserByAccount(entity.LoginAccount) != null)
            {
                error_message = _lang_api_base_path + ".ExistedUser";
                return null;
            }
            if (!IsEmail(entity.Email))
            {
                error_message = "邮件格式不合格，请重新输入！";
                return null;
            }
            entity.LoginPassword = MD5.MD5Encrypt(_default_password);
            return base.Insert(entity);
        }

        #endregion

        #region 更新用户信息

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public int Update(Sys_User entity, out string error_message)
        {
            error_message = "";
            Sys_User beforeUpdateUser = GetEntityByKey(entity.UserID);
            if (GetUserByAccount(entity.LoginAccount) != null && beforeUpdateUser.LoginAccount != entity.LoginAccount)
            {
                error_message = _lang_api_base_path + ".ExistedUser";
                return -1;
            }
            //if (entity.IsSupplier == true)
            //{
            //    var supplierInfo = _supplierApp.GetFirstEntity(t => t.SupplyerCode == entity.LoginAccount);
            //    if (entity.Email != supplierInfo.EMail)
            //    {
            //        supplierInfo.EMail = entity.Email;
            //        _supplierApp.Update(supplierInfo);
            //    }
            //}
            return base.Update(entity);
        }

        #endregion


        #region 登录校验

        /// <summary>
        /// 
        /// </summary>
        /// <param name="account"></param>
        /// <param name="password"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public Sys_User doLogin(string account, string password, out string error_message)
        {
            error_message = LanguagePackagePath.COMMON_PATH + ".success";
            var user = GetList(x => x.LoginAccount == account && x.LoginPassword == password).ToList().FirstOrDefault();



            if (user != null)
            {
                if (user.IsEnable == false)
                {
                    error_message = _lang_api_base_path + ".AccountEnabled";      // 账号已冻结
                    return null;
                }

                //if (true.Equals(user.NeedUpdatePassword.GetValueOrDefault()) && user.ResetLoginCount.GetValueOrDefault() > 0)
                //{
                //    error_message = _lang_api_base_path + ".NeedUpdatePassword";      // 账号已冻结
                //    return null;
                //}

                if (user.NeedUpdatePassword.Value)
                {

                    user.ResetLoginCount += 1;
                    Update(user);
                }

                return user;
            }

            else
            {
                error_message = _lang_api_base_path + ".AuthenticationFailed";      // 认证失败
                return null;
            };



        }

        #endregion

        #region 根据账号获取用户基本信息

        /// <summary>
        /// 根据账号获取用户基本信息
        /// </summary>
        /// <param name="loginAccount"></param>
        /// <returns></returns>
        public Sys_User GetUserByAccount(string loginAccount)
        {
            var entity = base.GetList(t => t.LoginAccount == loginAccount).ToList().FirstOrDefault();
            if (entity != null && !string.IsNullOrEmpty(entity.OrganizationID))
            {
                var oragnize = _organizationApp.GetEntityByKey(entity.OrganizationID);
                entity.OrganizationDesc = oragnize.OrganizationDesc;
            }
            return entity;
        }
        /// <summary>
        /// 验证EMail是否合法
        /// </summary>
        /// <param name="email">要验证的Email</param>
        public static bool IsEmail(string email)
        {
            //如果为空，认为验证不合格
            if (string.IsNullOrEmpty(email))
            {
                return false;
            }
            //清除要验证字符串中的空格
            email = email.Trim();
            //模式字符串
            string pattern = @"^([0-9a-zA-Z]([-.\w]*[0-9a-zA-Z])*@([0-9a-zA-Z][-\w]*[0-9a-zA-Z]\.)+[a-zA-Z]{2,9})$";
            //验证 
            return Regex.IsMatch(email, pattern);
        }

        #endregion

        #region 用户修改密码

        /// <summary>
        /// 
        /// </summary>
        /// <param name="login_user"></param>
        /// <param name="old_password"></param>
        /// <param name="new_password"></param>
        /// <param name="error_message"></param>
        /// <returns></returns>
        public bool ModifyPassword(Sys_User login_user, string old_password, string new_password, out string error_message)
        {
            //error_message = LanguagePackagePath.COMMON_PATH + ".success";


            //if (login_user.LoginPassword.Equals(MD5.MD5Encrypt(old_password, 32)))
            //{
            //    // 新密码长度不能小于3位
            //    if (new_password.Length < 3)
            //    {
            //        error_message = _lang_api_base_path + ".password_longthlt3";
            //        return false;
            //    }
            //    else
            //    {
            //        login_user.LoginPassword = MD5.MD5Encrypt(new_password, 32);
            //        return base.Update(login_user) > 0 ? true : false;
            //    }
            //}
            //else
            //{
            //    error_message = _lang_api_base_path + ".OldPasswordIsWrong";
            //    return false;
            //};

            error_message = LanguagePackagePath.COMMON_PATH + ".success";

            if (login_user.LoginPassword.Equals(old_password))
            {
                login_user.LoginPassword = new_password;
                login_user.NeedUpdatePassword = false;
                login_user.ResetLoginCount = 0;
                login_user.PasswordUpdateFlag = 1;
                return base.Update(login_user) > 0 ? true : false;
            }
            else
            {
                error_message = _lang_api_base_path + ".OldPasswordIsWrong";
                return false;
            };


        }


        #endregion

        #region 管理员重置用户密码

        /// <summary>
        /// 管理员重置密码：默认(123456)
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="newPassword"></param>
        /// <param name="resetUser"></param>
        /// <returns></returns>
        public int ResetPassword(string[] ids, string newPassword, string resetUser)
        {
            List<Sys_User> listUser = base.GetListByKeys(ids);
            //string defaultPassword = MD5.MD5Encrypt(_default_password);
            string defaultPassword = newPassword;
            return base.Update(t => ids.Contains(t.UserID), t => new Sys_User() { NeedUpdatePassword = true, ResetLoginCount = 0, LoginPassword = defaultPassword, MUser = resetUser, MTime = DateTime.Now });
        }

        #endregion

        #region 获取用户所有角色（用户多角色）

        /// <summary>
        /// 获取用户关联的所有角色
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public List<Sys_Role> GetUserRoles(string userId)
        {
            return base.DbContext.Queryable<Sys_User>()
                .Where(x => x.UserID == userId)
                .InnerJoin<Sys_UserRole>((u, ur) => u.UserID == ur.UserID)
                .InnerJoin<Sys_Role>((u, ur, r) => ur.RoleID == r.RoleID)
                .Select((u, ur, r) => r)
                .ToList();
        }

        #endregion

        #region 创建供应商流水账户
        /// <summary>
        /// 创建供应商流水账户
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public string CreateSupplierLoginAccount(string type)
        {
            //校验有效数据
            //new SupplierInfoApp().ValidData(type);

            var suppliers = this.GetList(x => x.LoginAccount.Substring(0, 2) == type)?.ToList();
            var maxCode = type + "00001";
            if (suppliers.Count() > 0)
            {
                maxCode = suppliers.Max(x => x.LoginAccount);
                maxCode = (int.Parse(maxCode) + 1).ToString().Trim();
            }
            return maxCode.Trim();
        }
        #endregion

        #region 查询所有采购部的账号
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public object GetNotSupplier()
        {
            var roleId = new Sys_RoleApp().GetList(x => x.RoleDesc.Contains("采购工程师")).Select(x => x.RoleID)?.ToList();
            var userId = new Sys_UserRoleApp().GetList(x => roleId.Contains(x.RoleID)).Select(x => x.UserID)?.ToList();

            var items = base.GetList(x =>
            x.IsEnable == true && x.IsSupplier == false && userId.Contains(x.UserID)
            )?.ToList().Select(x => new { x.LoginAccount, x.UserName }).Distinct();
            return items;
        }
        #endregion

        #region 根据角色查找用户信息
        /// <summary>
        /// 根据角色查找用户信息
        /// </summary>
        /// <param name="roleDesc">角色描述</param>
        /// <returns></returns>
        public object GetUserInfoByRoleDesc(string roleDesc)
        {
            var roleId = new Sys_RoleApp().GetList(x => x.RoleDesc.Contains(roleDesc)).Select(x => x.RoleID)?.ToList();
            var userId = new Sys_UserRoleApp().GetList(x => roleId.Contains(x.RoleID)).Select(x => x.UserID)?.ToList();

            var items = base.GetList(x => userId.Contains(x.UserID)).Select(t => new { t.LoginAccount, t.UserName })?.ToList();
            return items;
        }
        #endregion'
    }
}

