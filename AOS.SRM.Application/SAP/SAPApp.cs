using System.Collections.Generic;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.SAP;

namespace AOS.SRM.Application.SAP
{
    /// <summary>
    ///SAP中间库方法层
    /// </summary>
    public class SAPApp : ContentBase
    {
        #region 查询供应商账户信息
        /// <summary>
        /// 查询供应商账户组信息
        /// </summary>
        /// <returns></returns>
        public List<XZ_SAP_T077Y> GetXZSAP_T077Y(string keyword)
        {
            return DbContextForSAP.Queryable<XZ_SAP_T077Y>()
                .Where(t => string.IsNullOrEmpty(keyword) || t.KTOKK.Contains(keyword) || t.TXT30.Contains(keyword))
                .ToList();
        }
        #endregion

        #region 查询供应商国家代码
        /// <summary>
        /// 查询供应商国家代码
        /// </summary>
        /// <returns></returns>
        public List<XZ_SAP_T005T> GetXZSAP_T005T(string keyword)
        {
            return DbContextForSAP.Queryable<XZ_SAP_T005T>()
                .Where(t => string.IsNullOrEmpty(keyword) || t.LAND1.Contains(keyword) || t.LANDX.Contains(keyword)).ToList();
        }
        #endregion

        #region 查询采购付款条件代码
        /// <summary>
        /// 查询采购付款条件代码
        /// </summary>
        /// <returns></returns>
        public List<XZ_SAP_T052U> GetXZSAP_T052U(string keyword)
        {
            return DbContextForSAP.Queryable<XZ_SAP_T052U>()
                .Where(t => string.IsNullOrEmpty(keyword) || t.ZTERM.Contains(keyword) || t.TEXT1.Contains(keyword))
                .ToList();
        }
        #endregion

        #region 税码(税率)
        /// <summary>
        /// 税码
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<XZ_SAP_T007S> GetXZ_SAP_T007S(string keyword)
        {
            return DbContextForSAP.Queryable<XZ_SAP_T007S>()
                .Where(t => string.IsNullOrEmpty(keyword) || t.MWSKZ.Contains(keyword) || t.TEXT1.Contains(keyword))
                .ToList();
        }
        #endregion

        #region 订货货币
        /// <summary>
        /// 订货货币
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public List<XZ_SAP_TCURT> GetXZ_SAP_TCURT(string keyword)
        {
            return DbContextForSAP.Queryable<XZ_SAP_TCURT>()
                .Where(t => string.IsNullOrEmpty(keyword) || t.WAERS.Contains(keyword) || t.LTEXT.Contains(keyword))
                .ToList();
        }
        #endregion    
    }
}
