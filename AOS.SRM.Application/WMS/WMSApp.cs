using System;
using System.Collections.Generic;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.WMS;

namespace AOS.SRM.Application.WMS
{
    /// <summary>
    /// WMS中间库方法层
    /// </summary>
    public class WMSApp : ContentBase
    {
        #region 查询省市区
        /// <summary>
        /// 查询省市区
        /// </summary>
        /// <returns></returns>
        public List<MD_Province_City_District> GetProvince_City_District(string parentId)
        {
            if (!string.IsNullOrEmpty(parentId) && parentId != "0")
            {
                return DbContextForWMS.Queryable<MD_Province_City_District>()
                    .Where(t => t.ParentId == Convert.ToInt32(parentId)).ToList();
            }
            else
            {
                return DbContextForWMS.Queryable<MD_Province_City_District>()
                    .Where(t => t.ParentId == 0).ToList();
            }
        }
        #endregion
    }
}
