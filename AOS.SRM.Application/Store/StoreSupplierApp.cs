using System;
using System.Collections.Generic;
using AOS.Core.Http;
using AOS.SRM.Application.Base;
using AOS.SRM.Entity.SAP;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.Store
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class StoreSupplierApp : BaseApp<Store_Supplier>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public StoreSupplierApp() : base()
        {
        }

        #endregion


        public ResponseData Warehousing(Store_SupplierDetail storeSupplierDetail, Sys_User currentUser)
        {
            var result = new ResponseData();
            try
            {
                DbContext.Ado.BeginTran();
                List<Store_Supplier> list;
                if (storeSupplierDetail.OperateType == 1)
                {
                    // 入库
                    // 中间表校验编码是否存在
                    var part = DbContextForSAP.Queryable<XZ_SAP_MARC>().Where(t => t.MATNR == storeSupplierDetail.PartNo).ToList();
                    if (part.Count == 0)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = "物料不存在,请前往SAP维护后再试";
                        return result;
                    }
                    list = GetList(t =>
                            t.PartNo == storeSupplierDetail.PartNo &&
                            t.SupplierCode == storeSupplierDetail.SupplierCode)
                        .ToList();
                    if (list.Count == 0)
                    {
                        // 添加库存
                        Store_Supplier storeSupplier = new Store_Supplier();
                        storeSupplier.ID = Guid.NewGuid().ToString();
                        storeSupplier.SupplierCode = storeSupplierDetail.SupplierCode;
                        storeSupplier.SupplierName = storeSupplierDetail.SupplierName;
                        storeSupplier.PartNo = part[0].MATNR;
                        storeSupplier.MaterialDesc = part[0].MAKTX;
                        storeSupplier.StoreNo = storeSupplierDetail.OperateNo;
                        DbContext.Insertable(storeSupplier).ExecuteCommand();
                        list.Add(storeSupplier);
                    }
                    else
                    {
                        // 更新库存
                        var storeSupplier = list[0];
                        storeSupplier.StoreNo += storeSupplierDetail.OperateNo;
                        DbContext.Updateable(storeSupplier).ExecuteCommand();
                    }
                }
                else
                {
                    // 出库
                    list = GetList(t =>
                            t.PartNo == storeSupplierDetail.PartNo &&
                            t.SupplierCode == storeSupplierDetail.SupplierCode)
                        .ToList();
                    if (list.Count == 0)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = "物料不存在";
                        return result;
                    }
                    else
                    {
                        // 更新库存
                        var storeSupplier = list[0];
                        if (storeSupplier.StoreNo < storeSupplierDetail.OperateNo)
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            result.Message = "库存不足";
                            return result;
                        }
                        storeSupplier.StoreNo -= storeSupplierDetail.OperateNo;
                        DbContext.Updateable(storeSupplier).ExecuteCommand();
                    }
                }
                // 操作记录
                storeSupplierDetail.ID = Guid.NewGuid().ToString();
                storeSupplierDetail.OperateType = storeSupplierDetail.OperateType;
                storeSupplierDetail.OperateTime = DateTime.Now;
                storeSupplierDetail.Operator = currentUser.LoginAccount;
                storeSupplierDetail.PID = list[0].ID;
                storeSupplierDetail.MaterialDesc = list[0].MaterialDesc;
                DbContext.Insertable(storeSupplierDetail).ExecuteCommand();
                DbContext.Ado.CommitTran();
                result.Code = (int)WMSStatusCode.Success;
                result.Message = "操作成功";
                return result;
            }
            catch (Exception e)
            {
                if (DbContext.Ado.IsAnyTran())
                    DbContext.Ado.RollbackTran();
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = e.Message;
                return result;
            }
        }
    }
}

