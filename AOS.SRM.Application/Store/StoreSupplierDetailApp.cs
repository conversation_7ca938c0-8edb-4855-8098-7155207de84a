using AOS.SRM.Application.Base;
using AOS.SRM.Entity.Sys;

namespace AOS.SRM.Application.Store
{
    /// <summary>
    /// 没有out_message消息的函数，意味着只在最前端做未处理异常捕捉
    /// </summary>

    public class StoreSupplierDetailApp : BaseApp<Store_SupplierDetail>
    {
		#region 默认构造函数
        
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public StoreSupplierDetailApp() : base()
        {
        }

        #endregion


    }
}

