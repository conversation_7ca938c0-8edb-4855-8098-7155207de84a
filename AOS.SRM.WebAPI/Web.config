<?xml version="1.0" encoding="utf-8"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  https://go.microsoft.com/fwlink/?LinkId=301879
  -->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net" />
    <section name="oracle.manageddataaccess.client" type="OracleInternal.Common.ODPMSectionHandler, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
  </configSections>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="log4net.Internal.Debug" value="false" />
    <!--Excel导出设置-->
    <add key="ExportMaxRowsLimit" value="10000" />
    <add key="ExportExcelType" value="xls" />
    <add key="Lic" value="RyKlJelRYxEF+M9yw7/qqmch4X5ize4eRL/jrACu3p6v/QCHF8oX5ti16GXsAmQOcDzNTrDM9TksYFj/z/KtjQ==" />
    <!--获取SAP物料信息 自制外购区分-->
    <add key="SapToken" value="gFBzZCr6tcexwWu1hM2PDSKqGHnCF3zK" />
    <add key="GetMatnrBase" value="http://**********:9001/api/sap/GetMatnrBase" />
    <!--打印设置-->
    <!--<add key="PrintSite" value="https://wmstest3.faradynemotors.cn:9527/FLDAPI/"/>-->
    <!--<add key="PrintSite" value="https://wmstest3.faradynemotors.cn:9528/FLDAPI/" />-->
    <add key="LabelTemplateUploadPath" value="UploadFiles\LabelTemplate\" />
    <add key="PrintPath" value="PrintTempPath\" />
    <!--FLDAPI-->
    <add key="ApiVisualPathName" value="AOS.SRM.WebAPI" />
    <!--上传地址-->
    <add key="Reconciliation" value="UploadFiles\files\FO\" />
    <add key="ReturnOrder" value="UploadFiles\files\FO_Return\" />
    <add key="ReturnOrder2" value="UploadFiles\files\FO_Return2\" />
    <add key="PXC_LogisticsI" value="UploadFiles\files\PXC\" />
    <add key="S_Upload" value="UploadFiles\files\Supplier\" />
    <add key="Announce" value="UploadFiles\files\Announce\" />
    <add key="Receipt" value="UploadFiles\files\Receipt\" />
    <add key="Contract" value="UploadFiles\files\Contract\" />
    <add key="ProjectData" value="UploadFiles\files\ProjectData\" />
    <add key="FindSource" value="UploadFiles\files\FindSource\" />
    <add key="AnswerQuestions" value="UploadFiles\files\AnswerQuestions\" />
    <add key="Bidding" value="UploadFiles\files\Bidding\" />
    <add key="TechnicalNotice" value="UploadFiles\files\TechnicalNotice\" />
    <add key="StandardsOfBank" value="UploadFiles\files\StandardsOfBank\" />
    <add key="BillScan" value="UploadFiles\files\BillScan\" />
    <add key="P_Blueprint_Non_Standard" value="UploadFiles\files\P_Blueprint_Non_Standard\" />
    <!--是否发送邮件-->
    <add key="IsSend" value="1" />
    <!--自动抓取生产订单数据-->
    <add key="AutoRunCrawOrderJob" value="true" />
    <!--抓取多长时间内有更新的数据 单位：分钟-->
    <add key="CrawOrderJobTimeBefore" value="600" />
    <!--API基础URL，用于定时任务调用API-->
    <add key="ApiBaseUrl" value="http://localhost:8080" />
    <!--SAP接口账户设置-->
    <!--<add key="SapInterfaceUser" value="_WMS0526" />
    <add key="SapInterfacePassword" value="Wm$Sim0526" />-->
    <!--正式版地址-->
    <add key="SapInterfaceUser" value="_WMS0622" />
    <add key="SapInterfacePassword" value="Wm$482944" />
    <!--SAP库存查询服务url-->
    <!--https://my600071.sapbyd.cn/sap/byd/odata/scm_inboundlogistics_analytics.svc/RPSCMINVV02_Q0001QueryResults?$select=CLOG_AREA_UUID,CMATERIAL_UUID,T1MATERIAL_UUIDsMATR_INT_ID,KCON_HAND_STOCK,CON_HAND_STOCK_UOM&$filter=(CSITE_UUID eq 'SZ1')&$top=1000000&$format=json-->
    <add key="StockQueryUrl" value="https://my600071.sapbyd.cn/sap/byd/odata/scm_inboundlogistics_analytics.svc/RPSCMINVV02_Q0001QueryResults" />
    <add key="SaleOrderReferenceQueryUrl" value="https://my600071.sapbyd.cn/sap/byd/odata/cust/v1/sitelogisticstasks/SiteLogisticsTaskCollection" />
    <add key="StockQueryUser" value="QWMS0526" />
    <add key="StockQueryPassword" value="Huazhi0526" />
    <!--ALL | CONFIG | NONE-->
    <add key="LogType" value="CONFIG" />
    <add key="SupplierNotifyMessageTypeIDList" value="7EFAF0A8-25EB-4C7F-A254-9CEFD5AF832E,7KFAF0A8-25EB-4C7F-A254-9CEFD5AF832E" />
    <!--<add key="BaseURL" value="http://10.18.0.88:8007/" />-->
    <add key="BaseURL" value="http://10.18.0.20:8007/" />
    <!--跨域访问设置-->
    <add key="cors_allowOrigins" value="*" />
    <add key="cors_allowHeaders" value="*" />
    <add key="cors_allowMethods" value="*" />
    <!--Blueprint下载认证信息-->
    <add key="BlueprintAuthUsername" value="YSAdmin" />
    <add key="BlueprintAuthPassword" value="FWDAdmin2025" />
  </appSettings>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.6" />
      </system.Web>
  -->
  <system.web>
    <compilation targetFramework="4.6" />
    <httpRuntime targetFramework="4.5" maxRequestLength="2097151" />
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-5.2.4.0" newVersion="5.2.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Chloe" publicKeyToken="null" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.9.0.0" newVersion="3.9.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Oracle.ManagedDataAccess" publicKeyToken="89b483f429c47342" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-**********" newVersion="**********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.5.0" newVersion="4.1.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.webServer>
    <modules runAllManagedModulesForAllRequests="true">
      <remove name="WebDAVModule" />
    </modules>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <remove name="OPTIONSVerbHandler" />
      <remove name="TRACEVerbHandler" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
    </handlers>
    <staticContent>
      <remove fileExtension=".*" />
      <mimeMap fileExtension=".*" mimeType="*/*" />
    </staticContent>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="2072576000" />
      </requestFiltering>
    </security>
  </system.webServer>
  <connectionStrings>
    <!--94-->
    <add name="DbConnection" connectionString="04ytv6KDr57/SNNAGEbRNO5xb6X2tpzG8RETwxplaN/McP1fZKnDyRRRxf4D05ZiLpJUV2+8hQMv+5NdzwEU2mx01MvNSSRu" />
    <add name="DbConnectionForWMS" connectionString="04ytv6KDr57/SNNAGEbRNO5xb6X2tpzGQaBjpK22g+IX3VBomDhzQY5AjfWnCJRdnb89p8gVdRnEyicNr7+lmHcM7uFmXU3P" />
    <add name="DbConnectionForSAP" connectionString="04ytv6KDr57/SNNAGEbRNO5xb6X2tpzGH0HwimsZ8tyzNS1R8DmngrInEX9/5zxDMkh9ZCvUIh6AHbHIX8PVVw5WwkftzqhT" />
    <!--测试服 -->
    <!-- <add name="DbConnection" connectionString="04ytv6KDr56gA7rt2zL8I5RAhKz8np4Y8mqSnTIEZphsQ/aHDWH/ys54ghoQw6nDfU3oMDDiUvgwMowikUw15g==" /> -->
    <!-- <add name="DbConnectionForWMS" connectionString="04ytv6KDr56gA7rt2zL8I5RAhKz8np4YRsSaQFxike00h09Yaro4z6KR85jel7+9MvcKzbGDyQeelAzvgLOHAw==" /> -->
    <!-- <add name="DbConnectionForSAP" connectionString="04ytv6KDr56gA7rt2zL8I5RAhKz8np4YiZ8oYm1kBCeuoP7MgydhYI+7Pxd6gzxy8zxZ8TCOj0N2YNkwiaXgEA==" /> -->
    <!-- Oracle数据库连接配置 -->
    <add name="DbConnectionForOracle" connectionString="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=**********)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=wind)));User Id=wind;Password=*******;" />
  </connectionStrings>
  <system.data>
    <DbProviderFactories>
      <remove invariant="Oracle.ManagedDataAccess.Client" />
      <add name="ODP.NET, Managed Driver" invariant="Oracle.ManagedDataAccess.Client" description="Oracle Data Provider for .NET, Managed Driver" type="Oracle.ManagedDataAccess.Client.OracleClientFactory, Oracle.ManagedDataAccess, Version=**********, Culture=neutral, PublicKeyToken=89b483f429c47342" />
    </DbProviderFactories>
  </system.data>
</configuration>