using System;
using System.Web.Http;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.Core.Http;
using AOS.SRM.WebAPI.Controllers;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 用户消息列表
    /// </summary>
    public class Sys_UserMessageController : ApiBaseController
    {
        private Sys_UserMessageApp _app = new Sys_UserMessageApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword,[FromUri]DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                DateTime fromTime = dateValue[0] == null ? DateTime.Now.AddDays(-7).Date : dateValue[0].Date;
                DateTime toTime = dateValue[1] == null ? DateTime.Now.Date : dateValue[1].Date;

                Sys_User currentLoginUser = GetCurrentUser();
                string userId = currentLoginUser.UserRole ==(int) UserRoleType.Administrator ? "" : currentLoginUser.UserID;
                var itemsData = _app.GetPageList(page,fromTime,toTime,keyword, userId);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]Sys_UserMessage entity)
        {
            var result = new ResponseData();
            try
            {
				Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
				entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]Sys_UserMessage entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
				entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取用户未读消息列表
        /// <summary>
        /// 获取用户未读消息列表
        /// </summary>
        /// <param name="unReadList"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetUserUnReadMessageList([FromUri] string[] unReadList)
        {
            var result = new ResponseData();
            try
            {
                
                
                Sys_User currentUser = GetCurrentUser();
                result.Data = _app.GetUserUnReadMessageList(currentUser.UserID,unReadList);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 阅读消息

        /// <summary>
        /// 阅读消息
        /// </summary>
        /// <param name="userMessageID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ReadUserMessage([FromUri] string userMessageID)
        {
            var result = new ResponseData();
            try
            {
                _app.ReadUserMessage(userMessageID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}

