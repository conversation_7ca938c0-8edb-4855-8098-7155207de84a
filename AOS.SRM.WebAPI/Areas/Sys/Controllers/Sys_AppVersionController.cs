using AOS.Core.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using AOS.SRM.Entity.Sys;
using AOS.SRM.Application.Sys;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 版本管理
    /// </summary>
    public class Sys_AppVersionController : ApiController
    {

        private Sys_AppVersionApp _app = new Sys_AppVersionApp();

        /// <summary>
        /// 获取最新版本
        /// </summary>
        /// <param name="appid"></param>
        /// <returns></returns>
        [HttpGet]
        
        public IHttpActionResult GetAppLastVersion([FromUri] string appid)
        {
            var result = new ResponseData();
            try
            {
                Sys_AppVersion appVersion = _app.GetEntityByKey(appid);
                result.Data = new { LastVersion=appVersion.CurrentVersion ,UpdateUrl= appVersion.UpdateUrl,ForceUpdate=appVersion.ForceUpdate };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        
    }
}
