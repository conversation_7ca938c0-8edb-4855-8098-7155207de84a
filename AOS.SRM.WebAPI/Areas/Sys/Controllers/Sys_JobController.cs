using System;
using System.Threading.Tasks;
using System.Web.Http;
using AOS.Core.Http;
using AOS.SRM.WebAPI.Controllers;
using AOS.SRM.WebJob;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 定时任务管理控制器
    /// </summary>
    public class Sys_JobController : ApiBaseController
    {
        /// <summary>
        /// 获取同步图纸释放状态任务状态
        /// </summary>
        /// <returns>任务状态信息</returns>
        [HttpGet]
        public async Task<IHttpActionResult> GetSyncBlueprintJobStatus()
        {
            var result = new ResponseData();
            try
            {
                string status = await JobScheduler.GetJobStatus();
                result.Data = new { Status = status };
                result.Message = "获取任务状态成功";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 手动触发同步图纸释放状态任务
        /// </summary>
        /// <returns>触发结果</returns>
        [HttpPost]
        public async Task<IHttpActionResult> TriggerSyncBlueprintJob()
        {
            var result = new ResponseData();
            try
            {
                await JobScheduler.TriggerSyncBlueprintReleaseStatusJob();
                result.Message = "手动触发同步图纸释放状态任务成功";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 重启定时任务调度器
        /// </summary>
        /// <returns>重启结果</returns>
        [HttpPost]
        public async Task<IHttpActionResult> RestartJobScheduler()
        {
            var result = new ResponseData();
            try
            {
                // 先停止调度器
                await JobScheduler.Stop();
                
                // 等待一秒
                await Task.Delay(1000);
                
                // 重新启动调度器
                await JobScheduler.Start();
                
                result.Message = "定时任务调度器重启成功";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
    }
}
