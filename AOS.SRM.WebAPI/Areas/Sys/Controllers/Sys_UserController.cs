using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using AOS.Core.Security;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.Core.Http;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using AOS.Core.Vue;
using AOS.Core.Office;
using AOS.SRM.Application;
using AOS.Core.Utilities;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 用户管理
    /// </summary>
    public class Sys_UserController : ApiBaseController
    {
        private Sys_UserApp _app = new Sys_UserApp();
        private Sys_ResourceApp resApp = new Sys_ResourceApp(); // 模块资源

        #region 登录及权限相关

        #region 01.根据登录用户名和密码换取Token

        /// <summary>
        /// 登录用户名和密码换取Token以及用户基本信息
        /// </summary>
        /// <param name="username">用户登录账号</param>
        /// <param name="password">用户登录密码(MD5:暂时未作加密处理)</param>
        /// <param name="appId">登录客户端</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymousAttribute]
        public IHttpActionResult doLogin(string username, string password, string appId = "P0001")
        {
            var result = new ResponseData();
            try
            {
                //验证用户名和密码
                string error_message = "";

                if (!LicenseManager.CheckLicense(out error_message))
                {
                    result.Message = error_message;
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                Sys_User user = _app.doLogin(username, password, out error_message);
                if (user != null && !string.IsNullOrEmpty(error_message))
                {
                    List<Sys_Resource> userResouces = resApp.GetUserPermissionButtons(user.UserID, appId);
                    result.Data = new
                    {
                        Token = TokenUtil.GenerateToken(username),
                        UserInfo = user,
                        PermissionButtons = userResouces.Where(t => t.ResourceType == (int)ResourceType.Button)?.Select(t => t.ResourceName).ToArray()
                    };
                    if (user.NeedUpdatePassword == true)
                    {
                        result.Message = "api.Sys.Sys_User.NeedUpdatePassword";
                    }

                    result.Code = (int)WMSStatusCode.Success;


                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #endregion

        #region 02.根据Token获取用户权限列表

        /// <summary>
        /// 获取用户功能清单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetUserRoutes()
        {
            var result = new ResponseData();
            try
            {
                List<Sys_Resource> userResouces = resApp.GetUserPermissionRoutes(GetCurrentUser(), GetRequestClientAppID());
                result.Data = GenerateUserRoutes(userResouces);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            JsonSerializerSettings jss = new JsonSerializerSettings();
            jss.NullValueHandling = NullValueHandling.Ignore;
            return Json(result, jss);  // 忽略NULL值
        }


        #endregion

        #region 03.获取用户模块功能列表
        /// <summary>
        /// 获取用户模块功能
        /// </summary>
        /// <param name="moduleId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetUserModuleRoutes([FromUri] string moduleId)
        {
            var result = new ResponseData();
            try
            {
                List<Sys_Resource> userResouces = resApp.GetUserModuleResources(GetCurrentUser(), moduleId);
                result.Data = userResouces;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            JsonSerializerSettings jss = new JsonSerializerSettings();
            jss.NullValueHandling = NullValueHandling.Ignore;
            return Json(result, jss);  // 忽略NULL值
        }

        #endregion

        #region 04.获取用户模块列表
        /// <summary>
        /// 获取用户模块列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetUserTopModuleRoutes()
        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();
                string clientID = GetRequestClientAppID();
                List<Sys_Resource> userResouces = resApp.GetUserAppModules(currentUser, clientID);
                result.Data = userResouces;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            JsonSerializerSettings jss = new JsonSerializerSettings();
            jss.NullValueHandling = NullValueHandling.Ignore;
            return Json(result, jss);  // 忽略NULL值
        }

        #endregion

        #region 05.获取用户所有按钮权限
        /// <summary>
        /// 获取用户所有权限按钮
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetUserPermissionButtons()
        {
            var result = new ResponseData();
            try
            {
                List<Sys_Resource> userResouces = resApp.GetUserPermissionButtons(GetCurrentUser().UserID, GetRequestClientAppID());
                result.Data = userResouces;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            JsonSerializerSettings jss = new JsonSerializerSettings();
            jss.NullValueHandling = NullValueHandling.Ignore;
            return Json(result, jss);  // 忽略NULL值

        }


        #endregion

        #endregion

        #region 查询用户列表

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询西子用户
        /// <summary>
        /// 查询西子用户（非供应商）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetListWithXZ()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(t => t.IsSupplier == false).ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 查询销售交货波次指定责任人

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSDUserList()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x => !x.IsSupplier.Value).ToList();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, keyword).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword) || t.UserName.Contains(keyword) || t.LoginAccount.Contains(keyword)).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<Sys_User>> columns = ExcelService.FetchDefaultColumnList<Sys_User>();
                string[] ignoreField = new string[] { "LoginPassword", "OrganizationDesc", "IsDelete", "DTime", "DUser" };


                List<ExcelColumn<Sys_User>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<Sys_User> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "OrganizationID")
                    {
                        column.Formattor = OrganizationFormatter;
                    }

                    if (column.ColumnName == "IsEnable")
                    {
                        column.Formattor = ExcelExportFormatter.IsEnableFormatter;
                    }

                    if (column.ColumnName == "Gender")
                    {
                        column.Formattor = ExcelExportFormatter.GenderFormatter;
                    }
                });

                return ExportToExcelFile<Sys_User>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="Item"></param>
        /// <returns></returns>
        public static object OrganizationFormatter(object value, object Item)
        {
            List<Sys_Organization> listOrga = new Sys_OrganizationApp().GetList().ToList();
            return listOrga.Where(t => t.OrganizationID == value?.ToString()).FirstOrDefault()?.OrganizationDesc;
        }



        #endregion

        #region 添加
        /// <summary>
        /// 添加用户
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]Sys_User entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                string error_message = "";
                var dataItem = _app.Insert(entity, out error_message);
                if (string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Data = dataItem;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException.StackTrace.ToString();
                //result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]Sys_User entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                string error_message = "";
                var dataItem = _app.Update(entity, out error_message);
                if (!string.IsNullOrEmpty(error_message))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 用户修改个人密码

        /// <summary>
        /// 用户修改个人密码
        /// </summary>
        /// <param name="oldPassword"></param>
        /// <param name="newPassword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ModifyPassword([FromUri]string oldPassword, [FromUri] string newPassword)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";

                bool bModify = _app.ModifyPassword(GetCurrentUser(), oldPassword, newPassword, out error_message);
                if (bModify)
                {
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 管理员重置用户密码
        /// <summary>
        /// 管理员重置用户密码
        /// </summary>
        /// <param name="Data"></param>
        /// <returns></returns>
        [HttpPut]
        public IHttpActionResult ResetUserPassword([FromBody]ResetData Data)
        {
            var result = new ResponseData();
            try
            {
                if (Data.ids != null && Data.ids.Length > 0)
                {

                    if (_app.ResetPassword(Data.ids, Data.newPassword, GetCurrentUser().LoginAccount) > 0)
                    {
                        result.Code = (int)WMSStatusCode.Success;
                    }
                    else
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                    }
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }

        /// <summary>
        /// 
        /// </summary>
        public class ResetData
        {
            /// <summary>
            /// 
            /// </summary>
            public string[] ids { get; set; }

            /// <summary>
            /// 
            /// </summary>
            public string newPassword { get; set; }
        }
        #endregion

        #region 退出系统

        /// <summary>
        /// 退出系统
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Logout()
        {
            var result = new ResponseData();
            try
            {
                //暂时没有相关缓存机制，不做任何处理
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #endregion

        #region 私有方法


        private List<Route> GenerateUserRoutes(List<Sys_Resource> resources)
        {
            List<Route> vueRoute = new List<Route>();

            List<Route> vueRoute2 = new List<Route>();

            // hack 技巧 排序，保证循序遍历可以逐层添加
            resources.OrderBy(t => t.SortNo).ThenBy(t=>t.ResourceID).ToList().ForEach(delegate (Sys_Resource resource)
            {
                Route route = new Route { routeid = resource.ResourceID, path = resource.ResourcePath, component = resource.Component, name = resource.ResourceName, hidden = resource.Hidden.Value, meta = new RouteMeta { title = resource.ResourceTitle, icon = resource.ResourceIcon } };
                vueRoute2.Add(route);
                // 顶级节点 FathResourceID 0,空格，null均可
                if (string.IsNullOrEmpty(resource.FathResourceID) || "0".Equals(resource.FathResourceID))
                {
                    route.alwaysShow = true;
                    // 顶层节点直接添加
                    vueRoute.Add(route);
                }
                else
                {
                    // 非顶层节点通过子节点添加
                    Route parentRoute = vueRoute2.Where(t => t.routeid == resource.FathResourceID).FirstOrDefault();     // 必须保证父节点提前添加（）
                    if (parentRoute != null)
                    {
                        if (parentRoute.children == null)
                        {
                            parentRoute.children = new List<Route>();
                        }
                        parentRoute.children.Add(route);
                    }

                }
            });
            return vueRoute;
        }

        #endregion

    }
}

