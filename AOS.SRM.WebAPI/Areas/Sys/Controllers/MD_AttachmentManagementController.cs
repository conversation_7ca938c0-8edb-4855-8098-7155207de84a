using AOS.Core.Http;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using HZ.WMS.Application.Sys;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class MD_AttachmentManagementController : ApiBaseController
    {
        MD_AttachmentManagementApp _app = new MD_AttachmentManagementApp();

        #region 附件上传
        /// <summary>
        /// 附件上传
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UploadFile()
        {
            var result = new ResponseData();
            try
            {

                result.Code = (int)WMSStatusCode.Success;
                result.Data = _app.Add(_currentUser.UserName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 查询附件
        /// <summary>
        /// 
        /// </summary>
        /// <param name="supplierCode">供应商编号</param>
        /// <param name="referenceDocNumber">供应商ID</param>
        /// <param name="type">附件类型</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string supplierCode, [FromUri]string referenceDocNumber, [FromUri]string type)
        {
            var result = new ResponseData();
            try
            {

                result.Code = (int)WMSStatusCode.Success;
                result.Data = _app.GetList(supplierCode, referenceDocNumber, type);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 附件更新
        /// <summary>
        /// 附件上传
        /// entity附件实体，supplierCode供应商编号，supplierName供应商名称，TypeOrder菜单单号，type供应商类型
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateUploadFile()
        {
            var result = new ResponseData();
            try
            {

                result.Code = (int)WMSStatusCode.Success;
                result.Data = _app.Update(_currentUser.UserName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 删除附件
        /// <summary>
        /// 附件删除（资源池用）
        /// </summary>
        /// <param name="ids">主键集合</param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult DeleteFiles([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                _app.DeleteFiles(ids, _currentUser.UserName);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 接口测试
        //[HttpGet]
        //public IHttpActionResult SAPInterfaceTest()
        //{
        //    var result = new ResponseData();
        //    try
        //    {

        //        result.Code = (int)WMSStatusCode.Success;
        //        if (new Sys_SAPCompanyInfoApp()._isEnableSAP)
        //        {
        //            new Sys_SAPCompanyInfoApp().SAPInterfaceTest();
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //    }
        //    return Json(result);
        //}
        #endregion

        #region 获取实体类
        /// <summary>
        /// 
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public  IHttpActionResult GetEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var fileId = getValue(jo, "fileId"); //FileId
                result.Data = _app.GetFirstEntity(t => t.FileId == fileId);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }

    /// <summary>
    /// 
    /// </summary>
    public class UploadParamer
    {
        /// <summary>
        /// 
        /// </summary>
        public MD_AttachmentManagement entity { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string supplierCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string supplierName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string TypeOrder { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int type { get; set; }
    }
}