using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using AOS.Core.Security;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.Core.Http;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using AOS.Core.Vue;
using System.Threading;
using AOS.SRM.Application;
using AOS.Core.Office;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class Sys_LogController : ApiBaseController
    {
        private Sys_LogApp _app = new Sys_LogApp();

        #region 查询分页列表

        /// <summary>
        /// 查询日志分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            //try
            //{
            //    var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
            //    DateTime fromTime = querDateTimes[0];
            //    DateTime toTime = querDateTimes[1];

            //    //等待5秒，临时测试用
            //    //Thread.Sleep(5 * 1000);

            //    var itemsData = _app.GetPageList(page, t => (
            //     string.IsNullOrEmpty(keyword)
            //     || t.ApiModule.Contains(keyword)
            //     || t.ApiDescription.Contains(keyword))
            //     && (t.CTime >= fromTime && t.CTime <= toTime), Chloe.LockType.NoLock)
            //    .ToList();
            //    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            //    result.Code = (int)WMSStatusCode.Success;
            //}
            //catch (Exception ex)
            //{
            //    result.Code = (int)WMSStatusCode.UnHandledException;
            //    result.Message = ex.InnerException?.Message ?? ex.Message;
            //}
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]Sys_Log entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]Sys_Log entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                var itemsData = _app.GetList(t => string.IsNullOrEmpty(keyword)
                 || t.ApiModule.Contains(keyword)
                 || t.ApiDescription.Contains(keyword)).Where(x => x.CTime > fromTime && x.CTime < toTime).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<Sys_Log>> columns = ExcelService.FetchDefaultColumnList<Sys_Log>();
                string[] ignoreField = new string[] { "MUser", "MTime", "IsDelete", "DTime", "DUser", "RequestParms", "ResponseData" };

                List<ExcelColumn<Sys_Log>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<Sys_Log> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsPosted")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<Sys_Log>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

    }

}


