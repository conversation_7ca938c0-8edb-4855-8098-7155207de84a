using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

//
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.Core.Http;
using AOS.SRM.WebAPI.Controllers;
using AOS.SRM.Entity.Sys.ViewModel;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 人员消息通知
    /// </summary>
    public class Sys_MessageNotifySettingController : ApiBaseController
    {
        private Sys_MessageNotifySettingApp _app = new Sys_MessageNotifySettingApp();
        private Sys_MessageTypeApp _messageTypeApp = new Sys_MessageTypeApp();


        #region 获取消息分类设置详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="MessageTypeID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMessageNotifySettingDetailInfo([FromUri]string MessageTypeID)
        {
            var result = new ResponseData();
            try
            {
                string[] userIds = _app.GetMessageNotifySetting(MessageTypeID).Select(t => t.UserID).ToArray();
                bool? notifyType = _messageTypeApp.GetEntityByKey(MessageTypeID).IsNotifyByEmail;
                result.Data = new { UserIDS = userIds,IsNotifyByEmail = notifyType };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 保存消息分类设定

        /// <summary>
        /// 保存消息分类设定
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SaveMessageNotifySetting([FromBody]Sys_MessageNotifySettingInput entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();
                List<Sys_MessageNotifySetting> messageSetting = new List<Sys_MessageNotifySetting>();
                Sys_MessageType messageType = _messageTypeApp.GetEntityByKey(entity.MessageTypeID);
                messageType.IsNotifyByEmail = entity.IsNotifyByEmail;
                foreach(string userid in entity.UserIDS)
                {
                    messageSetting.Add(new Sys_MessageNotifySetting { MessageTypeID = entity.MessageTypeID, UserID = userid, CUser = currentUser.LoginAccount, CTime = DateTime.Now });
                }
                _app.SaveMessageNotifySetting(messageSetting, messageType);

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}

