using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

//
using AOS.Core.Security;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.Core.Http;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using AOS.Core.Vue;
using System.Threading.Tasks;
using AOS.SRM.Application;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 数据库b
    /// </summary>
    public class Sys_DbBackupController : ApiBaseController
    {
        private Sys_DbBackupApp _app = new Sys_DbBackupApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateValue)
        {
            var result = new ResponseData();
            //try
            //{
            //    var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
            //    DateTime fromTime = querDateTimes[0];
            //    DateTime toTime = querDateTimes[1];

            //    var itemsData = _app.GetPageList(page, t => (
            //     string.IsNullOrEmpty(keyword)
            //     || t.BackupName.Contains(keyword))
            //     && (t.CTime >= fromTime && t.CTime <= toTime), Chloe.LockType.NoLock)
            //    .ToList();
            //    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            //    result.Code = (int)WMSStatusCode.Success;
            //}
            //catch (Exception ex)
            //{
            //    result.Code = (int)WMSStatusCode.UnHandledException;
            //    result.Message = ex.InnerException?.Message ?? ex.Message;
            //}
            return Json(result);
        }

        #endregion

        #region 备份数据库

        /// <summary>
        /// 备份数据库
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult BackupDb()
        {
            var result = new ResponseData();
            try
            {
                Task taskBackup = _app.BackupDB(GetCurrentUser());
                taskBackup.Wait();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除备份文件（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                _app.DeleteDbBackup(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 还原数据库备份

        /// <summary>
        /// 还原数据库
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult RestoreDB([FromUri] string DbBackupID)
        {
            var result = new ResponseData();
            try
            {
                Task resultDbResotre = _app.RestoreDB(_app.GetEntityByKey(DbBackupID));
                resultDbResotre.Wait();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}

