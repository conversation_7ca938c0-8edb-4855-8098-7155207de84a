using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using AOS.Core.Security;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.Core.Http;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using AOS.Core.Vue;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class Sys_ResourceController : ApiBaseController
    {
        private Sys_ResourceApp _app = new Sys_ResourceApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]Sys_Resource entity)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]Sys_Resource entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取特定客户端权限列表

        /// <summary>
        /// 获取指定客户端资源列表清单（不指定客户端的查看无意义）
        /// </summary>
        /// <param name="appid"></param>
        /// <param name="roleSet"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetResourcesByAppID([FromUri] string appid, [FromUri] string roleSet = "")
        {
            var result = new ResponseData();
            try
            {
                string[] notName = { "100001", "", "100002", "100003", "100006", "100", "200001", "200004", "", "200006", "200", "300001", "300003", "300", "400001", "400003", "400" };
                //string appid = GetRequestClientAppID();
                var resultData = _app.GetList(t => t.AppID == appid)
                        .Where(x => x.Hidden != true && (!string.IsNullOrEmpty(roleSet) || notName.Contains(x.ResourceID))).ToList();

                result.Data = resultData; // && t.Hidden!=true
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }


        #endregion


        #region 获取特定客户端特定角色权限列表

        /// <summary>
        /// 获取特定客户端特定角色权限列表
        /// </summary>
        /// <param name="appid"></param>
        /// <param name="roleid"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetResourceByAppRole([FromUri] string appid, [FromUri] string roleid)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetAppRoleResources(roleid, appid);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}

