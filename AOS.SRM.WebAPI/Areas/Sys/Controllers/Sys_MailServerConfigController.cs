using System;

using System.Web.Http;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.Core.Http;
using AOS.SRM.WebAPI.Controllers;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class Sys_MailServerConfigController : ApiBaseController
    {
        private Sys_MailServerConfigApp _app = new Sys_MailServerConfigApp();


        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMailServerConfig()
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetMailServerConfig();
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]Sys_MailServerConfig entity)
        {
            var result = new ResponseData();
            try
            {
				Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
				entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]Sys_MailServerConfig entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
				entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #region 西子富沃德项目新加

        #region 查询邮箱配置列表
        /// <summary> 
        /// 查询邮箱配置列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        
        public IHttpActionResult GetMailServer([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetMailServer(page,keyword);
                result.Data = new ResponsePageData { total=page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除邮件配置列表数据（支持批量删除）

        /// <summary>
        /// 删除邮件配置列表数据(根据主键批量删除)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult DeleteMailServer([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteMailServer(ids);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 新增邮件配置数据

        /// <summary>
        /// 新增邮件配置数据
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddMailServer([FromBody]Sys_MailServerConfig MailServerConfig)
        {
            var result = new ResponseData();
            try
            {
                string UserName = GetCurrentUser().LoginAccount;
                string error_message = "";
                result.Data = _app.AddMailServer(MailServerConfig, out error_message, UserName);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 修改邮件配置数据

        /// <summary>
        /// 修改邮件配置数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult UpdateMailServer([FromUri]Sys_MailServerConfig MailServerConfig,[FromUri] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string UserName = GetCurrentUser().LoginAccount;
                result.Data = _app.UpdateMailServer(MailServerConfig, ids,UserName);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion


        #endregion




    }
}

