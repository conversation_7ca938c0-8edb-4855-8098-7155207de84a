using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using AOS.Core.Security;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.Core.Http;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using AOS.Core.Vue;
using AOS.SRM.Application;
using AOS.Core.Office;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 角色管理
    /// </summary>
    public class Sys_RoleController : ApiBaseController
    {
        private Sys_RoleApp _app = new Sys_RoleApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "Remark";
                var itemData = _app.GetPageList(page, x => string.IsNullOrEmpty(keyword) || x.RoleDesc.Contains(keyword) || x.Remark.Contains(keyword)).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 查询(不带分页)

        /// <summary>
        /// 查询(不带分页)
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetList(x => string.IsNullOrEmpty(keyword) || x.RoleDesc.Contains(keyword)).ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 添加
        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]Sys_Role entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                entity.MUser = currLoginUser.LoginAccount;
                entity.CUser = currLoginUser.LoginAccount;
                entity.MTime = DateTime.Now;
                entity.CTime = DateTime.Now;

                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取详情
        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetEntity([FromUri]string ID)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetEntityByKey(ID);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]Sys_Role entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                _app.Delete(ids, GetCurrentUser().LoginAccount, out error_message);
                if (!string.IsNullOrEmpty(error_message))
                {
                    result.Message = error_message;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 获取用户角色

        /// <summary>
        /// 获取用户角色
        /// </summary>
        /// <param name="userid"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetUserRoles([FromUri]string userid)
        {
            var result = new ResponseData();
            try
            {
                result.Data = new { RoleList = _app.GetList().OrderBy(t => t.Remark).ToList(), UserRoleList = _app.GetUserRoles(userid) };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion


        #region 导出

        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword) || t.RoleDesc.Contains(keyword)).ToList();
                //var itemsData = _app.GetList().ToList();
                List<ExcelColumn<Sys_Role>> columns = ExcelService.FetchDefaultColumnList<Sys_Role>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser" };

                List<ExcelColumn<Sys_Role>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<Sys_Role> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "OrganizationID")
                //    {
                //        column.Formattor = ExcelExportFormatter.OrganizationFormatter;
                //    }

                //    if (column.ColumnName == "IsEnable")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsEnableFormatter;
                //    }

                //    if (column.ColumnName == "Gender")
                //    {
                //        column.Formattor = ExcelExportFormatter.GenderFormatter;
                //    }
                //});

                return ExportToExcelFile<Sys_Role>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion





    }
}

