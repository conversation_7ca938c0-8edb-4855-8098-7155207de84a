using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application;
using AOS.SRM.Application.PXC.OutsourcingProcurement;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Dto;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.PXC
{
    /// <summary>
    /// 委外报检
    /// </summary>
    public class InspectionController : ApiBaseController
    {
        private P_Out_InspectionApp _app = new P_Out_InspectionApp();

        #region 报检单页面

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="InspectionNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="orderNo"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMakeInspection([FromUri]Pagination page, [FromUri]string InspectionNo, [FromUri]string SupplyCode, [FromUri]string MaterialCode, [FromUri]string orderNo, [FromUri] string Status, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri]string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetMakeInspection(page, InspectionNo, SupplyCode, MaterialCode, orderNo, Status, StartTime, EndTime, CompanyCode);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 查询导出数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string InspectionNo, [FromUri]string SupplyCode, [FromUri]string MaterialCode, [FromUri]string orderNo, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri] string Status, [FromUri]string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetAllExportData(InspectionNo, SupplyCode, MaterialCode, orderNo, StartTime, EndTime, Status, CompanyCode);
                List<ExcelColumn<PXC_MakeInspection_View>> columns = ExcelService.FetchDefaultColumnList<PXC_MakeInspection_View>();
                string[] ignoreField = new string[] {
                    "Id","BSART","IsDelete","Status"
                };

                List<ExcelColumn<PXC_MakeInspection_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PXC_MakeInspection_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<PXC_MakeInspection_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        /// <summary>
        /// 取消送货报检
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateInspection([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;
                var detail = _app.GetListByKeys(ids);
                foreach (var item in detail)
                {
                    if (item.QualifiedQty != null && item.QualifiedQty > 0)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        string msg = "报检单号[" + item.InspectionNo + "]、物料[" + item.ItemCode + "]已报检，不允许删除";
                        result.Message = msg;
                        return Json(result);
                    }
                    if (item.UnQualifiedQty != null && item.UnQualifiedQty > 0)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        string msg = "报检单号[" + item.InspectionNo + "]、物料[" + item.ItemCode + "]已报检，不允许删除";
                        result.Message = msg;
                        return Json(result);
                    }
                    if (item.StorageQty != null && item.StorageQty > 0)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        string msg = "报检单号[" + item.InspectionNo + "]、物料[" + item.ItemCode + "]已入库，不允许删除";
                        result.Message = msg;
                        return Json(result);
                    }
                    bool bExistInWMS = _app.IsExistsInWMS(item.InspectionNo, (int)item.InspectionLine);
                    if (bExistInWMS)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        string msg = "报检单号[" + item.InspectionNo + "]、行号[" + item.InspectionLine.ToString() + "]已存在WMS，不允许删除";
                        result.Message = msg;
                        return Json(result);
                    }
                    item.DUser = userCode;
                    item.IsDelete = true;
                }
                _app.UpdateWithTran(detail);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region  制作报检单页面

        ///// <summary>
        ///// 查询供应商
        ///// </summary>
        ///// <returns></returns>
        //[HttpGet]
        //public IHttpActionResult GetSupplierCode([FromUri]Pagination page, [FromUri]string SupplierName)
        //{
        //    var result = new ResponseData();
        //    try
        //    {
        //        result.Data = _app.GetSupplierCode(page, SupplierName);
        //        result.Code = (int)WMSStatusCode.Success;
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //    }
        //    return Json(result);
        //}


        /// <summary>
        /// 生成单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.PO, DocFixedNumDef.PO_Make_Inspection_Report);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 删除当前列表   （如果已经保检，则无法进行删除）未加
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult DeleteInspectionDetail([FromUri]string[] ids, [FromUri] string deleteUser)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteInspectionDetail(ids, deleteUser);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 保存功能
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult InsertInspectionDetail([FromBody] P_InspectionDetail_Views dto)
        {
            var result = new ResponseData();
            string userCode = GetCurrentUser().LoginAccount;
            try
            {
                dto.CUser = userCode;
                dto.CTime = DateTime.Now;

                foreach (var item in dto.P_InspectionDetail)
                {

                    //单号
                    item.InspectionNo = dto.InspectionNo;
                    item.Id = Guid.NewGuid().ToString();
                    item.CUser = userCode;
                    item.CTime = DateTime.Now;
                    item.SupplyName = dto.SupplyName;
                }


                result.Data = _app.InsertInspection(dto, userCode);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

    }
}
