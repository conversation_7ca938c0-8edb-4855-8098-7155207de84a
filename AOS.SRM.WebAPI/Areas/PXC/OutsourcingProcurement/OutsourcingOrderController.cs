using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web.Http;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.PXC.OutsourcingProcurement;
using AOS.SRM.Application.SHM;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Dto;
using AOS.SRM.Entity.PXC.ViewModel.PurchaseOrder;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;

namespace AOS.SRM.WebAPI.Areas.PXC
{
    /// <summary>
    /// 委外采购订单
    /// </summary>
    public class OutsourcingOrderController : ApiBaseController
    {
        private Sys_MailApp _mailApp = new Sys_MailApp();
        private p_DeliveryPlanApp _deliveryPlanApp = new p_DeliveryPlanApp();
        private p_OutsourcingOrderApp _app = new p_OutsourcingOrderApp();
        private SupplierInfoApp _supplierApp = new SupplierInfoApp();

        private string isSend = ConfigurationManager.AppSettings["IsSend"]?.ToString();
        /// <summary>
        /// 查询委外订单页面
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="EBELN"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]

        public IHttpActionResult GetOutsourcingOrder([FromUri]Pagination page, [FromUri]string SupplyCode, [FromUri]string EBELN, [FromUri]string Status, [FromUri]string MaterialCode, [FromUri]string MaterialName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri]string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetOutsourcingOrder(page, SupplyCode, EBELN, Status, MaterialCode, MaterialName, StartTime, EndTime, CompanyCode,GetCurrentUser()).Where(T => T.BSART == "Z004").ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 接收采购需求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdatePurchaseOrderStatus([FromBody]List<DeliveryBatchDto> dto)
        {

            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();

                string supplyCode = "";
                string supplyName = "";
                _deliveryPlanApp.UpdatePurchaseOrderStatus(dto, loginInfo.LoginAccount, out supplyCode, out supplyName);

                #region 邮件发送
                if(isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    //if (loginInfo.IsSupplier == true)
                    //{
                    //    var message = "";
                    //    var typeDesc = "接收委外采购订单";
                    //    var receiverMail = _mailApp.GetReceiveEmail(typeDesc);
                    //    if (string.IsNullOrEmpty(receiverMail))
                    //    {
                    //        result.Code = (int)WMSStatusCode.Failed;
                    //        string msg = "邮件发送失败：收件人邮箱不能为空";
                    //        result.Message = msg;
                    //        return Json(result);
                    //    }

                    //    Sys_Mail mail = new Sys_Mail();
                    //    mail.MessageTypeDesc = typeDesc;
                    //    mail.SenderDisplayName = loginInfo.SupplyName;
                    //    mail.ReceiverMail = receiverMail;
                    //    mail.MailSubject = typeDesc;
                    //    var mailBody = "您好：" + "\n" + "\t" +
                    //        "委外采购需求已接收，请前往西子富沃德SRM系统确认！\n\t" +
                    //        "采购订单、行号\n\t";
                    //    foreach (var item in dto)
                    //    {
                    //        mailBody += item.EBELN + "、" + item.EBELP + "\n\t";
                    //    }
                    //    mail.MailBody = mailBody;
                    //    mail.CUser = loginInfo.LoginAccount;
                    //    mail.SendTime = DateTime.Now;
                    //    _mailApp.SendEmail(mail, out message);
                    //}
                }
                #endregion

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 制作送货计划
        /// </summary>
        /// <param name="lstEntity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddPurchaseOrder([FromBody] List<P_DeliveryBatch> lstEntity)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();

                if (lstEntity.Count != 0)
                {
                    _deliveryPlanApp.AddDeliveryBatch(lstEntity, loginInfo.LoginAccount);

                    #region 邮件发送
                    if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                    {
                        if (loginInfo.IsSupplier == false)
                        {
                            var message = "";
                            var typeDesc = "制作委外送货批次";
                            var supplierCode = lstEntity.First().SupplyCode;
                            var supplierInfo = _supplierApp.GetFirstEntityByFieldValue("SupplyerCode", supplierCode);
                            if (supplierInfo == null && string.IsNullOrEmpty(supplierInfo.EMail))
                            {
                                result.Code = (int)WMSStatusCode.Failed;
                                string msg = string.Format("邮件发送失败：供应商[{0}]邮箱不能为空", supplierCode);
                                result.Message = msg;
                                return Json(result);
                            }

                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = loginInfo.UserName;
                            mail.ReceiverMail = supplierInfo.EMail;
                            mail.MailSubject = typeDesc;
                            var mailBody = "您好：" + "\n" + "\t" +
                                "委外送货计划已生成，请前往西子富沃德SRM系统确认！\n\t" +
                                "采购订单、采购订单行\n\t";
                            foreach (var item in lstEntity)
                            {
                                mailBody += item.OrderNo + "、" + item.OrderLine + "\n\t";
                            }
                            mail.MailBody = mailBody;
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                    #endregion
                }
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }



        /// <summary>
        /// 委外订单-送货计划弹框接口
        /// </summary>
        /// <param name="lstDto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPurchaseApplyQty([FromBody]List<DeliveryBatchDto> lstDto)
        {

            var result = new ResponseData();
            try
            {
                List<PXC_PurchaseApplyQty_View> lstEntity = new List<PXC_PurchaseApplyQty_View>();
                var itemsData = _app.GetPurchaseApplyQty(lstDto).ToList();
                //foreach (var item in itemsData)
                //{
                //    var entity = itemsData.Where(t => t.EBELN == item.EBELN && t.EBELP == item.EBELP).First();
                //    if (entity != null)
                //    {
                //        entity.CumulativeBatchQuantity = item.CumulativeBatchQuantity;
                //        entity.DeliveredQuantity = item.DeliveredQuantity;
                //        entity.OutstandingQuantity = item.OutstandingQuantity;
                //        lstEntity.Add(entity);
                //    }
                //}
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #region 导出
        /// <summary>
        /// 查询委外订单页面
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="EBELN"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile( [FromUri]string SupplyCode, [FromUri]string EBELN, [FromUri]string Status, [FromUri]string MaterialCode, [FromUri]string MaterialName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri]string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetAllExportData(SupplyCode, EBELN, Status, MaterialCode, MaterialName, StartTime, EndTime, CompanyCode);
                List<ExcelColumn<PXC_PurchaseOrder_View>> columns = ExcelService.FetchDefaultColumnList<PXC_PurchaseOrder_View>();
                string[] ignoreField = new string[] {
                    "BSART","BUKRS","PSTYP","WERKS","Status","Id","IsDelete","SaleNo","SaleLineNo","PurchaseORG","PurchaseGroup","BatchNum"
                };

                List<ExcelColumn<PXC_PurchaseOrder_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PXC_PurchaseOrder_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<PXC_PurchaseOrder_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}
