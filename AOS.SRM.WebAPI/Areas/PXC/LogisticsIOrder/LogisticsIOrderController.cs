using AOS.Core.Http;
using AOS.SRM.Application;
using AOS.SRM.Application.PXC.LogisticsIOrderApp;
using AOS.SRM.Application.PXC.OutsourcingProcurement;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Basic.ViewModel;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.SAP;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using HZ.WMS.Application.Sys;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Http;
using Newtonsoft.Json.Linq;
using AOS.Core.Office;
using System.Net.Http;
using System.Net;

namespace AOS.SRM.WebAPI.Areas.PXC
{
    /// <summary>
    /// 物流采购
    /// </summary>
    public class LogisticsIOrderController : ApiBaseController
    {
        private P_LogisticsIOrderApp _stutasApp = new P_LogisticsIOrderApp();
        private Sys_SAPCompanyInfoApp _sap = new Sys_SAPCompanyInfoApp();
        private P_LogisticsDispatchCarApp _logisticsDispatch = new P_LogisticsDispatchCarApp();
        private MD_AttachmentManagementApp attachmentManagementapp = new MD_AttachmentManagementApp();
        private Sys_MailApp _mailApp = new Sys_MailApp();
        private string isSend = ConfigurationManager.AppSettings["IsSend"]?.ToString();

        #region 查询
        /// <summary>
        /// 物流采购订单列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="DocNum"></param>
        /// <param name="CustomerName"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetLogisticsIOrder([FromUri]Pagination page, [FromUri]string SupplyCode, [FromUri]string DocNum, [FromUri] string Status, [FromUri] string CustomerName, [FromUri] DateTime StartTime, [FromUri] DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _stutasApp.GetLogisticsIOrder(page, SupplyCode, DocNum, Status, CustomerName, StartTime, EndTime).ToList();

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 物流明细
        /// <summary>
        /// 物流单明细
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetLogisticsIOrderDetail(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var DocNum = getValue(jo, "DocNum");
                var PageNumber = getValue(jo, "PageNumber");
                var PageSize = getValue(jo, "PageSize");
                Pagination page = new Pagination();
                page.PageNumber = Convert.ToInt32(PageNumber);
                page.PageSize = Convert.ToInt32(PageSize);

                var itemsData = _stutasApp.GetLogisticsIOrderDetail(page, DocNum).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 接收采购需求
        /// <summary>
        /// 接收采购需求
        /// </summary>
        /// 整单接收
        /// <param name="OrderNo"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult UpdatePurchaseOrderStatus([FromUri]string[] OrderNo)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                string supplyCode = "";
                string supplyName = "";

                result.Data = _stutasApp.ModifyDispatchStatus(OrderNo, loginInfo.LoginAccount, out supplyCode, out supplyName);

                #region 邮件发送
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    //if (loginInfo.IsSupplier == true)
                    //{
                    //    var message = "";
                    //    var typeDesc = "接收物流订单";
                    //    var receiverMail = _mailApp.GetReceiveEmail(typeDesc);
                    //    if (string.IsNullOrEmpty(receiverMail))
                    //    {
                    //        result.Code = (int)WMSStatusCode.Failed;
                    //        string msg = "邮件发送失败：收件人邮箱不能为空";
                    //        result.Message = msg;
                    //        return Json(result);
                    //    }

                    //    Sys_Mail mail = new Sys_Mail();
                    //    mail.MessageTypeDesc = typeDesc;
                    //    mail.SenderDisplayName = loginInfo.SupplyName;
                    //    mail.ReceiverMail = receiverMail;
                    //    mail.MailSubject = typeDesc;
                    //    mail.MailBody = "您好：" + "\n" + "\t" +
                    //        "物流采购需求已接收，请前往西子富沃德SRM系统确认！\n\t" +
                    //        "物流单号:" + string.Join(",", OrderNo);
                    //    mail.CUser = loginInfo.LoginAccount;
                    //    mail.SendTime = DateTime.Now;
                    //    _mailApp.SendEmail(mail, out message);
                    //}
                }
                #endregion

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 派车
        /// <summary>
        /// 派车
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult UpdateDispatchStatus([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;
                var email = GetCurrentUser().Email;

                var lstEntity = _stutasApp.GetListByKeys(ids);
                foreach (var item in lstEntity)
                {
                    var carInfo = _logisticsDispatch.Any(t => t.LogisticsOrderStatusId == item.Id);
                    if (!carInfo)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        result.Message = "请先维护车辆信息";
                        return Json(result);
                    }
                }
                result.Data = _stutasApp.UpdateDispatchStatus(lstEntity, userCode);

                #region 邮件发送
                if (isSend == "1")
                {
                    //if (!string.IsNullOrEmpty(email))
                    //{
                    //    string MessageTypeDesc = "派车";
                    //    string theme = "派车";
                    //    string content = "尊敬的" + "浙江西子富沃德电机公司" + ",您好：" + "\n" + "\t" +
                    //        "我司已派车，请贵公司前往系统确认信息！";

                    //    MailApp.SendEmailToXiZi(theme, email, MessageTypeDesc, content, "");
                    //}
                }
                #endregion

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 取消派车
        /// <summary>
        /// 取消派车
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult CancelDispatch([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;
                var email = GetCurrentUser().Email;

                result.Data = _stutasApp.CancelDispatch(ids, GetCurrentUser().LoginAccount);

                #region 邮件发送
                if (isSend == "1")
                {
                    //if (!string.IsNullOrEmpty(email))
                    //{
                    //    string MessageTypeDesc = "取消派车";
                    //    string theme = "取消派车";
                    //    string content = "尊敬的" + "浙江西子富沃德电机公司" + ",您好：" + "\n" + "\t" +
                    //        "我司已派车，请贵公司前往系统确认信息！";
                    //    MailApp.SendEmailToXiZi(theme, GetCurrentUser().Email, MessageTypeDesc, content, "");
                    //}
                }
                #endregion

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 维护物流派车信息
        /// <summary>
        /// 维护物流派车信息
        /// </summary>
        /// <param name="lstEntity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult CreateDispatchCar([FromBody]List<P_LogisticsDispatchCar> lstEntity)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;

                var logisticsId = lstEntity.First().LogisticsOrderStatusId;
                var lstExist = _logisticsDispatch.GetList(t => t.LogisticsOrderStatusId == logisticsId).ToList();
                if (lstExist != null && lstExist.Count > 0)
                {
                    _logisticsDispatch.HardDeleteWithTran(lstExist);
                }
                lstEntity.ForEach(t =>
                {
                    t.Id = Guid.NewGuid().ToString();
                    t.CUser = userCode;
                });
                _logisticsDispatch.InsertWithTran(lstEntity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 查询派车信息
        /// <summary>
        /// 查询派车信息
        /// </summary>
        /// <param name="LogisticsOrderStatusId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDispatchCar([FromUri]string LogisticsOrderStatusId)
        {
            var result = new ResponseData();
            try
            {
                var data = _logisticsDispatch.GetList(t => t.LogisticsOrderStatusId == LogisticsOrderStatusId).ToList();
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="DocNum"></param>
        /// <param name="Status"></param>
        /// <param name="CustomerName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string SupplyCode, [FromUri]string DocNum, [FromUri] string Status, [FromUri] string CustomerName, [FromUri] DateTime StartTime, [FromUri] DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _stutasApp.GetAllExportData(SupplyCode, DocNum, Status, CustomerName, StartTime, EndTime);
                List<ExcelColumn<PXC_ConsignmentNote_Export>> columns = ExcelService.FetchDefaultColumnList<PXC_ConsignmentNote_Export>();
                string[] ignoreField = new string[] {
                    "LogisticsNo","LogisticsSupplierCode","Status"
                };

                List<ExcelColumn<PXC_ConsignmentNote_Export>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PXC_ConsignmentNote_Export> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "States")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<PXC_ConsignmentNote_Export>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 上传附件
        /// <summary>
        /// 上传附件
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Upload([FromUri] string id)
        {

            var userCode = GetCurrentUser().LoginAccount;
            string attachType = "PXC_LogisticsI";
            string attachTypeName = "物流采购附件";
            string MenuType = "采购协同-物流采购";
            string newid = System.Guid.NewGuid().ToString();
            var result = new ResponseData();

            try
            {
                var data = _stutasApp.GetEntityByKey(id);
                if (data == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "没有找到该条数据";
                    return Json(result);
                }

                //获取参数信息
                HttpContextBase context = (HttpContextBase)Request.Properties["MS_HttpContext"];
                HttpRequestBase request = context.Request;
                string fileName = request.Params["TempleteFile"]?.ToString();
                string newFileName = DateTime.Now.ToString("yyyyMMddhhmmss").ToString() + Path.GetExtension(fileName);//重新命名（序列）
                string resPath = ConfigurationManager.AppSettings[attachType]?.ToString() + newFileName;
                string filePath = HttpContext.Current.Server.MapPath("~/") + resPath;
                //对名称进行重命名
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Headers.Add("FileName", HttpUtility.UrlEncode(fileName));
                request.Files[0].SaveAs(filePath);

                #region 存入附件
                //存入附件管理表
                MD_AttachmentManagement attachmentmanagement = new MD_AttachmentManagement();
                attachmentmanagement.Id = newid;
                attachmentmanagement.AttachTypeCode = attachType;
                attachmentmanagement.AttachType = attachTypeName;
                //attachmentmanagement.FileId = newid;
                attachmentmanagement.FileName = fileName;
                //attachmentmanagement.FileCodeName = resPath.Replace(Path.GetFileNameWithoutExtension(fileName), DateTime.Now.ToString("yyyyMMddhhmmss").ToString());
                attachmentmanagement.Address = resPath;
                attachmentmanagement.MenuType = MenuType;
                attachmentmanagement.SupplyCode = data.SupplyCode;
                attachmentmanagement.SupplyName = data.SupplyName;
                //attachmentmanagement.TypeOrder = TypeOrder;
                attachmentManagementapp.Insert(attachmentmanagement);
                #endregion

                result.Data = new { FilePath = "" + resPath.Replace("\\", "/"), id, fileName };

                //更新单据
                string path = resPath.Replace("\\", "/");
                data.AttachId = newid;
                data.FileName = fileName;
                data.Path = path;
                data.Status = "4";//已完成
                data.MUser = userCode;
                data.MTime = DateTime.Now;
                result.Data = _stutasApp.Update(data);

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}
