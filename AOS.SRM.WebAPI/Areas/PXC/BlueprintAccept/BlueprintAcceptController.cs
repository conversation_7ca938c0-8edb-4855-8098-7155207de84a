using System;
using System.IO;
using System.Linq;
using System.Web.Http;
using AOS.Core.Http;
using AOS.SRM.Application.Plm;
using AOS.SRM.Application.Sys;
using AOS.SRM.WebAPI.Areas.PXC.BlueprintAccept.Models;
using AOS.SRM.WebAPI.Controllers;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 图纸接收
    /// </summary>
    public class BlueprintAcceptController : ApiBaseController
    {
        P_Blueprint_AcceptApp _blueprintAcceptApp = new P_Blueprint_AcceptApp();
        Plm_BlueprintApp _blueprintApp = new Plm_BlueprintApp();
        
        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="request">查询参数</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page,[FromUri] BlueprintAcceptQueryRequest request)
        {
            var result = new ResponseData();
            try
            {
                var list = _blueprintAcceptApp.GetPageList(page, t => 
                    (string.IsNullOrEmpty(request.MaterialCode) || t.MaterialCode.Contains(request.MaterialCode))
                    && (string.IsNullOrEmpty(request.Name) || t.Name.Contains(request.Name))
                    && (string.IsNullOrEmpty(request.MaterialDesc) || t.MaterialDesc.Contains(request.MaterialDesc))
                    && (string.IsNullOrEmpty(request.BlueprintCode) || t.BlueprintCode.Contains(request.BlueprintCode))
                    && (request.Type == null || t.Type == request.Type)
                    && (t.CUser == GetCurrentUser().LoginAccount)
                    && (string.IsNullOrEmpty(request.Version) || t.Version.Contains(request.Version)));
                foreach (var plmBlueprint in list)
                {
                    plmBlueprint.FileName = Path.GetFileName(plmBlueprint.DownUrl);
                    plmBlueprint.DownUrl = "/Plm/Plm_Blueprint/DownloadFile?id=" + plmBlueprint.Pid;
                }
                result.Data = new ResponsePageData { total = page.Total, items = list };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        
    }
}