using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web;
using System.Web.Http;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.PLM;
using AOS.SRM.Entity.PLM;
using AOS.SRM.Entity.PLM.Req;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;

namespace AOS.SRM.WebAPI.Areas.Plm.Controllers
{
    /// <summary>
    /// 非标图纸控制器
    /// </summary>
    public class P_Blueprint_Non_StandardController : ApiBaseController
    {
        private P_Blueprint_Non_StandardApp _app = new P_Blueprint_Non_StandardApp();

        #region 查询功能

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="pageRequest">分页查询请求参数</param>
        /// <returns>分页数据</returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]P_Blueprint_Non_StandardPageQueryRequest pageRequest)
        {
            var result = new ResponseData();
            try
            {
                // 如果没有传递分页参数，使用默认值
                if (pageRequest?.Page == null)
                {
                    if (pageRequest == null)
                        pageRequest = new P_Blueprint_Non_StandardPageQueryRequest();

                    pageRequest.Page = new Pagination
                    {
                        PageNumber = 1,
                        PageSize = 20
                    };
                }

                // 转换为查询请求参数
                var request = pageRequest.ToQueryRequest();
                var data = _app.GetPageList(pageRequest.Page, request);

                result.Data = new { rows = data, total = pageRequest.Page.Total };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 根据ID获取详情
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>详情数据</returns>
        [HttpGet]
        public IHttpActionResult GetById([FromUri]string id)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetEntityByKey(id);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 数据操作

        /// <summary>
        /// 新增非标图纸记录
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]P_Blueprint_Non_Standard entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();
                var data = _app.AddEntity(entity, currentUser.UserName);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
                result.Message = "添加成功";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 更新非标图纸记录
        /// </summary>
        /// <param name="entity">实体对象</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody]P_Blueprint_Non_Standard entity)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();
                bool success = _app.UpdateEntity(entity, currentUser.UserName);
                result.Data = success;
                result.Code = success ? (int)WMSStatusCode.Success : (int)WMSStatusCode.Failed;
                result.Message = success ? "更新成功" : "更新失败";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 删除非标图纸记录
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody]string id)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();
                bool success = _app.DeleteEntity(id, currentUser.UserName);
                result.Data = success;
                result.Code = success ? (int)WMSStatusCode.Success : (int)WMSStatusCode.Failed;
                result.Message = success ? "删除成功" : "删除失败";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 文件上传

        /// <summary>
        /// 文件上传
        /// </summary>
        /// <param name="recordId">记录ID</param>
        /// <returns>上传结果</returns>
        [HttpPost]
        public IHttpActionResult UploadFile(string recordId = null)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currentUser = GetCurrentUser();

                if (string.IsNullOrEmpty(recordId))
                {
                    // 如果没有提供记录ID，只上传文件不更新状态
                    var uploadResult = _app.UploadFile(currentUser.UserName);
                    result.Data = uploadResult;
                    result.Message = "文件上传成功";
                }
                else
                {
                    // 如果提供了记录ID，上传文件并更新记录状态
                    var uploadResult = _app.UploadFileAndUpdateStatus(recordId, currentUser.UserName);
                    result.Data = uploadResult;
                    result.Message = "文件上传成功，记录状态已更新";
                }

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出功能

        /// <summary>
        /// 导出Excel
        /// </summary>
        /// <param name="pageRequest">分页查询请求参数</param>
        /// <returns>Excel文件</returns>
        [HttpGet]
        public IHttpActionResult ExportToExcel([FromUri]P_Blueprint_Non_StandardPageQueryRequest pageRequest)
        {
            var result = new ResponseData();
            try
            {
                // 如果没有传递查询参数，使用默认值
                if (pageRequest == null)
                {
                    pageRequest = new P_Blueprint_Non_StandardPageQueryRequest();
                }

                // 转换为查询请求参数
                var request = pageRequest.ToQueryRequest();

                // 获取导出数据
                var data = _app.GetAllData(request);

                // 创建DataTable用于导出
                DataTable dt = new DataTable();
                dt.Columns.Add("物料编码", typeof(string));
                dt.Columns.Add("物料版本", typeof(string));
                dt.Columns.Add("物料描述", typeof(string));
                dt.Columns.Add("单位", typeof(string));
                dt.Columns.Add("数量", typeof(long));
                dt.Columns.Add("外部物料组", typeof(string));
                dt.Columns.Add("工厂", typeof(string));
                dt.Columns.Add("SAP订单号", typeof(string));
                dt.Columns.Add("SAP订单行", typeof(int));
                dt.Columns.Add("文件状态", typeof(string));
                dt.Columns.Add("文件名称", typeof(string));
                dt.Columns.Add("创建时间", typeof(DateTime));
                dt.Columns.Add("创建人", typeof(string));
                dt.Columns.Add("备注", typeof(string));

                // 填充数据
                foreach (var item in data)
                {
                    DataRow row = dt.NewRow();
                    row["物料编码"] = item.MaterialCode ?? "";
                    row["物料版本"] = item.MaterialVersion ?? "";
                    row["物料描述"] = item.MaterialDesc ?? "";
                    row["单位"] = item.Unit ?? "";
                    row["数量"] = item.Quantity ?? 0;
                    row["外部物料组"] = item.OutMaterialGroup ?? "";
                    row["工厂"] = item.FactoryCode ?? "";
                    row["SAP订单号"] = item.SapNo ?? "";
                    row["SAP订单行"] = item.SapLine ?? 0;
                    row["文件状态"] = GetFileStatusText(item.FileStatus);
                    row["文件名称"] = item.FileName ?? "";
                    row["创建时间"] = item.CTime ?? DateTime.MinValue;
                    row["创建人"] = item.CUser ?? "";
                    row["备注"] = item.Remark ?? "";
                    dt.Rows.Add(row);
                }

                string fileName = $"非标图纸_{DateTime.Now:yyyyMMddHHmmss}";
                return ExportToExcelFile(dt, fileName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }



        /// <summary>
        /// 获取文件状态文本
        /// </summary>
        /// <param name="fileStatus">文件状态</param>
        /// <returns>状态文本</returns>
        private string GetFileStatusText(int? fileStatus)
        {
            switch (fileStatus)
            {
                case 0:
                    return "未上传";
                case 1:
                    return "已上传";
                default:
                    return "未知";
            }
        }

        #endregion

        #region 文件下载

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>文件下载</returns>
        [HttpGet]
        public HttpResponseMessage DownloadFile([FromUri]string id)
        {
            try
            {
                if (string.IsNullOrEmpty(id))
                {
                    return new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent("记录ID不能为空")
                    };
                }

                // 根据ID获取记录
                var entity = _app.GetEntityByKey(id);
                if (entity == null)
                {
                    return new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("未找到对应的记录")
                    };
                }

                // 检查文件状态
                if (entity.FileStatus != 1)
                {
                    return new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent("文件尚未上传，无法下载")
                    };
                }

                // 检查文件路径是否存在
                if (string.IsNullOrEmpty(entity.FilePath))
                {
                    return new HttpResponseMessage(HttpStatusCode.BadRequest)
                    {
                        Content = new StringContent("文件路径为空，无法下载")
                    };
                }

                // 构建完整的文件路径
                string fullPath = HttpContext.Current.Server.MapPath("~" + entity.FilePath);

                // 检查文件是否存在
                if (!File.Exists(fullPath))
                {
                    return new HttpResponseMessage(HttpStatusCode.NotFound)
                    {
                        Content = new StringContent("文件不存在，可能已被删除")
                    };
                }

                // 获取文件名
                string fileName = entity.FileName ?? Path.GetFileName(fullPath);

                // 创建文件流
                var fileStream = new FileStream(fullPath, FileMode.Open, FileAccess.Read);

                // 创建HTTP响应
                var response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Content = new StreamContent(fileStream);
                response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
                {
                    FileName = HttpUtility.UrlEncode(fileName)
                };
                response.Headers.Add("Access-Control-Expose-Headers", "FileName");
                response.Headers.Add("FileName", HttpUtility.UrlEncode(fileName));

                return response;
            }
            catch (Exception ex)
            {
                return new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent(ex.InnerException?.Message ?? ex.Message)
                };
            }
        }

        #endregion
    }
}
