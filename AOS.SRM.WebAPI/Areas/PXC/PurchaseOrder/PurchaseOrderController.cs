using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web.Http;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application;
using AOS.SRM.Application.PXC;
using AOS.SRM.Application.SHM;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Dto;
using AOS.SRM.Entity.PXC.ViewModel.PurchaseOrder;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;

namespace AOS.SRM.WebAPI.Areas.PXC
{
    /// <summary>
    /// 采购执行协同  采购订单
    /// </summary>
    public class PurchaseOrderController : ApiBaseController
    {
        private P_PurchaseOrderApp _app = new P_PurchaseOrderApp();
        private Sys_MailApp _mailApp = new Sys_MailApp();
        private SupplierInfoApp _supplierApp = new SupplierInfoApp();
        private PurchaseDeliveryBatchApp _deliveryBatchApp = new PurchaseDeliveryBatchApp();
        private string isSend = ConfigurationManager.AppSettings["IsSend"]?.ToString();

        #region 查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="EBELN"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPurchaseOrder([FromUri]Pagination page, [FromUri]string SupplyCode, [FromUri]string EBELN, [FromUri]string Status, [FromUri]string MaterialCode, [FromUri]string MaterialName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime,[FromUri]string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                Sys_User userInfo = GetCurrentUser();
                var itemsData = _app.GetPurchaseOrder(page, SupplyCode, EBELN, Status, MaterialCode, MaterialName, StartTime, EndTime, CompanyCode, userInfo.LoginAccount, userInfo.IsSupplier.HasValue ? userInfo.IsSupplier.Value : false,GetCurrentUser()).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
        
        #region 查询退货
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="EBELN"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPurchaseOrderReturn([FromUri]Pagination page, [FromUri]string SupplyCode, [FromUri]string EBELN, [FromUri]string MaterialCode, [FromUri]string MaterialName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime,[FromUri]string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                Sys_User userInfo = GetCurrentUser();
                var itemsData = _app.GetPurchaseOrderReturn(page, SupplyCode, EBELN, MaterialCode, MaterialName, StartTime, EndTime, CompanyCode, userInfo.LoginAccount, userInfo.IsSupplier.HasValue ? userInfo.IsSupplier.Value : false).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 接收采购需求
        /// <summary>
        /// 接收采购需求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdatePurchaseOrderStatus([FromBody]List<DeliveryBatchDto> dto)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();

                var func_result = _deliveryBatchApp.UpdatePurchaseOrderStatus(dto, loginInfo.LoginAccount);

                #region 邮件发送
                //if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                //{
                //if (loginInfo.IsSupplier == true)
                //{
                //    var message = "";
                //    var typeDesc = "接收采购订单";
                //    var receiverMail = _mailApp.GetReceiveEmail(typeDesc);
                //    if (string.IsNullOrEmpty(receiverMail))
                //    {
                //        result.Code = (int)WMSStatusCode.Failed;
                //        string msg = "邮件发送失败：收件人邮箱不能为空";
                //        result.Message = msg;
                //        return Json(result);
                //    }

                //    Sys_Mail mail = new Sys_Mail();
                //    mail.MessageTypeDesc = typeDesc;
                //    mail.SenderDisplayName = loginInfo.SupplyName;
                //    mail.ReceiverMail = receiverMail;
                //    mail.MailSubject = typeDesc;
                //    var mailBody = "您好：" + "\n" + "\t" +
                //        "采购需求已接收，请前往西子富沃德SRM系统确认！\n\t" +
                //        "采购订单、行号\n\t";
                //    foreach (var item in dto)
                //    {
                //        mailBody += item.EBELN + "、" + item.EBELP + "\n\t";
                //    }
                //    mail.MailBody = mailBody;
                //    mail.CUser = loginInfo.LoginAccount;
                //    mail.SendTime = DateTime.Now;
                //    _mailApp.SendEmail(mail, out message);
                //}
                //}
                #endregion

                result.Code = (int)WMSStatusCode.Success;
                result.Message = func_result;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                //result.Message = ex.ToString();
            }
            return Json(result);
        }
        #endregion

        #region 保存送货计划
        /// <summary>
        /// 制作送货批次
        /// </summary>
        /// <param name="lstEntity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddPurchaseOrder([FromBody] List<P_DeliveryBatch> lstEntity)
        {
            var result = new ResponseData();
            try
            {
                if (lstEntity.Count != 0)
                {
                    var loginInfo = GetCurrentUser();

                    //制作送货批次
                    _deliveryBatchApp.AddDeliveryBatch(lstEntity, loginInfo.LoginAccount);

                    #region 邮件发送
                    //if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                    //{
                    //    if (loginInfo.IsSupplier == false)
                    //    {
                    //        var message = "";
                    //        var typeDesc = "制作送货批次";
                    //        var supplierCode = lstEntity.First().SupplyCode;
                    //        var supplierInfo = _supplierApp.GetFirstEntityByFieldValue("SupplyerCode", supplierCode);
                    //        if (supplierInfo == null && string.IsNullOrEmpty(supplierInfo.EMail))
                    //        {
                    //            result.Code = (int)WMSStatusCode.Failed;
                    //            string msg = string.Format("邮件发送失败：供应商[{0}]邮箱不能为空", supplierCode);
                    //            result.Message = msg;
                    //            return Json(result);
                    //        }

                    //        Sys_Mail mail = new Sys_Mail();
                    //        mail.MessageTypeDesc = typeDesc;
                    //        mail.SenderDisplayName = loginInfo.UserName;
                    //        mail.ReceiverMail = supplierInfo.EMail;
                    //        mail.MailSubject = typeDesc;
                    //        var mailBody = "您好：" + "\n" + "\t" +
                    //            "送货计划已生成，请前往西子富沃德SRM系统确认！\n\t" +
                    //            "采购订单、采购订单行\n\t";
                    //        foreach (var item in lstEntity)
                    //        {
                    //            mailBody += item.OrderNo + "、" + item.OrderLine + "\n\t";
                    //        }
                    //        mail.MailBody = mailBody;
                    //        mail.CUser = loginInfo.LoginAccount;
                    //        mail.SendTime = DateTime.Now;
                    //        _mailApp.SendEmail(mail, out message);
                    //    }
                    //}
                    #endregion
                }
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 制作送货计划弹窗显示数据
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lstDto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPurchaseApplyQty([FromBody]List<DeliveryBatchDto> lstDto)
        {

            var result = new ResponseData();
            try
            {
                List<PXC_PurchaseApplyQty_View> lstEntity = new List<PXC_PurchaseApplyQty_View>();
                var itemsData = _app.GetPurchaseApplyQty(lstDto).ToList();
                //foreach (var item in itemsData)
                //{
                //    var entity = itemsData.Where(t => t.EBELN == item.EBELN && t.EBELP == item.EBELP).First();
                //    if (entity != null)
                //    {
                //        entity.CumulativeBatchQuantity = item.CumulativeBatchQuantity;
                //        entity.DeliveredQuantity = item.DeliveredQuantity;
                //        entity.OutstandingQuantity = item.OutstandingQuantity;
                //        lstEntity.Add(entity);
                //    }
                //}
                result.Data = itemsData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="EBELN"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string SupplyCode, [FromUri]string EBELN, [FromUri]string Status, [FromUri]string MaterialCode, [FromUri]string MaterialName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri] string CompanyCode)
        {

            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetAllExportData(SupplyCode, EBELN, Status, MaterialCode, MaterialName, StartTime, EndTime, CompanyCode);
                List<ExcelColumn<PXC_PurchaseOrder_View>> columns = ExcelService.FetchDefaultColumnList<PXC_PurchaseOrder_View>();
                string[] ignoreField = new string[] {
                    "BSART","BUKRS","PSTYP","WERKS","Status","Id","IsDelete","SaleNo","SaleLineNo","PurchaseORG","PurchaseGroup","BatchNum"
                };

                List<ExcelColumn<PXC_PurchaseOrder_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PXC_PurchaseOrder_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<PXC_PurchaseOrder_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
        
        #region 导出退货订单
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="EBELN"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFileReturn([FromUri]string SupplyCode, [FromUri]string EBELN, [FromUri]string MaterialCode, [FromUri]string MaterialName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri] string CompanyCode)
        {

            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetAllExportDataReturn(SupplyCode, EBELN, MaterialCode, MaterialName, StartTime, EndTime, CompanyCode);
                List<ExcelColumn<PXC_PurchaseOrder_View>> columns = ExcelService.FetchDefaultColumnList<PXC_PurchaseOrder_View>();
                string[] ignoreField = new string[] {
                    "BSART","BUKRS","PSTYP","WERKS","Status","Id","IsDelete","SaleNo","SaleLineNo","PurchaseORG","PurchaseGroup","BatchNum"
                };

                List<ExcelColumn<PXC_PurchaseOrder_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PXC_PurchaseOrder_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<PXC_PurchaseOrder_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}
