using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application;
using AOS.SRM.Application.PXC;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.RPT.ViewModel;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.PXC
{
    /// <summary>
    /// 采购执行协同  采购送货批次
    /// </summary>
    public class PurchaseDeliveryBatchController : ApiBaseController
    {
        private PurchaseDeliveryBatchApp _app = new PurchaseDeliveryBatchApp();

        #region 查询
        /// <summary>
        /// 查询采购送货批次列表
        /// </summary>
        /// <param name="OrderNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="page"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="status">报检状态 1：已报检 0：未报检（部分报检属于未报检）</param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPurchaseDeliveryBatch([FromUri]Pagination page, [FromUri]string OrderNo, [FromUri] string SupplyCode, [FromUri]string MaterialCode, [FromUri]string MaterialName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri] string status, [FromUri] string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                Sys_User userInfo = GetCurrentUser();
                var itemsData = _app.GetPurchaseDeliveryBatch(page, OrderNo, SupplyCode, MaterialCode, MaterialName, StartTime, EndTime, status, CompanyCode, userInfo.LoginAccount, userInfo.IsSupplier.HasValue ? userInfo.IsSupplier.Value : false).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出
        /// <summary>
        /// 
        /// </summary>
        /// <param name="OrderNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="status"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string OrderNo, [FromUri] string SupplyCode, [FromUri] string MaterialCode, [FromUri]string MaterialName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime,[FromUri] string status, [FromUri] string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetAllExportData(OrderNo, SupplyCode, MaterialCode, MaterialName, StartTime, EndTime, status, CompanyCode);
                List<ExcelColumn<PXC_PurchaseDeliveryBatch_View>> columns = ExcelService.FetchDefaultColumnList<PXC_PurchaseDeliveryBatch_View>();
                string[] ignoreField = new string[] {
                    "Id","IsDelete","NoInspectionQty","HasInspectionQty","OutstandingQuantity","DeliveredQuantity"
                };

                List<ExcelColumn<PXC_PurchaseDeliveryBatch_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PXC_PurchaseDeliveryBatch_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<PXC_PurchaseDeliveryBatch_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 删除当前制作送货批次列表
        /// <summary>
        /// 删除当前制作送货批次列表
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DeleteDeliveryBatch([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var msg = "";
                var loginInfo = GetCurrentUser();
                bool bResult = _app.DeleteDeliveryBatch(ids, loginInfo.LoginAccount,out msg);
                if (!bResult)
                {
                    result.Message = msg;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 修改当前制作送货批次列表
        /// <summary>
        /// 修改当前制作送货批次列表
        /// </summary>
        /// <param name="P_DeliveryBatch"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult UpdateDeliveryBatch([FromUri] P_DeliveryBatch P_DeliveryBatch)
        {
            var result = new ResponseData();
            try
            {
                string error_message = "";
                bool batch = _app.UpdateDeliveryBatch(P_DeliveryBatch, out error_message);
                if (!batch)
                {
                    result.Data = error_message;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        /// <summary>
        /// 采购订单送货批次配置信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="NAME1"></param>
        /// <param name="EBELN"></param>
        /// <param name="Status"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPurchaseApplyQty([FromUri]Pagination page, [FromUri]string NAME1, [FromUri]string EBELN, [FromUri]string Status, [FromUri]string MaterialCode, [FromUri]string MaterialName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime)
        {

            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPurchaseApplyQty(page, NAME1, EBELN, Status, MaterialCode, MaterialName, StartTime, EndTime).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
    }
}
