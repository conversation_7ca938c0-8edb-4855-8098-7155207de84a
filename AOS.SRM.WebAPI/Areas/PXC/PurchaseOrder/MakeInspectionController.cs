using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application;
using AOS.SRM.Application.PXC;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Dto;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using System.Web.Http;
using Newtonsoft.Json;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.PXC
{
    /// <summary>
    /// 报检单
    /// </summary>
    public class MakeInspectionController : ApiBaseController
    {
        private Sys_DictionaryApp _dictionaryApp = new Sys_DictionaryApp();
        private P_InspectionApp _app = new P_InspectionApp();

        #region 报检单页面

        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMakeInspection([FromUri]Pagination page, [FromUri]string InspectionNo, [FromUri]string SupplyCode, [FromUri]string MaterialCode, [FromUri]string orderNo, [FromUri]string Status, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri]string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                Sys_User userInfo = GetCurrentUser();
                var itemsData = _app.GetMakeInspection(page, InspectionNo, SupplyCode, MaterialCode, orderNo, Status, StartTime, EndTime, CompanyCode, userInfo.LoginAccount, userInfo.IsSupplier.HasValue ? userInfo.IsSupplier.Value : false);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出
        /// <summary>
        /// 查询导出数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string InspectionNo, [FromUri]string SupplyCode, [FromUri]string MaterialCode, [FromUri]string orderNo, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri]string Status, [FromUri] string CompanyCode)
        {
            var result = new ResponseData();
            try
            {

                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetAllExportData(InspectionNo, SupplyCode, MaterialCode, orderNo, StartTime, EndTime, Status, CompanyCode);
                List<ExcelColumn<PXC_MakeInspection_View>> columns = ExcelService.FetchDefaultColumnList<PXC_MakeInspection_View>();
                string[] ignoreField = new string[] {
                    "Id","BSART","IsDelete","Status"
                };

                List<ExcelColumn<PXC_MakeInspection_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PXC_MakeInspection_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<PXC_MakeInspection_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        #region 删除
        /// <summary>
        /// 取消送货报检
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateInspection([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;
                var detail = _app.GetListByKeys(ids);
                foreach (var item in detail)
                {
                    if (item.QualifiedQty != null && item.QualifiedQty > 0)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        string msg = "报检单号[" + item.InspectionNo + "]、物料[" + item.ItemCode + "]已报检，不允许删除";
                        result.Message = msg;
                        return Json(result);
                    }
                    if (item.UnQualifiedQty != null && item.UnQualifiedQty > 0)
                    {
                        result.Code = (int)WMSStatusCode.Failed;
                        string msg = "报检单号[" + item.InspectionNo + "]、物料[" + item.ItemCode + "]已报检，不允许删除";
                        result.Message = msg;
                        return Json(result);
                    }
                    //if (item.StorageQty != null && item.StorageQty > 0)
                    //{
                    //    result.Code = (int)WMSStatusCode.Failed;
                    //    string msg = "报检单号[" + item.InspectionNo + "]、物料[" + item.ItemCode + "]已入库，不允许取消";
                    //    result.Message = msg;
                    //    return Json(result);
                    //}
                    item.DUser = userCode;
                    item.IsDelete = true;
                }
                _app.UpdateWithTran(detail);

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                // result.Message = ex.InnerException?.Message ?? ex.Message;
                result.Message = ex.ToString();
            }
            return Json(result);
        }
        #endregion

        #endregion

        #region  制作报检单页面

        /// <summary>
        /// 查询供应商
        /// </summary>
        /// <returns></returns>
        [HttpGet]

        public IHttpActionResult GetSupplierCode()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _dictionaryApp.GetList().OrderBy(x => x.EnumKey).OrderBy(x => x.EnumValue1).Where(x => x.TypeCode == "XZ_COMPANY").ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        /// <summary>
        /// 生成单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                string docNum = "";
                docNum = base.GenerateDocNum(DocType.PO, DocFixedNumDef.PO_Make_Inspection_Report);
                if (string.IsNullOrEmpty(docNum))
                {
                    docNum = "I" + DateTime.Now.ToString("yyyyMMddHHmmss");
                }
                result.Data = docNum;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        ///// <summary>
        ///// 删除当前列表   
        ///// </summary>
        ///// <returns></returns>
        //[HttpPost]
        //public IHttpActionResult DeleteInspectionDetail([FromBody]string[] ids)
        //{
        //    var result = new ResponseData();
        //    try
        //    {
        //        var userCode = GetCurrentUser().LoginAccount;
        //        result.Data = _app.DeleteInspectionDetail(ids, userCode);
        //        result.Code = (int)WMSStatusCode.Success;
        //    }
        //    catch (Exception ex)
        //    {
        //        result.Code = (int)WMSStatusCode.UnHandledException;
        //        result.Message = ex.InnerException?.Message ?? ex.Message;
        //    }
        //    return Json(result);
        //}

        #region 新增
        /// <summary>
        /// 保存功能
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult InsertInspectionDetail([FromBody] P_InspectionDetail_Views dto)
        {
            var result = new ResponseData();
            string userCode = GetCurrentUser().LoginAccount;
            try
            {
                if (string.IsNullOrEmpty(dto.InspectionNo.Trim()) || _app.Any(t => t.InspectionNo == dto.InspectionNo))
                {
                    dto.InspectionNo = "I" + DateTime.Now.ToString("yyyyMMddHHmmss");
                }
                dto.CUser = userCode;
                dto.CTime = DateTime.Now;

                foreach (var item in dto.P_InspectionDetail)
                {
                    if (item.InspectionQty <= 0)
                    {
                        result.Message = "报检单数量必须大于0";
                        result.Code = (int)WMSStatusCode.Failed;
                        return Json(result);
                    }
                    //单号
                    item.InspectionNo = dto.InspectionNo;
                    item.Id = Guid.NewGuid().ToString();
                    item.CUser = userCode;
                    item.CTime = DateTime.Now;
                    item.SupplyName = dto.SupplyName;
                }

                _app.InsertInspection(dto, userCode);
                result.Data = dto.InspectionNo;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #endregion

        #region 打印

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Print(JObject jo)
        {
            var result = new ResponseData();
            var list = new List<string>();

            var docNums = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "docNums"));
            var templateCode = getValue(jo, "templateCode");

            string[] InspectionNo = docNums.ToList().Distinct().ToArray();
            if (InspectionNo == null || InspectionNo.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                foreach (var item in docNums.ToList().Distinct())
                {
                    DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                    DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;

                    DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                    {
                        Name = "@InspectionNo",
                        Type = typeof(string),
                        Value = item
                    };

                    dataSource.Queries[0].Parameters[0] = parameter;
                    var path = base.GetPrintPDFPath(report);
                    if (!string.IsNullOrEmpty(path))
                    {
                        list.Add(path);
                    }
                }

                //string inspectionNo = string.Join(",", InspectionNo);
                //DevExpress.XtraReports.UI.XtraReport report =
                //    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") 
                //    + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                //DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                //DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                //{
                //    Name = "@InspectionNo",
                //    Type = typeof(string),
                //    Value = inspectionNo
                //};
                //dataSource.Queries[0].Parameters[0] = parameter;

                //return base.PrintToPDF(report);

            }
            result.Data = list;
            return Json(result);
        }

        #endregion


        /// <summary>  
        /// GET Method  
        /// </summary>  
        /// <returns></returns>  
        public static string HttpGet(string url)
        {
            HttpWebRequest myRequest = (HttpWebRequest)WebRequest.Create(url);
            myRequest.Method = "GET";

            HttpWebResponse myResponse = null;
            try
            {
                myResponse = (HttpWebResponse)myRequest.GetResponse();
                StreamReader reader = new StreamReader(myResponse.GetResponseStream(), Encoding.UTF8);
                string content = reader.ReadToEnd();
                return content;
            }
            //异常请求  
            catch (WebException e)
            {
                myResponse = (HttpWebResponse)e.Response;
                using (Stream errData = myResponse.GetResponseStream())
                {
                    using (StreamReader reader = new StreamReader(errData))
                    {
                        string text = reader.ReadToEnd();

                        return text;
                    }
                }
            }
        }
    }
}
