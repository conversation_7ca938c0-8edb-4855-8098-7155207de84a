using System;
using System.Linq;
using System.Linq.Expressions;
using System.Web.Http;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.PXC;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Req;
using AOS.SRM.Entity.PXC.ViewModel;
using AOS.SRM.WebAPI.Controllers;
using System.Collections.Generic;

namespace AOS.SRM.WebAPI.Areas.PXC
{
    /// <summary>
    /// 图纸发放记录控制器
    /// </summary>
    public class P_Blueprint_Send_RecordController : ApiBaseController
    {
        private readonly P_Blueprint_Send_RecordApp _app = new P_Blueprint_Send_RecordApp();

        #region 查询接口

        /// <summary>
        /// 分页查询图纸发放记录
        /// </summary>
        /// <param name="page">分页参数</param>
        /// <param name="request">查询请求参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]P_Blueprint_Send_RecordQueryRequest request)
        {
            var result = new ResponseData();
            try
            {
                var list = _app.GetDetailPageList(page, t => 
                    (string.IsNullOrEmpty(request.BlueprintId) || t.BlueprintId == request.BlueprintId)
                    && (string.IsNullOrEmpty(request.AcceptId) || t.AcceptId == request.AcceptId)
                    && (request.CreateTimeStart == null || t.CTime >= request.CreateTimeStart)
                    && (request.CreateTimeEnd == null || t.CTime < request.CreateTimeEnd.Value.AddDays(1))
                    && (string.IsNullOrEmpty(request.MaterialCode) || t.MaterialCode.Contains(request.MaterialCode))
                    && (t.CUser == GetCurrentUser().LoginAccount)
                    && (string.IsNullOrEmpty(request.Keyword) || (t.Remark != null && t.Remark.Contains(request.Keyword))));
                result.Data = new ResponsePageData { total = page.Total, items = list };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 根据ID获取单个记录
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetById([FromUri]GetByIdRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || string.IsNullOrEmpty(request.Id))
                {
                    result.Code = (int)WMSStatusCode.ParameterError;
                    result.Message = "ID不能为空";
                    return Json(result);
                }

                var data = _app.GetEntityByKey(request.Id);
                if (data == null || data.IsDelete)
                {
                    result.Code = (int)WMSStatusCode.DataNotFound;
                    result.Message = "记录不存在";
                    return Json(result);
                }

                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 根据图纸ID获取发放记录
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetByBlueprintId([FromUri]GetByBlueprintIdRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || string.IsNullOrEmpty(request.BlueprintId))
                {
                    result.Code = (int)WMSStatusCode.ParameterError;
                    result.Message = "图纸ID不能为空";
                    return Json(result);
                }

                var data = _app.GetRecordsByBlueprintId(request.BlueprintId);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 根据接收ID获取发放记录
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetByAcceptId([FromUri]GetByAcceptIdRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || string.IsNullOrEmpty(request.AcceptId))
                {
                    result.Code = (int)WMSStatusCode.ParameterError;
                    result.Message = "接收ID不能为空";
                    return Json(result);
                }

                var data = _app.GetRecordsByAcceptId(request.AcceptId);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 根据图纸ID和接收ID获取发放记录
        /// </summary>
        /// <param name="blueprintId">图纸ID</param>
        /// <param name="acceptId">接收ID</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetByBlueprintAndAccept([FromUri]string blueprintId, [FromUri]string acceptId)
        {
            var result = new ResponseData();
            try
            {
                if (string.IsNullOrEmpty(blueprintId) || string.IsNullOrEmpty(acceptId))
                {
                    result.Code = (int)WMSStatusCode.ParameterError;
                    result.Message = "图纸ID和接收ID不能为空";
                    return Json(result);
                }

                var data = _app.GetRecordByBlueprintAndAccept(blueprintId, acceptId);
                if (data == null)
                {
                    result.Code = (int)WMSStatusCode.DataNotFound;
                    result.Message = "记录不存在";
                    return Json(result);
                }

                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 新增接口

        /// <summary>
        /// 新增图纸发放记录
        /// </summary>
        /// <param name="entity">记录实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody]P_Blueprint_Send_Record entity)
        {
            var result = new ResponseData();
            try
            {
                if (entity == null)
                {
                    result.Code = (int)WMSStatusCode.ParameterError;
                    result.Message = "参数不能为空";
                    return Json(result);
                }

                if (string.IsNullOrEmpty(entity.BlueprintId) || string.IsNullOrEmpty(entity.AcceptId))
                {
                    result.Code = (int)WMSStatusCode.ParameterError;
                    result.Message = "图纸ID和接收ID不能为空";
                    return Json(result);
                }

                // 检查是否已存在
                var existRecord = _app.GetFirstEntity(x => x.BlueprintId == entity.BlueprintId && x.AcceptId == entity.AcceptId && x.IsDelete == false);
                if (existRecord != null)
                {
                    result.Code = (int)WMSStatusCode.DataExisted;
                    result.Message = "该接收记录已存在此图纸的发放记录";
                    return Json(result);
                }
                var data = _app.Insert(entity);
                result.Data = data;
                result.Code = (int)WMSStatusCode.Success;
                result.Message = "新增成功";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 批量发放图纸给接收记录
        /// </summary>
        /// <param name="request">批量发放请求</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult BatchSend([FromBody]BatchSendToAcceptsRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || string.IsNullOrEmpty(request.BlueprintId) || request.AcceptIds == null || !request.AcceptIds.Any())
                {
                    result.Code = (int)WMSStatusCode.ParameterError;
                    result.Message = "参数不完整";
                    return Json(result);
                }

                var success = _app.BatchSendToAccepts(request.BlueprintId, request.AcceptIds, request.CreateUser);
                if (success)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "批量发放成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.OperationFailed;
                    result.Message = "批量发放失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出接口

        /// <summary>
        /// 导出图纸发放记录到Excel
        /// </summary>
        /// <param name="request">导出请求参数</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]P_Blueprint_Send_RecordExportRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null)
                {
                    request = new P_Blueprint_Send_RecordExportRequest();
                }

                Expression<Func<P_Blueprint_Send_Record, bool>> condition = x => x.IsDelete == false;

                // 图纸ID条件
                if (!string.IsNullOrEmpty(request.BlueprintId))
                {
                    condition = x => x.IsDelete == false && x.BlueprintId == request.BlueprintId;
                }

                // 接收ID条件
                if (!string.IsNullOrEmpty(request.AcceptId))
                {
                    if (!string.IsNullOrEmpty(request.BlueprintId))
                        condition = x => x.IsDelete == false && x.BlueprintId == request.BlueprintId && x.AcceptId == request.AcceptId;
                    else
                        condition = x => x.IsDelete == false && x.AcceptId == request.AcceptId;
                }

                // 创建时间范围条件
                if (request.CreateTimeStart.HasValue || request.CreateTimeEnd.HasValue)
                {
                    var currentCondition = condition;
                    if (request.CreateTimeStart.HasValue && request.CreateTimeEnd.HasValue)
                        condition = x => currentCondition.Compile()(x) && x.CTime >= request.CreateTimeStart.Value && x.CTime <= request.CreateTimeEnd.Value;
                    else if (request.CreateTimeStart.HasValue)
                        condition = x => currentCondition.Compile()(x) && x.CTime >= request.CreateTimeStart.Value;
                    else if (request.CreateTimeEnd.HasValue)
                        condition = x => currentCondition.Compile()(x) && x.CTime <= request.CreateTimeEnd.Value;
                }

                // 创建用户条件
                if (!string.IsNullOrEmpty(request.CreateUser))
                {
                    var currentCondition = condition;
                    condition = x => currentCondition.Compile()(x) && x.CUser == request.CreateUser;
                }

                // 关键字搜索条件（备注字段）
                if (!string.IsNullOrEmpty(request.Keyword))
                {
                    var currentCondition = condition;
                    condition = x => currentCondition.Compile()(x) && (x.Remark != null && x.Remark.Contains(request.Keyword));
                }

                var itemsData = _app.GetDetailList(condition).ToList();

                // 配置Excel列
                List<ExcelColumn<P_Blueprint_Send_Record_View>> columns = ExcelService.FetchDefaultColumnList<P_Blueprint_Send_Record_View>();
                
                // 移除不需要导出的字段
                string[] ignoreFields = new string[] { "Id", "BlueprintId", "AcceptId", "DownUrl", "UserID" };
                List<ExcelColumn<P_Blueprint_Send_Record_View>> ignoreFieldList = columns.Where(t => ignoreFields.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<P_Blueprint_Send_Record_View> column in ignoreFieldList)
                {
                    columns.Remove(column);
                }

                // 设置列标题和格式化
                columns.ForEach((column) =>
                {
                    switch (column.ColumnName)
                    {
                        case "MaterialCode":
                            column.Description = "物料编码";
                            break;
                        case "BlueprintName":
                            column.Description = "图纸名称";
                            break;
                        case "Version":
                            column.Description = "图纸版本";
                            break;
                        case "EffectiveDate":
                            column.Description = "生效日期";
                            column.Formattor = (value, item) => value != null ? ((DateTime)value).ToString("yyyy-MM-dd") : "";
                            break;
                        case "UserName":
                            column.Description = "用户姓名";
                            break;
                        case "LoginAccount":
                            column.Description = "登录账号";
                            break;
                        case "Email":
                            column.Description = "电子邮箱";
                            break;
                        case "Mobile":
                            column.Description = "移动手机";
                            break;
                        case "SupplyCode":
                            column.Description = "供应商编码";
                            break;
                        case "SupplyName":
                            column.Description = "供应商名称";
                            break;
                        case "StatusDesc":
                            column.Description = "接收状态";
                            break;
                        case "PurchaseOrder":
                            column.Description = "采购订单号";
                            break;
                        case "PurchaseOrderLine":
                            column.Description = "采购订单行";
                            break;
                        case "PurchaseOrderAndLine":
                            column.Description = "采购订单号和行号";
                            break;
                        case "TypeDesc":
                            column.Description = "发放类型";
                            break;
                        case "Remark":
                            column.Description = "备注";
                            break;
                        case "CUser":
                            column.Description = "发放人";
                            break;
                        case "CTime":
                            column.Description = "发放时间";
                            column.Formattor = (value, item) => value != null ? ((DateTime)value).ToString("yyyy-MM-dd HH:mm:ss") : "";
                            break;
                        case "AcceptTime":
                            column.Description = "接收时间";
                            column.Formattor = (value, item) => value != null ? ((DateTime)value).ToString("yyyy-MM-dd HH:mm:ss") : "";
                            break;
                        case "MUser":
                            column.Description = "修改用户";
                            break;
                        case "MTime":
                            column.Description = "修改时间";
                            column.Formattor = (value, item) => value != null ? ((DateTime)value).ToString("yyyy-MM-dd HH:mm:ss") : "";
                            break;
                    }
                });

                return ExportToExcelFile<P_Blueprint_Send_Record_View>(itemsData, columns, "图纸发放记录");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
} 