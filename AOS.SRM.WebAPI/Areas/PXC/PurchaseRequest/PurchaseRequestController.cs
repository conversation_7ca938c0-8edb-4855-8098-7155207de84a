using AOS.Core.Http;
using AOS.SRM.Entity.SAP;
using HZ.WMS.Application.Sys;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.PXC.PurchaseRequest
{
    /// <summary>
    /// 采购申请
    /// </summary>
    public class PurchaseRequestController : ApiController
    {
        Sys_SAPCompanyInfoApp _sap = new Sys_SAPCompanyInfoApp();


        #region 采购申请查询
        /// <summary>
        /// 采购申请查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult PurchaseRequestQuery([FromUri]Pagination page, [FromUri]string SupplyCode, [FromUri]string MaterialCode, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                ZFGSRM005 entity = new ZFGSRM005()
                {
                    I_FLIEF = SupplyCode,
                    I_MATNR = string.IsNullOrEmpty(MaterialCode) ? "" : MaterialCode,
                    I_DAT_STA = StartTime,
                    I_DAT_END = EndTime
                };
                bool isPosted;
                string message = "";
                var data = _sap.ZFGSRM005("001", entity, out isPosted, out message);
                var groupData = data.GroupBy(t
                        => new { t.SupplyCode, t.ItemCode, t.ItemName, t.Unit, t.DeliveryDate, t.BrushingWords })
                    .Select(m => new
                    {
                        m.Key.SupplyCode,
                        m.Key.ItemCode,
                        m.Key.ItemName,
                        m.Key.Unit,
                        m.Key.DeliveryDate,
                        m.Key.BrushingWords,
                        Qty = m.Sum(p => p.Qty)
                    }).ToList();
                var itemsPageData = groupData.Skip(page.PageSize * (page.PageNumber - 1)).Take(page.PageSize).ToList();

                result.Data = new ResponsePageData { total = page.Total, items = itemsPageData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
        
    }
}
