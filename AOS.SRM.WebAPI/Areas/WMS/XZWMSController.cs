using AOS.Core.Http;
using AOS.SRM.Application.WMS;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.WMS
{
    /// <summary>
    /// SAP中间库（通用）
    /// </summary>
    public class XZWMSController: ApiBaseController
    {
        #region 初始化

        private WMSApp xzwms_app = new WMSApp();

        #endregion

        #region 查询省市区
        /// <summary>
        /// 查询省市区
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetProvince_City_District([FromUri]string parentId)
        {
            var result = new ResponseData();
            try
            {
                var data = xzwms_app.GetProvince_City_District(parentId);
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}