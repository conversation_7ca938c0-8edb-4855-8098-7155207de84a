using AOS.Core.Extensions;
using AOS.Core.Http;
using AOS.SRM.Application.IX;
using AOS.SRM.Entity.IX;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.IX
{
    /// <summary>
    /// 公告回执
    /// </summary>
    public class IX_NoticeReceiptController : ApiBaseController
    {
        IX_NoticeReceiptApp _app = new IX_NoticeReceiptApp();

        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var page = JsonConvert.DeserializeObject<Pagination>(getValue(jo, "page"));
                var startTime = Convert.ToDateTime(getValue(jo, "startTime")); //.ToDateTime().Value;//开始日期
                var endTime = Convert.ToDateTime(getValue(jo, "endTime")); //.ToDateTime().Value;//结束日期
                var supplierCode = getValue(jo, "supplierCode");//状态

                var itemsData = _app.GetPageList(page,
                    t => (t.CTime >= startTime) && (t.CTime < endTime.AddDays(1))
                    && (string.IsNullOrEmpty(supplierCode) || t.SupplierCode.Contains(supplierCode))
                    ).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 新增

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var entity = JsonConvert.DeserializeObject<IX_NoticeReceipt>(getValue(jo, "entity"));
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                string msg = "";
                result.Data = _app.AddEntity(entity, fileIds, _currentUser, out msg);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}