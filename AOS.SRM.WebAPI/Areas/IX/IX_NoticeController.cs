using AOS.Core.Extensions;
using AOS.Core.Http;
using AOS.SRM.Application.IX;
using AOS.SRM.Entity.IX;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.IX
{
    /// <summary>
    /// 公告管理
    /// </summary>
    public class IX_NoticeController : ApiBaseController
    {
        IX_NoticeApp _app = new IX_NoticeApp();

        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var page = JsonConvert.DeserializeObject<Pagination>(getValue(jo, "page"));
                var docNum = getValue(jo, "docNum");//公告编号
                var startTime = Convert.ToDateTime(getValue(jo, "startTime")); //.ToDateTime.Value);//开始日期
                var endTime = Convert.ToDateTime(getValue(jo, "endTime")); //.ToDateTime().Value;//结束日期Z
                var status = getValue(jo, "status");//状态

                var itemsData = _app.GetPageList(page,
                    t => (t.CTime >= startTime) && (t.CTime < endTime.AddDays(1))
                    && (string.IsNullOrEmpty(docNum) || t.DocNum.Contains(docNum))
                    && (string.IsNullOrEmpty(status) || t.Status.Contains(status))
                    ).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 新增

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var entity = JsonConvert.DeserializeObject<IX_Notice>(getValue(jo, "entity"));
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                string msg = "";
                result.Data = _app.AddEntity(entity, fileIds, _currentUser, out msg);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var entity = JsonConvert.DeserializeObject<IX_Notice>(getValue(jo, "entity"));
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                string msg = "";
                result.Data = _app.UpdateEntity(entity, fileIds, _currentUser, out msg);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] List<IX_Notice> entitys)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.Delete(entitys, _currentUser.LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 发布
        /// <summary>
        /// 发布
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Publish([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                //var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "ids"));
                var lstEntity = _app.GetListByKeys(ids.ToArray());
                lstEntity.ForEach(t =>
                {
                    t.Status = "已发布";
                    t.ReleaseTime = DateTime.Now;
                    t.MUser = _currentUser.LoginAccount;
                    t.MTime = DateTime.Now;
                });
                result.Data = _app.Update(lstEntity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 首页公告
        /// <summary>
        /// 首页公告
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetList(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetList(t => t.Status == "已发布").Take(5).ToList();
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}