using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application;
using AOS.SRM.Application.RPT;
using AOS.SRM.Entity.RPT.ViewModel;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.RPT.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_PLineOutPutController : ApiBaseController
    {
        RPT_PLineOutPutApp _app = new RPT_PLineOutPutApp();

        #region 查询分页列表
        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="dateTimes"></param>
        /// <param name="DType"></param>
        /// <param name="PLine"></param>
        /// <param name="ItemCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri] DateTime[] dateTimes, [FromUri]string DType, [FromUri]string PLine = "", [FromUri] string ItemCode = "")
        {
            var result = new ResponseData();
            try
            {

                List<RPT_PLineOutPut> itemsData = new List<RPT_PLineOutPut>();
                itemsData = _app.GetPageList(page, PLine, ItemCode, dateTimes, DType);

                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导出
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="PLine"></param>
        /// <param name="ItemCode"></param>
        /// <param name="dateTimes"></param>
        /// <param name="DType"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri]string PLine, [FromUri] string ItemCode, [FromUri] DateTime[] dateTimes, [FromUri] string DType)
        {
            var result = new ResponseData();
            try
            {
                List<ExcelColumn<RPT_PLineOutPut>> columns = ExcelService.FetchDefaultColumnList<RPT_PLineOutPut>();
                string[] ignoreField = new string[] { };
                var itemsData = _app.GetAllExportData(PLine, ItemCode, dateTimes, DType);
                List<ExcelColumn<RPT_PLineOutPut>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<RPT_PLineOutPut> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                columns.ForEach((column) =>
                {
                    if (column.ColumnName == "CTime")
                    {
                        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                    }
                });
                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion
    }
}