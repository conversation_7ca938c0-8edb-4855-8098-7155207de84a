using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.RPT;
using AOS.SRM.Entity.RPT.ViewModel;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.RPT.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_V_NotPostStockMoveController : ApiBaseController
    {
        V_NotPostStockMoveApp _app = new V_NotPostStockMoveApp();
        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageCountList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateTimes)
        {
            var result = new ResponseData();
            try
            {
                //Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetPageList(page, keyword, dateTimes);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateTimes)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetAllData(keyword, dateTimes)?.ToList();
                List<ExcelColumn<V_NotPostStockMove>> columns = ExcelService.FetchDefaultColumnList<V_NotPostStockMove>();
                string[] ignoreField = new string[] { };

                List<ExcelColumn<V_NotPostStockMove>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<V_NotPostStockMove> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "States")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<V_NotPostStockMove>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
}