using AOS.Core.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using AOS.SRM.Application.RPT;
using AOS.Core.Office;
using AOS.SRM.WebAPI.Controllers;
using AOS.SRM.Entity.RPT;
using AOS.SRM.Application;
using AOS.SRM.Entity.Sys;
using AOS.SRM.Entity.RPT.ViewModel;

namespace AOS.SRM.WebAPI.Areas.RPT.Controllers
{
    /// <summary>
    /// 开票申请明细
    /// </summary>

    public class RPT_InvoiceDetailsController : ApiBaseController
    {
        private RPT_InvoiceDetailsApp _app = new RPT_InvoiceDetailsApp();

        #region 开票明细报表查询
        /// <summary>
        /// 开票明细报表查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="supplyCode">供应商编码</param>
        /// <param name="billingNo">单据编号</param>
        /// <param name="invoiceNo">发票号</param>
        /// <param name="inspectionNo">报检单号</param>
        /// <param name="orderNo">采购单号</param>
        /// <param name="itemCode">物料编码</param>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetInvoiceDetails([FromUri] Pagination page, [FromUri] string supplyCode, [FromUri] string billingNo, [FromUri] string invoiceNo, [FromUri] string inspectionNo, [FromUri] string orderNo, [FromUri] string itemCode, [FromUri] DateTime startTime, [FromUri] DateTime endTime, [FromUri] string status)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetInvoiceDetails(page, supplyCode, billingNo, invoiceNo, inspectionNo, orderNo, itemCode, startTime, endTime, status);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 开票明细报表导出
        /// <summary>
        /// 开票明细报表导出
        /// </summary>
        /// <param name="supplyCode">供应商编码</param>
        /// <param name="billingNo">单据编号</param>
        /// <param name="invoiceNo">发票号</param>
        /// <param name="inspectionNo">报检单号</param>
        /// <param name="orderNo">采购单号</param>
        /// <param name="itemCode">物料编码</param>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string supplyCode, [FromUri] string billingNo, [FromUri] string invoiceNo, [FromUri] string inspectionNo, [FromUri] string orderNo, [FromUri] string itemCode, [FromUri] DateTime startTime, [FromUri] DateTime endTime, [FromUri] string status)
        {

            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetAllExportData(supplyCode, billingNo, invoiceNo, inspectionNo, orderNo, itemCode, startTime, endTime, status);
                List<ExcelColumn<V_RPT_InvoiceDetails>> columns = ExcelService.FetchDefaultColumnList<V_RPT_InvoiceDetails>();
                string[] ignoreField = new string[] {
                    "Status"
                };

                List<ExcelColumn<V_RPT_InvoiceDetails>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<V_RPT_InvoiceDetails> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<V_RPT_InvoiceDetails>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 收获记录开票状态查询
        /// <summary>
        /// 收获记录开票状态查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="supplyCode">供应商编码</param>
        /// <param name="inspectionNo">报检单号</param>
        /// <param name="orderNo">采购单号</param>
        /// <param name="itemCode">物料编码</param>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetReceiptAndInvoiceState([FromUri] Pagination page, [FromUri] string supplyCode, [FromUri] string inspectionNo, [FromUri] string orderNo, [FromUri] string itemCode, [FromUri] DateTime startTime, [FromUri] DateTime endTime, [FromUri] string status)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetReceiptAndInvoiceState(page, supplyCode, inspectionNo, orderNo, itemCode, startTime, endTime, status);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 收获记录开票状态导出
        /// <summary>
        /// 收获记录开票状态导出
        /// </summary>
        /// <param name="supplyCode">供应商编码</param>
        /// <param name="inspectionNo">报检单号</param>
        /// <param name="orderNo">采购单号</param>
        /// <param name="itemCode">物料编码</param>
        /// <param name="startTime">开始日期</param>
        /// <param name="endTime">结束日期</param>
        /// <param name="status">状态</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ReceiptAndInvoiceStateExport([FromUri] string supplyCode, [FromUri] string inspectionNo, [FromUri] string orderNo, [FromUri] string itemCode, [FromUri] DateTime startTime, [FromUri] DateTime endTime, [FromUri] string status)
        {

            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.ReceiptAndInvoiceStateExport(supplyCode, inspectionNo, orderNo, itemCode, startTime, endTime, status);
                List<ExcelColumn<V_RPT_ReceiptAndInvoiceState>> columns = ExcelService.FetchDefaultColumnList<V_RPT_ReceiptAndInvoiceState>();
                string[] ignoreField = new string[] {
                    "DocNum"
                };

                List<ExcelColumn<V_RPT_ReceiptAndInvoiceState>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<V_RPT_ReceiptAndInvoiceState> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<V_RPT_ReceiptAndInvoiceState>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

    }
}
