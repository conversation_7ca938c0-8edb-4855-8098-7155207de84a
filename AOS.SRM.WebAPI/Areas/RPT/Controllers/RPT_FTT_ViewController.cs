using AOS.SRM.WebAPI.Controllers;
using System.Web.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using AOS.Core.Http;
using AOS.SRM.Application.RPT;
using AOS.Core.Office;
using AOS.SRM.Entity.RPT;
using AOS.SRM.Application;

namespace AOS.SRM.WebAPI.Areas.RPT.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_FTT_ViewController : ApiBaseController
    {
        RPT_FTT_ViewApp _app = new RPT_FTT_ViewApp();

        #region 分页查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <param name="Ftype"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageCountList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateTimes, [FromUri]string Ftype)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageListByType(page, keyword, dateTimes, Ftype);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出
        /// <summary>
        /// 
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <param name="FType"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateTimes, [FromUri]string FType)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetAllExportData(keyword, dateTimes, FType);
                List<ExcelColumn<RPT_FTT_View>> columns = ExcelService.FetchDefaultColumnList<RPT_FTT_View>();
                string[] ignoreField = new string[] { };

                List<ExcelColumn<RPT_FTT_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<RPT_FTT_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "IsDelivery")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<RPT_FTT_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

    }
}