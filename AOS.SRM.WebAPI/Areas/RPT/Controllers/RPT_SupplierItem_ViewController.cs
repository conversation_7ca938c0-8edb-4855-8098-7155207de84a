using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.RPT;
using AOS.SRM.Entity.RPT.ViewModel;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.RPT.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_SupplierItem_ViewController : ApiBaseController
    {
        RPT_SupplierItem_ViewApp _app = new RPT_SupplierItem_ViewApp();
        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageCountList([FromUri]Pagination page, [FromUri]string keyword, [FromUri] DateTime[] dateTimes)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetPageList(page, keyword, dateTimes, currLoginUser);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri] DateTime[] dateTimes)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetAllExportData(keyword, dateTimes, currLoginUser);
                List<ExcelColumn<RPT_SupplierItem_View>> columns = ExcelService.FetchDefaultColumnList<RPT_SupplierItem_View>();
                string[] ignoreField = new string[] { };

                List<ExcelColumn<RPT_SupplierItem_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<RPT_SupplierItem_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "States")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});

                return ExportToExcelFile<RPT_SupplierItem_View>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion
    }
}