using AOS.Core.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using AOS.SRM.Application.RPT;
using AOS.Core.Office;
using AOS.SRM.WebAPI.Controllers;
using AOS.SRM.Entity.RPT;
using AOS.SRM.Application;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.RPT.Controllers
{
    /// <summary>
    /// 采购交货报表
    /// </summary>

    public class PurchaseDeliveryController : ApiBaseController
    {
        private PurchaseDeliveryApp _deliveryApp = new PurchaseDeliveryApp();

        /// <summary>
        /// 采购交货进度报表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPurchaseDeliverySchedule([FromUri]Pagination page, [FromUri]string keyword, [FromUri]DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                // 存储过程分页问题
                var itemsData = _deliveryApp.GetPurchaseDeliverySchedule(page, keyword, fromTime, toTime);
                result.Data = new ResponsePageData { total = itemsData.Count(), items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 采购交货及时率
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <param name="dateValue"></param>
        /// <returns></returns>

        [HttpGet]
        public IHttpActionResult GetPurchaseDeliveryOnTimeRate([FromUri]Pagination page, [FromUri]string keyword, [FromUri]DateTime[] dateValue)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateValue);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                // 存储过程分页问题
                var itemsData = _deliveryApp.GetPurchaseOnTimeDeliveryRate(page, keyword, fromTime, toTime);
                result.Data = new ResponsePageData { total = itemsData.Count(), items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #region 导出

        /// <summary>
        /// 交货及时率
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string keyword, [FromUri]DateTime[] dateTimes)
        {
            var result = new ResponseData();
            try
            {
                DateTime fromTime = DateTime.Now.AddMonths(-3).Date;
                DateTime toTime = DateTime.Now.Date.AddDays(7);

                if (dateTimes != null && dateTimes.Length >= 2)
                {
                    fromTime = dateTimes[0];
                    toTime = dateTimes[1];
                }
                // var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword)).ToList();
                var itemsData = _deliveryApp.GetPurchaseOnTimeDeliveryRate(keyword, fromTime, toTime);
                List<ExcelColumn<PRT_PurchaseDeliveryOnTimeRate>> columns = ExcelService.FetchDefaultColumnList<PRT_PurchaseDeliveryOnTimeRate>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser" };

                List<ExcelColumn<PRT_PurchaseDeliveryOnTimeRate>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PRT_PurchaseDeliveryOnTimeRate> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "Status")
                //    {
                //        column.Formattor = ExcelExportFormatter.StatusFormatter_PO003;
                //    }
                //});

                return ExportToExcelFile<PRT_PurchaseDeliveryOnTimeRate>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        /// <summary>
        /// 采购交货进度报表
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="dateTimes"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFileSchedule([FromUri] string keyword, [FromUri]DateTime[] dateTimes)
        {
            var result = new ResponseData();
            try
            {
                var querDateTimes = FormatProcessor.QueryDateTimesFormat(dateTimes);
                DateTime fromTime = querDateTimes[0];
                DateTime toTime = querDateTimes[1];
                // var itemsData = _app.GetList().Where(t => string.IsNullOrEmpty(keyword)).ToList();
                var itemsData = _deliveryApp.GetPurchaseDeliverySchedule(keyword, fromTime, toTime);
                List<ExcelColumn<RPT_PurchaseDeliverySchedule>> columns = ExcelService.FetchDefaultColumnList<RPT_PurchaseDeliverySchedule>();
                string[] ignoreField = new string[] { "IsDelete", "DTime", "DUser" };

                List<ExcelColumn<RPT_PurchaseDeliverySchedule>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<RPT_PurchaseDeliverySchedule> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile<RPT_PurchaseDeliverySchedule>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }


        #endregion
    }
}
