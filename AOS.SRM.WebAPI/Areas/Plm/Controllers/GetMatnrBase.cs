using System.ComponentModel;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    public class GetMatnrBase
    {
        public MatnrBase[] DataIn { get; set; }

        public MatnrBase[] DataOut { get; set; }
        
        public class MatnrBase
        {
            [Description("工厂")] 
            public string WERKS { get; set; }
            
            [Description("物料编码")] 
            public string MATNR { get; set; }

            [Description("物料描述")] 
            public string MAKTX { get; set; }

            [Description("单位")] 
            public string MEINS { get; set; }

            [Description("识别符")] 
            public string BESKZ { get; set; }

            [Description("识别号")] 
            public string SOBSL { get; set; }
        }
    }
}