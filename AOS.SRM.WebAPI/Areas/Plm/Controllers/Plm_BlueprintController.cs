using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.OMS.Application.Util;
using AOS.SRM.Application.Plm;
using AOS.SRM.Application.PXC;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.PLM;
using AOS.SRM.Entity.PLM.Req;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.PXC.Req;
using AOS.SRM.Entity.PXC.ViewModel;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Areas.PXC.BlueprintAccept.Models;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SqlSugar;

namespace AOS.SRM.WebAPI.Areas.Sys.Controllers
{
    /// <summary>
    /// 谓词构建器，用于动态组合Lambda表达式
    /// </summary>
    public static class PredicateBuilder
    {
        /// <summary>
        /// 创建一个始终返回true的表达式
        /// </summary>
        public static Expression<Func<T, bool>> True<T>() { return f => true; }

        /// <summary>
        /// 创建一个始终返回false的表达式
        /// </summary>
        public static Expression<Func<T, bool>> False<T>() { return f => false; }

        /// <summary>
        /// 组合两个表达式，使用OR操作符
        /// </summary>
        public static Expression<Func<T, bool>> Or<T>(Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
        {
            var parameter = Expression.Parameter(typeof(T));
            var leftVisitor = new ReplaceExpressionVisitor(expr1.Parameters[0], parameter);
            var left = leftVisitor.Visit(expr1.Body);
            var rightVisitor = new ReplaceExpressionVisitor(expr2.Parameters[0], parameter);
            var right = rightVisitor.Visit(expr2.Body);
            return Expression.Lambda<Func<T, bool>>(Expression.OrElse(left, right), parameter);
        }

        /// <summary>
        /// 组合两个表达式，使用AND操作符
        /// </summary>
        public static Expression<Func<T, bool>> And<T>(Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
        {
            var parameter = Expression.Parameter(typeof(T));
            var leftVisitor = new ReplaceExpressionVisitor(expr1.Parameters[0], parameter);
            var left = leftVisitor.Visit(expr1.Body);
            var rightVisitor = new ReplaceExpressionVisitor(expr2.Parameters[0], parameter);
            var right = rightVisitor.Visit(expr2.Body);
            return Expression.Lambda<Func<T, bool>>(Expression.AndAlso(left, right), parameter);
        }
    }

    /// <summary>
    /// 表达式访问器，用于替换表达式中的参数
    /// </summary>
    internal class ReplaceExpressionVisitor : ExpressionVisitor
    {
        private readonly Expression _oldValue;
        private readonly Expression _newValue;

        public ReplaceExpressionVisitor(Expression oldValue, Expression newValue)
        {
            _oldValue = oldValue;
            _newValue = newValue;
        }

        public override Expression Visit(Expression node)
        {
            if (node == _oldValue)
                return _newValue;
            return base.Visit(node);
        }
    }

    /// <summary>
    ///
    /// </summary>
    public class Plm_BlueprintController : ApiBaseController
    {
        Plm_BlueprintApp _blueprintApp = new Plm_BlueprintApp();
        P_Blueprint_AcceptApp _blueprintAcceptApp = new P_Blueprint_AcceptApp();
        P_Blueprint_Send_RecordApp _sendRecordApp = new P_Blueprint_Send_RecordApp();
        P_Blueprint_Send_UserApp _sendUserApp = new P_Blueprint_Send_UserApp();
        Sys_UserApp _userApp = new Sys_UserApp();
        Sys_MailApp _mailApp = new Sys_MailApp();
        PlmOracleFileApp _oracleFileApp = new PlmOracleFileApp();
        Sys_DictionaryApp _dictionaryApp = new Sys_DictionaryApp();

        /// <summary>
        /// 从Oracle数据库查询文件名
        /// </summary>
        /// <param name="oid">文件OID</param>
        /// <returns>文件名</returns>
        private string GetFileNameFromOracle(string oid)
        {
            try
            {
                // 使用SqlSugar方式查询Oracle数据库
                var oracleFile = _oracleFileApp.GetFileByUrl(oid);
                if (oracleFile != null)
                {
                    Debug.WriteLine($"Oracle查询结果: {oracleFile.FileName}");
                    return oracleFile.FileName;
                }
                else
                {
                    Debug.WriteLine("Oracle查询未返回结果");
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不中断流程
                Debug.WriteLine($"Oracle查询异常: {ex.Message}");
            }

            return null;
        }

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="request">查询参数</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page,[FromUri] BlueprintQueryRequest request)
        {
            var result = new ResponseData();
            try
            {
                var blueprintTypeList = new string[]{};
                var werksList = new string[]{};
                var materialList = new string[]{};
                if (!string.IsNullOrEmpty(GetCurrentUser().BlueprintType))
                {
                    blueprintTypeList = GetCurrentUser().BlueprintType.Split(',');
                }

                if (!string.IsNullOrEmpty(GetCurrentUser().CompanyCode))
                {
                    werksList = GetCurrentUser().CompanyCode.Split(',');
                }

                if (!string.IsNullOrEmpty(GetCurrentUser().MaterialType))
                {
                    materialList = GetCurrentUser().MaterialType.Split(',');
                }
                
                var blueprintSendGroup = GetCurrentUser().BlueprintSendGroup;
                
                // 使用导航属性构建查询条件
                Expression<Func<Plm_Blueprint, bool>> condition = t => t.IsDelete == false;
                
                // 添加物料编码条件
                if (!string.IsNullOrEmpty(request.MaterialCode))
                {
                    var materialCode = request.MaterialCode;
                    Expression<Func<Plm_Blueprint, bool>> materialCondition = t => t.MaterialCode.Contains(materialCode);
                    condition = PredicateBuilder.And(condition, materialCondition);
                }
                
                // 添加名称条件
                if (!string.IsNullOrEmpty(request.Name))
                {
                    var name = request.Name;
                    Expression<Func<Plm_Blueprint, bool>> nameCondition = t => t.Name.Contains(name);
                    condition = PredicateBuilder.And(condition, nameCondition);
                }
                
                // 添加版本条件
                if (!string.IsNullOrEmpty(request.Version))
                {
                    var version = request.Version;
                    Expression<Func<Plm_Blueprint, bool>> versionCondition = t => t.Version.Contains(version);
                    condition = PredicateBuilder.And(condition, versionCondition);
                }
                
                // 添加工厂代码条件
                if (!string.IsNullOrEmpty(request.WERKS))
                {
                    var werks = request.WERKS;
                    Expression<Func<Plm_Blueprint, bool>> werksCondition = t => t.WERKS.Contains(werks);
                    condition = PredicateBuilder.And(condition, werksCondition);
                }
                
                // 添加CA号条件
                if (!string.IsNullOrEmpty(request.AENNR))
                {
                    var aennr = request.AENNR;
                    Expression<Func<Plm_Blueprint, bool>> aennrCondition = t => t.AENNR.Contains(aennr);
                    condition = PredicateBuilder.And(condition, aennrCondition);
                }
                
                // 添加释放状态条件
                if (request.ReleaseStatus.HasValue)
                {
                    var releaseStatus = request.ReleaseStatus.Value;
                    Expression<Func<Plm_Blueprint, bool>> releaseCondition = t => t.ReleaseStatus == releaseStatus;
                    condition = PredicateBuilder.And(condition, releaseCondition);
                }

                // 添加手动发放类型条件
                if (request.HandMovementSendType.HasValue)
                {
                    var handMovementSendType = request.HandMovementSendType.Value;
                    Expression<Func<Plm_Blueprint, bool>> handMovementCondition = t => t.HandMovementSendType == handMovementSendType;
                    condition = PredicateBuilder.And(condition, handMovementCondition);
                }
                
                // 添加创建时间开始条件
                if (request.CTimeStart.HasValue)
                {
                    var cTimeStart = request.CTimeStart.Value;
                    Expression<Func<Plm_Blueprint, bool>> cTimeStartCondition = t => t.CTime >= cTimeStart;
                    condition = PredicateBuilder.And(condition, cTimeStartCondition);
                }
                
                // 添加创建时间结束条件
                if (request.CTimeEnd.HasValue)
                {
                    var cTimeEnd = request.CTimeEnd.Value.AddDays(1);
                    Expression<Func<Plm_Blueprint, bool>> cTimeEndCondition = t => t.CTime < cTimeEnd;
                    condition = PredicateBuilder.And(condition, cTimeEndCondition);
                }
                
                // 添加释放时间开始条件
                if (request.ReleaseTimeStart.HasValue)
                {
                    var releaseTimeStart = request.ReleaseTimeStart.Value;
                    Expression<Func<Plm_Blueprint, bool>> releaseTimeStartCondition = t => t.ReleaseTime >= releaseTimeStart;
                    condition = PredicateBuilder.And(condition, releaseTimeStartCondition);
                }
                
                // 添加释放时间结束条件
                if (request.ReleaseTimeEnd.HasValue)
                {
                    var releaseTimeEnd = request.ReleaseTimeEnd.Value.AddDays(1);
                    Expression<Func<Plm_Blueprint, bool>> releaseTimeEndCondition = t => t.ReleaseTime < releaseTimeEnd;
                    condition = PredicateBuilder.And(condition, releaseTimeEndCondition);
                }
                
                // 添加图纸类型条件
                Expression<Func<Plm_Blueprint, bool>> typeListCondition = t => blueprintTypeList.Contains(t.BlueprintType) || string.IsNullOrEmpty(t.BlueprintType);
                condition = PredicateBuilder.And(condition, typeListCondition);
                
                // 添加物料类型条件
                Expression<Func<Plm_Blueprint, bool>> materialListCondition = t => materialList.Contains(t.MaterialType) || string.IsNullOrEmpty(t.MaterialType);
                condition = PredicateBuilder.And(condition, materialListCondition);
                
                // 添加工厂列表条件
                var werksListLocal = werksList;
                Expression<Func<Plm_Blueprint, bool>> werksListCondition = t => werksListLocal.Contains(t.WERKS);
                condition = PredicateBuilder.And(condition, werksListCondition);
                
                // 添加类型条件
                if (!string.IsNullOrEmpty(request.MaterialType))
                {
                    var type = request.MaterialType;
                    Expression<Func<Plm_Blueprint, bool>> typeCondition = t => t.MaterialType.Contains(type);
                    condition = PredicateBuilder.And(condition, typeCondition);
                }

                // 添加图纸类型条件
                if (!string.IsNullOrEmpty(request.BlueprintType))
                {
                    var blueprintType = request.BlueprintType;
                    Expression<Func<Plm_Blueprint, bool>> blueprintTypeCondition = t => t.BlueprintType.Contains(blueprintType);
                    condition = PredicateBuilder.And(condition, blueprintTypeCondition);
                }
                
                // 获取已发送图纸ID列表
                var sentBlueprintIds = new List<string>();
                if (request.SendStatus.HasValue)
                {
                    // 处理多个发送组（用逗号分割）
                    var sendUserRecords = new List<P_Blueprint_Send_User>();
                    if (blueprintSendGroup.HasValue && blueprintSendGroup.Value > 0)
                    {
                        string sendGroupStr = blueprintSendGroup.Value.ToString();
                        string[] userSendGroups = sendGroupStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                        foreach (string groupStr in userSendGroups)
                        {
                            if (int.TryParse(groupStr.Trim(), out int groupId) && groupId > 0)
                            {
                                Expression<Func<P_Blueprint_Send_User, bool>> sendUserCondition = su => su.BlueprintSendGroup == groupId && su.IsDelete == false;
                                var groupRecords = _sendUserApp.GetList(sendUserCondition).ToList();
                                sendUserRecords.AddRange(groupRecords);
                            }
                        }
                    }
                    sentBlueprintIds = sendUserRecords.Select(t => t.BlueprintId).Distinct().ToList();
                    
                    if (request.SendStatus == 1) // 已发送
                    {
                        if (sentBlueprintIds.Count == 0)
                        {
                            // 如果没有已发送的图纸，返回空结果
                            result.Data = new ResponsePageData { total = 0, items = new List<Plm_Blueprint>() };
                            result.Code = (int)WMSStatusCode.Success;
                            return Json(result);
                        }
                        
                        // 添加已发送条件
                        var ids = sentBlueprintIds;
                        Expression<Func<Plm_Blueprint, bool>> sentCondition = t => ids.Contains(t.Id);
                        condition = PredicateBuilder.And(condition, sentCondition);
                    }
                    else if (request.SendStatus == 0) // 未发送
                    {
                        // 添加未发送条件
                        var ids = sentBlueprintIds;
                        Expression<Func<Plm_Blueprint, bool>> notSentCondition = t => !ids.Contains(t.Id);
                        condition = PredicateBuilder.And(condition, notSentCondition);
                    }
                }
                
                // 执行分页查询
                var list = _blueprintApp.GetPageList(page, condition);

                // 一次性查询当前用户的所有发送状态记录，减少数据库调用次数
                Dictionary<string, bool> sendStatusDict = new Dictionary<string, bool>();
                if (!request.SendStatus.HasValue && list.Count > 0)
                {
                    // 获取当前页面所有图纸的ID列表
                    var blueprintIds = list.Select(b => b.Id).ToList();

                    // 一次性查询当前用户对这些图纸的发送状态（处理多个发送组）
                    var sendUserRecords = new List<P_Blueprint_Send_User>();
                    if (blueprintSendGroup.HasValue && blueprintSendGroup.Value > 0)
                    {
                        string sendGroupStr = blueprintSendGroup.Value.ToString();
                        string[] userSendGroups = sendGroupStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                        foreach (string groupStr in userSendGroups)
                        {
                            if (int.TryParse(groupStr.Trim(), out int groupId) && groupId > 0)
                            {
                                Expression<Func<P_Blueprint_Send_User, bool>> sendUserCondition = su =>
                                    blueprintIds.Contains(su.BlueprintId) &&
                                    su.BlueprintSendGroup == groupId &&
                                    su.IsDelete == false;

                                var groupRecords = _sendUserApp.GetList(sendUserCondition).ToList();
                                sendUserRecords.AddRange(groupRecords);
                            }
                        }
                    }

                    // 构建发送状态字典
                    sendStatusDict = sendUserRecords.GroupBy(t => t.BlueprintId).ToDictionary(su => su.Key, su => su.ToList().Count > 0);
                }

                foreach (var plmBlueprint in list)
                {
                    plmBlueprint.FileName = Path.GetFileName(plmBlueprint.DownUrl);
                    plmBlueprint.DownUrl = "/Plm/Plm_Blueprint/DownloadBlueprint?id=" + plmBlueprint.Id;

                    // 发送状态处理
                    if (request.SendStatus.HasValue)
                    {
                        plmBlueprint.SendStatus = request.SendStatus.Value;
                    }
                    else
                    {
                        // 使用字典查找发送状态，避免多次数据库查询
                        plmBlueprint.SendStatus = sendStatusDict.ContainsKey(plmBlueprint.Id) ? 1 : 0;
                    }
                }
                
                result.Data = new ResponsePageData { total = page.Total, items = list };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 接收图纸信息

        /// <summary>
        /// 接收图纸信息
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymousAttribute]
        public IHttpActionResult AcceptBlueprint([FromBody] List<BlueprintReq> blueprintReqs)
        {
            var result = new ResponseData();
            try
            {
                // Get token from header instead of request body
                var token = Request.Headers.Authorization?.Scheme;
                if (string.IsNullOrEmpty(token) || token != "kooijuaCnGYJs2rSw0My6m3p4OhLh7NDWkPYydIrP06bOG7dnHdENCljb1bBMKFi")
                {
                    result.Message = "Token Error";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                if (blueprintReqs == null || blueprintReqs.Count == 0)
                {
                    result.Message = "至少传递一条数据";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                foreach (var blueprintReq in blueprintReqs)
                {
                    if (string.IsNullOrEmpty(blueprintReq.DownUrl))
                    {
                        result.Message = "下载地址不能为空";
                        result.Code = (int)WMSStatusCode.Failed;
                        return Json(result);
                    }
                }
                List<GetMatnrBase.MatnrBase> getMatnrBaseList = blueprintReqs.Select(produce => new GetMatnrBase.MatnrBase
                {
                    WERKS = "2002",
                    MATNR = produce.MaterialCode,
                }).ToList().Where(t => !string.IsNullOrEmpty(t.MATNR)).ToList();
                string getMatnrBaseUri = ConfigurationManager.AppSettings["GetMatnrBase"];
                string requestToken = ConfigurationManager.AppSettings["SapToken"];
                string getMatnrBaseRes = HttpUtil.HttpPost($"{getMatnrBaseUri}?token={requestToken}", JsonConvert.SerializeObject(getMatnrBaseList), "POST");
                var resList = JsonConvert.DeserializeObject<List<GetMatnrBase.MatnrBase>>(getMatnrBaseRes);
                var matnrDict = resList.GroupBy(t => t.MATNR).ToDictionary(t => t.Key, t => t.First());
                List<Plm_Blueprint> insertBlueprints = new List<Plm_Blueprint>();
                List<Plm_Blueprint> updateBlueprints = new List<Plm_Blueprint>();
                foreach (var blueprintReq in blueprintReqs)
                {
                    string type = "";
                    string unit = "";
                    string materialDesc = "";
                    if (matnrDict.ContainsKey(blueprintReq.MaterialCode))
                    {
                        var material = matnrDict[blueprintReq.MaterialCode];
                        type = GetMatnrType(material);
                        unit = material.MEINS;
                        materialDesc = material.MAKTX;
                    }
                    var blueprint = _blueprintApp.GetList(t => t.Version == blueprintReq.Version && t.MaterialCode == blueprintReq.MaterialCode && t.BlueprintCode == blueprintReq.BlueprintCode).ToList();
                    
                    // 处理图纸类型
                    string blueprintType = "";
                    if (blueprintReq.MaterialCode.Contains("FA"))
                    {
                        blueprintType = "非标";
                    }
                    else
                    {
                        blueprintType = "常规";
                    }
                    
                    // 处理信息
                    if (blueprint.Count > 0)
                    {
                        var update = blueprint[0];
                        update.MaterialCode = blueprintReq.MaterialCode;
                        update.Name = blueprintReq.Name;
                        update.Version = blueprintReq.Version;
                        update.DownUrl = blueprintReq.DownUrl;
                        update.EffectiveDate = blueprintReq.EffectiveDate;
                        update.WERKS = blueprintReq.WERKS;
                        update.AENNR = blueprintReq.AENNR;
                        update.BlueprintCode = blueprintReq.BlueprintCode;
                        update.CrCode = blueprintReq.CrCode;
                        update.CreateUserName = blueprintReq.CreateUserName;
                        update.BlueprintCategory = blueprintReq.BlueprintCategory;
                        update.MaterialType = type;
                        update.Unit = unit;
                        update.MaterialDesc = materialDesc;
                        update.BlueprintType = blueprintType;
                        // 根据Type是否为空来设置释放状态
                        if (string.IsNullOrEmpty(type))
                        {
                            update.ReleaseStatus = 0;
                        }
                        else
                        {
                            update.ReleaseStatus = 1;
                            update.ReleaseTime = DateTime.Now;
                        }
                        updateBlueprints.Add(update);
                    }
                    else
                    {
                        Plm_Blueprint insert = new Plm_Blueprint();
                        insert.MaterialCode = blueprintReq.MaterialCode;
                        insert.Name = blueprintReq.Name;
                        insert.Version = blueprintReq.Version;
                        insert.DownUrl = blueprintReq.DownUrl;
                        insert.EffectiveDate = blueprintReq.EffectiveDate;
                        insert.WERKS = blueprintReq.WERKS;
                        insert.AENNR = blueprintReq.AENNR;
                        insert.BlueprintCode = blueprintReq.BlueprintCode;
                        insert.CrCode = blueprintReq.CrCode;
                        insert.CreateUserName = blueprintReq.CreateUserName;
                        insert.BlueprintCategory = blueprintReq.BlueprintCategory;
                        insert.CreateEmail = blueprintReq.CreateEmail;
                        insert.MaterialDesc = materialDesc;
                        insert.MaterialType = type;
                        insert.Unit = unit;
                        insert.SendStatus = 0; // 接收图纸时默认为未发送状态
                        insert.HandMovementSendType = 0; // 默认为批量发放
                        insert.BlueprintType = blueprintType;
                        // 根据Type是否为空来设置释放状态
                        if (string.IsNullOrEmpty(type))
                        {
                            insert.ReleaseStatus = 0;
                        }
                        else
                        {
                            insert.ReleaseStatus = 1;
                            insert.ReleaseTime = DateTime.Now;
                        }
                        insertBlueprints.Add(insert);
                    }
                }

                if (insertBlueprints.Count > 0)
                {
                    _blueprintApp.Insert(insertBlueprints);

                    // 对新插入的图纸执行自动发送检查
                    foreach (var blueprint in insertBlueprints)
                    {
                        if (blueprint.ReleaseStatus == 1) // 只有已释放的图纸才执行自动发送
                        {
                            ProcessBlueprintAutoSend(blueprint);
                        }
                    }
                }
                if (updateBlueprints.Count > 0)
                {
                    _blueprintApp.Update(updateBlueprints);

                    // 对更新的图纸执行自动发送检查
                    // foreach (var blueprint in updateBlueprints)
                    // {
                    //     if (blueprint.ReleaseStatus == 1) // 只有已释放的图纸才执行自动发送
                    //     {
                    //         ProcessBlueprintAutoSend(blueprint);
                    //     }
                    // }
                }

                result.Data = null;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception e)
            {
                result.Data = null;
                result.Message = e.Message;
                result.Code = (int)WMSStatusCode.Failed;
            }

            return Json(result);
        }

        private string GetMatnrType(GetMatnrBase.MatnrBase matnrBase)
        {
            if (matnrBase.BESKZ == "E" && string.IsNullOrEmpty(matnrBase.SOBSL))
            {
                return "自制";
            }
            else if (matnrBase.BESKZ == "F" && string.IsNullOrEmpty(matnrBase.SOBSL))
            {
                return "采购";
            }
            else if (matnrBase.BESKZ == "E" && matnrBase.SOBSL == "50")
            {
                return "虚拟";
            }
            else if (matnrBase.BESKZ == "F" && matnrBase.SOBSL == "30")
            {
                return "委外";
            }
            return "";
        }

        /// <summary>
        /// 图纸自动发送处理
        /// </summary>
        /// <param name="blueprint">图纸信息</param>
        private void ProcessBlueprintAutoSend(Plm_Blueprint blueprint)
        {
            try
            {
                // 获取自动发送规则字典数据
                var autoSendRules = _dictionaryApp.GetList(x => x.TypeCode == "BlueprintAutoSendRule" && x.IsDelete == false).ToList();

                if (autoSendRules == null || autoSendRules.Count == 0)
                {
                    return;
                }

                foreach (var rule in autoSendRules)
                {
                    // 检查是否符合自动发送条件
                    if (CheckBlueprintAutoSendCondition(blueprint, rule))
                    {
                        // 执行自动发送
                        ExecuteBlueprintAutoSend(blueprint, rule);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不影响主流程
                Debug.WriteLine($"图纸自动发送处理异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查图纸是否符合自动发送条件
        /// </summary>
        /// <param name="blueprint">图纸信息</param>
        /// <param name="rule">自动发送规则</param>
        /// <returns>是否符合条件</returns>
        private bool CheckBlueprintAutoSendCondition(Plm_Blueprint blueprint, Sys_Dictionary rule)
        {
            try
            {
                if (string.IsNullOrEmpty(rule.EnumValue))
                {
                    return false;
                }

                // 解析规则条件 JSON
                var ruleCondition = JsonConvert.DeserializeObject<BlueprintAutoSendCondition>(rule.EnumValue);
                if (ruleCondition == null)
                {
                    return false;
                }

                // 检查工厂代码
                if (!string.IsNullOrEmpty(ruleCondition.WERKS) && blueprint.WERKS != ruleCondition.WERKS)
                {
                    return false;
                }

                // 检查图纸类型
                if (!string.IsNullOrEmpty(ruleCondition.BlueprintType))
                {
                    var allowedBlueprintTypes = ruleCondition.BlueprintType.Split(',').Select(t => t.Trim()).ToList();
                    if (!string.IsNullOrEmpty(blueprint.BlueprintType) && !allowedBlueprintTypes.Contains(blueprint.BlueprintType))
                    {
                        return false;
                    }
                }

                // 检查图纸类别
                if (!string.IsNullOrEmpty(ruleCondition.BlueprintCategory))
                {
                    var allowedBlueprintCategories = ruleCondition.BlueprintCategory.Split(',').Select(c => c.Trim()).ToList();
                    if (!string.IsNullOrEmpty(blueprint.BlueprintCategory) && !allowedBlueprintCategories.Contains(blueprint.BlueprintCategory))
                    {
                        return false;
                    }
                }

                // 检查物料类型
                if (!string.IsNullOrEmpty(ruleCondition.MaterialType))
                {
                    var allowedMaterialTypes = ruleCondition.MaterialType.Split(',').Select(m => m.Trim()).ToList();
                    if (!string.IsNullOrEmpty(blueprint.MaterialType) && !allowedMaterialTypes.Contains(blueprint.MaterialType))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查自动发送条件异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行图纸自动发送
        /// </summary>
        /// <param name="blueprint">图纸信息</param>
        /// <param name="rule">自动发送规则</param>
        private void ExecuteBlueprintAutoSend(Plm_Blueprint blueprint, Sys_Dictionary rule)
        {
            try
            {
                if (string.IsNullOrEmpty(rule.EnumValue1))
                {
                    return;
                }

                // 解析接收用户账号
                var userAccounts = rule.EnumValue1.Split(',').Select(a => a.Trim()).Where(a => !string.IsNullOrEmpty(a)).ToList();
                if (userAccounts.Count == 0)
                {
                    return;
                }

                // 解析发送组配置
                var sendGroups = new List<int>();
                if (!string.IsNullOrEmpty(rule.EnumValue2))
                {
                    try
                    {
                        var groupConfig = JsonConvert.DeserializeObject<BlueprintAutoSendGroupConfig>(rule.EnumValue2);
                        if (groupConfig != null && !string.IsNullOrEmpty(groupConfig.SendGroups))
                        {
                            sendGroups = groupConfig.SendGroups.Split(',')
                                .Select(g => g.Trim())
                                .Where(g => !string.IsNullOrEmpty(g))
                                .Select(g => int.TryParse(g, out int groupId) ? groupId : 0)
                                .Where(g => g > 0)
                                .ToList();
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"解析发送组配置异常: {ex.Message}");
                    }
                }

                // 获取用户信息
                var users = new List<Sys_User>();
                foreach (var account in userAccounts)
                {
                    var user = _userApp.GetUserByAccount(account);
                    if (user != null && !string.IsNullOrEmpty(user.Email))
                    {
                        users.Add(user);
                    }
                }

                if (users.Count == 0)
                {
                    return;
                }

                // 执行自动发送逻辑
                var acceptRecords = new List<P_Blueprint_Accept>();
                var sendRecords = new List<P_Blueprint_Send_Record>();
                var sendUserRecords = new List<P_Blueprint_Send_User>();
                var emailList = new List<Sys_Mail>();

                foreach (var user in users)
                {
                    // 检查是否已存在接收记录
                    var existAcceptRecord = _blueprintAcceptApp.GetFirstEntity(x => x.MaterialCode == blueprint.MaterialCode
                        && x.Version == blueprint.Version && x.CUser == user.LoginAccount && x.IsDelete == false);

                    string acceptId;
                    if (existAcceptRecord == null)
                    {
                        // 创建接收记录
                        acceptId = Guid.NewGuid().ToString();
                        var acceptRecord = new P_Blueprint_Accept
                        {
                            Id = acceptId,
                            MaterialCode = blueprint.MaterialCode,
                            MaterialDesc = blueprint.MaterialDesc,
                            Name = blueprint.Name,
                            Version = blueprint.Version,
                            DownUrl = blueprint.DownUrl,
                            EffectiveDate = blueprint.EffectiveDate,
                            CUser = user.LoginAccount,
                            CTime = DateTime.Now,
                            Pid = blueprint.Id,
                            Status = 2, // 自动发送状态
                            Type = 1, // 自动发送类型
                            IsDelete = false
                        };
                        acceptRecords.Add(acceptRecord);
                    }
                    else
                    {
                        acceptId = existAcceptRecord.Id;
                    }

                    // 检查是否已存在发放记录
                    var existSendRecord = _sendRecordApp.GetFirstEntity(x => x.BlueprintId == blueprint.Id && x.AcceptId == acceptId && x.IsDelete == false);

                    if (existSendRecord == null)
                    {
                        // 创建发放记录
                        var sendRecord = new P_Blueprint_Send_Record
                        {
                            Id = Guid.NewGuid().ToString(),
                            BlueprintId = blueprint.Id,
                            AcceptId = acceptId,
                            MaterialCode = blueprint.MaterialCode,
                            CUser = "System", // 系统自动发送
                            CTime = DateTime.Now,
                            IsDelete = false
                        };
                        sendRecords.Add(sendRecord);

                        // 创建邮件通知
                        var mail = new Sys_Mail
                        {
                            MailID = Guid.NewGuid().ToString(),
                            UserID = user.UserID,
                            UserName = user.UserName,
                            MessageTypeDesc = "图纸自动发放通知",
                            SenderDisplayName = "系统管理员",
                            ReceiverMail = user.Email,
                            MailSubject = "图纸自动发放通知",
                            MailBody = $"您好：\n\t系统自动为您发放了新的图纸：\n\t• {blueprint.Name}（物料编码：{blueprint.MaterialCode}，版本：{blueprint.Version}）\n\n请前往西子富沃德SRM系统查看！",
                            CUser = "System",
                            CTime = DateTime.Now,
                            SendTime = DateTime.Now,
                            AutoSend = true,
                            IsDelete = false
                        };
                        emailList.Add(mail);

                        // 为每个发送组创建发送用户记录（如果用户有发送组信息）
                        if (user.BlueprintSendGroup.HasValue && user.BlueprintSendGroup.Value > 0)
                        {
                            // 处理多个发送组（用逗号分割）
                            string sendGroupStr = user.BlueprintSendGroup.Value.ToString();
                            string[] userSendGroups = sendGroupStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                            foreach (string groupStr in userSendGroups)
                            {
                                if (int.TryParse(groupStr.Trim(), out int groupId) && groupId > 0)
                                {
                                    var existSendUserRecord = _sendUserApp.GetFirstEntity(x =>
                                        x.BlueprintId == blueprint.Id &&
                                        x.UserId == user.UserID &&
                                        x.BlueprintSendGroup == groupId &&
                                        x.IsDelete == false);

                                    if (existSendUserRecord == null)
                                    {
                                        var sendUserRecord = new P_Blueprint_Send_User
                                        {
                                            Id = Guid.NewGuid().ToString(),
                                            BlueprintId = blueprint.Id,
                                            UserId = user.UserID,
                                            BlueprintSendGroup = groupId,
                                            CUser = "System",
                                            CTime = DateTime.Now,
                                            IsDelete = false
                                        };
                                        sendUserRecords.Add(sendUserRecord);
                                    }
                                }
                            }
                        }
                    }
                }

                // 批量插入记录
                if (acceptRecords.Count > 0)
                {
                    _blueprintAcceptApp.Insert(acceptRecords);
                }
                if (sendRecords.Count > 0)
                {
                    _sendRecordApp.Insert(sendRecords);
                }
                if (sendUserRecords.Count > 0)
                {
                    _sendUserApp.Insert(sendUserRecords);
                }

                // 异步发送邮件通知（与手动发送保持一致）
                if (emailList.Count > 0)
                {
                    Task.Run(() =>
                    {
                        var emailSendResults = new List<string>();
                        foreach (var mail in emailList)
                        {
                            try
                            {
                                string message;
                                bool sendSuccess = _mailApp.SendEmail(mail, out message);

                                if (sendSuccess)
                                {
                                    emailSendResults.Add($"成功发送邮件给 {mail.UserName}({mail.ReceiverMail})");
                                    Debug.WriteLine($"自动发送邮件成功: {mail.UserName}({mail.ReceiverMail})");
                                }
                                else
                                {
                                    emailSendResults.Add($"发送邮件失败给 {mail.UserName}({mail.ReceiverMail}): {message}");
                                    Debug.WriteLine($"自动发送邮件失败: {mail.UserName}({mail.ReceiverMail}) - {message}");
                                }
                            }
                            catch (Exception emailEx)
                            {
                                emailSendResults.Add($"发送邮件异常给 {mail.UserName}({mail.ReceiverMail}): {emailEx.Message}");
                                Debug.WriteLine($"自动发送邮件异常: {mail.UserName}({mail.ReceiverMail}) - {emailEx.Message}");
                            }
                        }

                        Debug.WriteLine($"图纸自动发送邮件完成: {blueprint.Name}, 邮件发送结果: {string.Join("; ", emailSendResults)}");

                        // 邮件发送完成后，更新发送组状态和图纸发送状态
                        UpdateSendGroupAndBlueprintStatus(blueprint, sendGroups, sendUserRecords.Count > 0);
                    });
                }
                else
                {
                    // 如果没有邮件需要发送，直接更新发送组状态
                    UpdateSendGroupAndBlueprintStatus(blueprint, sendGroups, sendUserRecords.Count > 0);
                }

                Debug.WriteLine($"图纸自动发送完成: {blueprint.Name}, 发送给 {users.Count} 个用户, 涉及发送组: {string.Join(",", sendGroups)}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"执行图纸自动发送异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新发送组状态和图纸发送状态
        /// </summary>
        /// <param name="blueprint">图纸信息</param>
        /// <param name="sendGroups">发送组列表</param>
        /// <param name="hasSendUserRecords">是否有发送用户记录</param>
        private void UpdateSendGroupAndBlueprintStatus(Plm_Blueprint blueprint, List<int> sendGroups, bool hasSendUserRecords)
        {
            try
            {
                // 更新发送组状态
                if (sendGroups.Count > 0)
                {
                    foreach (var sendGroup in sendGroups)
                    {
                        // 检查是否已存在该发送组的发送用户记录
                        var existSendUserRecord = _sendUserApp.GetFirstEntity(x =>
                            x.BlueprintId == blueprint.Id &&
                            x.BlueprintSendGroup == sendGroup &&
                            x.IsDelete == false);

                        if (existSendUserRecord == null)
                        {
                            // 创建发送组标记记录
                            var sendUserRecord = new P_Blueprint_Send_User
                            {
                                Id = Guid.NewGuid().ToString(),
                                BlueprintId = blueprint.Id,
                                UserId = "System", // 系统自动发送标记
                                BlueprintSendGroup = sendGroup,
                                CUser = "System",
                                CTime = DateTime.Now,
                                IsDelete = false
                            };
                            _sendUserApp.Insert(sendUserRecord);

                            Debug.WriteLine($"已为发送组 {sendGroup} 创建图纸发送标记: {blueprint.Name}");
                        }
                        else
                        {
                            Debug.WriteLine($"发送组 {sendGroup} 已存在图纸发送标记: {blueprint.Name}");
                        }
                    }
                }

                // 更新图纸发送状态
                if (hasSendUserRecords || sendGroups.Count > 0)
                {
                    if (blueprint.SendStatus != 1)
                    {
                        blueprint.SendStatus = 1; // 标记为已发送
                        _blueprintApp.Update(blueprint);
                        Debug.WriteLine($"已更新图纸发送状态为已发送: {blueprint.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"更新发送组状态异常: {ex.Message}");
            }
        }

        #endregion
        
        #region 下载图纸文件

        /// <summary>
        /// 下载图纸文件
        /// </summary>
        /// <param name="id">图纸ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IHttpActionResult> DownloadFile([FromUri]string id)
        {
            try
            {
                var blueprint = _blueprintApp.GetEntityByKey(id);
                if (blueprint == null)
                {
                    return NotFound();
                }
                return await Download(blueprint);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        /// <summary>
        /// 下载图纸文件
        /// </summary>
        /// <param name="id">图纸ID</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IHttpActionResult> DownloadBlueprint([FromUri]string id)
        {
            try
            {
                var blueprint = _blueprintApp.GetEntityByKey(id);
                if (blueprint == null)
                {
                    return NotFound();
                }
                var blueprintAccept = _blueprintAcceptApp.GetList(t => t.MaterialCode == blueprint.MaterialCode && t.Version == blueprint.Version && t.CUser == GetCurrentUser().LoginAccount).ToList().FirstOrDefault();
                if (blueprintAccept != null)
                {
                    blueprintAccept.Status = 2;
                    _blueprintAcceptApp.Update(blueprintAccept);
                }
                else
                {
                    blueprintAccept = new P_Blueprint_Accept
                    {
                        MaterialCode = blueprint.MaterialCode,
                        Name = blueprint.Name,
                        Version = blueprint.Version,
                        DownUrl = blueprint.DownUrl,
                        EffectiveDate = blueprint.EffectiveDate,
                        CUser = GetCurrentUser().LoginAccount,
                        CTime = DateTime.Now,
                        Pid = blueprint.Id,
                        Status = 2
                    };
                    // 记录下载历史
                    _blueprintAcceptApp.Insert(blueprintAccept);
                }

                return await Download(blueprint);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        private async Task<IHttpActionResult> Download(Plm_Blueprint blueprint)
        {
            // 下载文件
            using (var client = new HttpClient())
            {
                // 添加Basic Auth认证
                var username = ConfigurationManager.AppSettings["BlueprintAuthUsername"];
                var password = ConfigurationManager.AppSettings["BlueprintAuthPassword"];
                if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                {
                    var auth = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{username}:{password}"));
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", auth);
                }
                
                var response = await client.GetAsync(blueprint.DownUrl);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsByteArrayAsync();
                    
                    // 从HTTP响应头中解析文件名
                    string fileName = GetFileNameFromResponse(response, blueprint);

                    // 从Oracle数据库查询文件名
                    // string oracleFileName = GetFileNameFromOracle(blueprint.DownUrl);
                    // if (!string.IsNullOrEmpty(oracleFileName))
                    // {
                    //     fileName = oracleFileName;
                    //     
                    //     // 更新Blueprint对象中的Filename字段
                    //     blueprint.Filename = fileName;
                    //     _blueprintApp.Update(blueprint);
                    // }

                    var result = new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = new ByteArrayContent(content)
                    };
                    result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
                    // 使用UTF-8格式返回文件，正确处理中文文件名
                    var contentDisposition = new ContentDispositionHeaderValue("attachment");
                    // 使用RFC 5987编码处理中文文件名
                    contentDisposition.Parameters.Add(new NameValueHeaderValue("filename", Uri.EscapeDataString(HttpUtility.UrlDecode(fileName, Encoding.UTF8))));
                    result.Content.Headers.ContentDisposition = contentDisposition;
                    return ResponseMessage(result);
                }
                else
                {
                    return InternalServerError(new Exception("文件下载失败"));
                }
            }
        }

        /// <summary>
        /// 从HTTP响应中解析文件名
        /// </summary>
        /// <param name="response">HTTP响应</param>
        /// <param name="blueprint">图纸信息</param>
        /// <returns>文件名</returns>
        private string GetFileNameFromResponse(HttpResponseMessage response, Plm_Blueprint blueprint)
        {
            string fileName = null;
            
            // 0. 优先从header中获取returnFileName
            IEnumerable<string> returnFileNameValues;
            if (response.Headers.TryGetValues("returnFileName", out returnFileNameValues))
            {
                fileName = returnFileNameValues.FirstOrDefault();
                if (!string.IsNullOrEmpty(fileName))
                {
                    return fileName;
                }
            }

            // 1. 从Content-Disposition头中获取文件名
            if (response.Content.Headers.ContentDisposition != null)
            {
                fileName = response.Content.Headers.ContentDisposition.FileName;
                if (!string.IsNullOrEmpty(fileName))
                {
                    // 去除引号
                    fileName = fileName.Trim('"');
                    if (!string.IsNullOrEmpty(fileName))
                    {
                        return fileName;
                    }
                }
            }

            // 2. 从Content-Type头中获取文件扩展名，结合图纸信息生成文件名
            string extension = "";
            if (response.Content.Headers.ContentType != null)
            {
                var contentType = response.Content.Headers.ContentType.MediaType;
                extension = GetExtensionFromContentType(contentType);
            }

            // 3. 如果没有扩展名，尝试从URL路径中获取
            if (string.IsNullOrEmpty(extension))
            {
                var urlFileName = Path.GetFileName(blueprint.DownUrl);
                if (!string.IsNullOrEmpty(urlFileName))
                {
                    extension = Path.GetExtension(urlFileName);
                }
            }

            // 4. 使用图纸信息生成文件名
            fileName = $"{blueprint.Name}_{blueprint.MaterialCode}_{blueprint.Version}";
            
            // 清理文件名中的非法字符
            var invalidChars = Path.GetInvalidFileNameChars();
            foreach (var invalidChar in invalidChars)
            {
                fileName = fileName.Replace(invalidChar, '_');
            }

            // 添加扩展名
            if (!string.IsNullOrEmpty(extension) && !extension.StartsWith("."))
            {
                extension = "." + extension;
            }
            
            return fileName + extension;
        }

        /// <summary>
        /// 根据Content-Type获取文件扩展名
        /// </summary>
        /// <param name="contentType">Content-Type</param>
        /// <returns>文件扩展名</returns>
        private string GetExtensionFromContentType(string contentType)
        {
            if (string.IsNullOrEmpty(contentType))
                return "";

            var mimeTypeMap = new Dictionary<string, string>
            {
                { "application/pdf", ".pdf" },
                { "application/msword", ".doc" },
                { "application/vnd.openxmlformats-officedocument.wordprocessingml.document", ".docx" },
                { "application/vnd.ms-excel", ".xls" },
                { "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", ".xlsx" },
                { "application/vnd.ms-powerpoint", ".ppt" },
                { "application/vnd.openxmlformats-officedocument.presentationml.presentation", ".pptx" },
                { "image/jpeg", ".jpg" },
                { "image/png", ".png" },
                { "image/gif", ".gif" },
                { "image/bmp", ".bmp" },
                { "image/tiff", ".tiff" },
                { "text/plain", ".txt" },
                { "text/csv", ".csv" },
                { "application/zip", ".zip" },
                { "application/x-rar-compressed", ".rar" },
                { "application/x-7z-compressed", ".7z" },
                { "application/dwg", ".dwg" },
                { "application/dxf", ".dxf" },
                { "application/step", ".step" },
                { "application/iges", ".iges" },
                { "application/octet-stream", ".bin" }
            };

            return mimeTypeMap.ContainsKey(contentType.ToLower()) ? mimeTypeMap[contentType.ToLower()] : "";
        }

        #endregion

        #region 确认图纸

        /// <summary>
        /// 确认图纸
        /// </summary>
        /// <param name="req">确认图纸请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ConfirmBlueprint([FromBody] ConfirmBlueprintReq req)
        {
            var result = new ResponseData();
            try
            {
                var blueprint = _blueprintAcceptApp.GetEntityByKey(req.Id);
                if (blueprint == null)
                {
                    result.Message = "图纸记录不存在";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                if (!string.IsNullOrEmpty(blueprint.PurchaseOrder))
                {
                    blueprint.Type = 1;
                }
                blueprint.PurchaseOrder = req.PurchaseOrder;
                blueprint.PurchaseOrderLine = req.PurchaseOrderLine;
                blueprint.PurchaseOrderAndLine = $"{req.PurchaseOrder}-{req.PurchaseOrderLine}";
                blueprint.Status = 3;
                blueprint.MUser = GetCurrentUser().LoginAccount;
                blueprint.MTime = DateTime.Now;

                _blueprintAcceptApp.Update(blueprint);

                result.Data = null;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception e)
            {
                result.Data = null;
                result.Message = e.Message;
                result.Code = (int)WMSStatusCode.Failed;
            }

            return Json(result);
        }

        #endregion

        #region 批量下载图纸

        /// <summary>
        /// 批量下载图纸文件（打包为ZIP）
        /// </summary>
        /// <param name="request">批量下载请求参数</param>
        /// <returns>ZIP压缩包</returns>
        [HttpPost]
        public async Task<IHttpActionResult> BatchDownloadBlueprints([FromBody] BatchDownloadRequest request)
        {
            try
            {
                if (request == null || request.BlueprintIds == null || request.BlueprintIds.Count == 0)
                {
                    return BadRequest("图纸ID列表不能为空");
                }

                // 获取图纸信息
                var blueprints = _blueprintApp.GetListByKeys(request.BlueprintIds.ToArray());
                if (blueprints == null || blueprints.Count == 0)
                {
                    return NotFound();
                }

                // 创建内存流用于存储ZIP文件
                using (var zipStream = new MemoryStream())
                {
                    using (var archive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
                    {
                        var currentUser = GetCurrentUser();
                        var httpClient = new HttpClient();

                        try
                        {
                            // 添加Basic Auth认证
                            var username = ConfigurationManager.AppSettings["BlueprintAuthUsername"];
                            var password = ConfigurationManager.AppSettings["BlueprintAuthPassword"];
                            if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                            {
                                var auth = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{username}:{password}"));
                                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", auth);
                            }

                            // 用于记录文件名重复的情况
                            var fileNameCounter = new Dictionary<string, int>();

                            foreach (var blueprint in blueprints)
                            {
                                try
                                {
                                    // 下载文件
                                    var response = await httpClient.GetAsync(blueprint.DownUrl);
                                    if (response.IsSuccessStatusCode)
                                    {
                                        var content = await response.Content.ReadAsByteArrayAsync();
                                        
                                        // 从HTTP响应头中解析文件名
                                        var originalFileName = GetFileNameFromResponse(response, blueprint);

                                        // 从Oracle数据库查询文件名
                                        string oracleFileName = GetFileNameFromOracle(blueprint.DownUrl);
                                        if (!string.IsNullOrEmpty(oracleFileName))
                                        {
                                            originalFileName = oracleFileName;
                                            
                                            // 更新Blueprint对象中的Filename字段
                                            blueprint.Filename = originalFileName;
                                            _blueprintApp.Update(blueprint);
                                        }

                                        // 处理文件名重复的情况
                                        string fileName = originalFileName;
                                        if (fileNameCounter.ContainsKey(originalFileName))
                                        {
                                            fileNameCounter[originalFileName]++;
                                            var nameWithoutExt = Path.GetFileNameWithoutExtension(originalFileName);
                                            var extension = Path.GetExtension(originalFileName);
                                            fileName = $"{nameWithoutExt}_{fileNameCounter[originalFileName]}{extension}";
                                        }
                                        else
                                        {
                                            fileNameCounter[originalFileName] = 1;
                                        }

                                        // 创建ZIP条目
                                        var zipEntry = archive.CreateEntry(fileName);
                                        using (var zipEntryStream = zipEntry.Open())
                                        {
                                            await zipEntryStream.WriteAsync(content, 0, content.Length);
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    // 单个文件下载失败不影响其他文件，继续处理
                                    // 可以在这里记录日志
                                    Debug.WriteLine($"下载文件异常: {ex.Message}");
                                    continue;
                                }
                            }
                        }
                        finally
                        {
                            httpClient.Dispose();
                        }
                    }

                    // 重置流位置
                    zipStream.Position = 0;

                    // 创建ZIP文件名
                    var zipFileName = $"图纸文件_{DateTime.Now:yyyyMMddHHmmss}.zip";

                    // 返回ZIP文件
                    var result = new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = new ByteArrayContent(zipStream.ToArray())
                    };
                    result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/zip");
                    
                    // 正确处理包含非ASCII字符的文件名
                    var contentDisposition = new ContentDispositionHeaderValue("attachment");
                    // 使用RFC 5987编码，处理Unicode字符
                    contentDisposition.Parameters.Add(new NameValueHeaderValue("filename*", "UTF-8''" + Uri.EscapeDataString(zipFileName)));
                    // 同时保留普通filename参数兼容旧浏览器
                    contentDisposition.FileName = zipFileName;
                    result.Content.Headers.ContentDisposition = contentDisposition;

                    return ResponseMessage(result);
                }
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        #endregion

        #region 手动发放图纸

        /// <summary>
        /// 手动发放图纸信息
        /// </summary>
        /// <param name="request">发放请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ManualSendBlueprint([FromBody] ManualSendBlueprintRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || request.BlueprintIds == null || request.BlueprintIds.Count == 0)
                {
                    result.Message = "图纸ID列表不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                if (request.UserIds == null || request.UserIds.Count == 0)
                {
                    result.Message = "用户ID列表不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                var currentUser = GetCurrentUser();

                // 获取图纸信息
                var allBlueprints = _blueprintApp.GetListByKeys(request.BlueprintIds.ToArray());

                var blueprints = allBlueprints;
                // 获取用户信息
                var users = _userApp.GetListByKeys(request.UserIds.ToArray());

                // 检查重复发放的情况
                var duplicateRecords = new List<string>();
                var acceptRecords = new List<P_Blueprint_Accept>();
                var sendRecords = new List<P_Blueprint_Send_Record>();
                var sendUserRecords = new List<P_Blueprint_Send_User>();
                var userBlueprintMap = new Dictionary<string, List<Plm_Blueprint>>();

                foreach (var blueprint in blueprints)
                {
                    foreach (var user in users)
                    {
                        // 检查是否已存在接收记录
                        var existAcceptRecord = _blueprintAcceptApp.GetFirstEntity(x => x.MaterialCode == blueprint.MaterialCode 
                            && x.Version == blueprint.Version && x.CUser == user.LoginAccount && x.IsDelete == false);
                        
                        string acceptId;
                        if (existAcceptRecord == null)
                        {
                            // 创建接收记录
                            acceptId = Guid.NewGuid().ToString();
                            var acceptRecord = new P_Blueprint_Accept
                            {
                                Id = acceptId,
                                MaterialCode = blueprint.MaterialCode,
                                MaterialDesc = blueprint.MaterialDesc,
                                BlueprintCode = blueprint.BlueprintCode,
                                Name = blueprint.Name,
                                Version = blueprint.Version,
                                DownUrl = blueprint.DownUrl,
                                EffectiveDate = blueprint.EffectiveDate,
                                CUser = user.LoginAccount,
                                CTime = DateTime.Now,
                                Pid = blueprint.Id,
                                Status = 2, // 待确认状态
                                Type = blueprint.HandMovementSendType == 1 ? 3 : 2, // 根据HandMovementSendType设置：1=单选发放(3), 0=批量发放(2)
                                IsDelete = false
                            };
                            acceptRecords.Add(acceptRecord);
                        }
                        else
                        {
                            acceptId = existAcceptRecord.Id;
                        }

                        // 检查是否已存在发放记录
                        var existSendRecord = _sendRecordApp.GetFirstEntity(x => x.BlueprintId == blueprint.Id && x.AcceptId == acceptId && x.IsDelete == false);
                        
                        if (existSendRecord == null)
                        {
                            // 创建发放记录
                            var sendRecord = new P_Blueprint_Send_Record
                            {
                                Id = Guid.NewGuid().ToString(),
                                BlueprintId = blueprint.Id,
                                AcceptId = acceptId,
                                MaterialCode = blueprint.MaterialCode,
                                CUser = currentUser.LoginAccount,
                                CTime = DateTime.Now,
                                IsDelete = false
                            };
                            sendRecords.Add(sendRecord);

                            // 收集用户对应的图纸信息，用于生成邮件（只有新发放的才发邮件）
                            if (!string.IsNullOrEmpty(user.Email))
                            {
                                var userKey = $"{user.UserID}_{user.Email}";
                                if (!userBlueprintMap.ContainsKey(userKey))
                                {
                                    userBlueprintMap[userKey] = new List<Plm_Blueprint>();
                                }
                                userBlueprintMap[userKey].Add(blueprint);
                            }
                        }
                        else
                        {
                            // 记录重复发放的信息
                            duplicateRecords.Add($"图纸\"{blueprint.Name}\"（物料编码：{blueprint.MaterialCode}，版本：{blueprint.Version}）已发放给用户\"{user.UserName}\"");
                        }
                    }
                    
                    // 检查是否已存在用户发放关联记录（处理多个发送组）
                    var currentUserSendGroup = GetCurrentUser().BlueprintSendGroup;
                    if (currentUserSendGroup.HasValue && currentUserSendGroup.Value > 0)
                    {
                        // 处理多个发送组（用逗号分割）
                        string sendGroupStr = currentUserSendGroup.Value.ToString();
                        string[] userSendGroups = sendGroupStr.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                        foreach (string groupStr in userSendGroups)
                        {
                            if (int.TryParse(groupStr.Trim(), out int groupId) && groupId > 0)
                            {
                                var existSendUserRecord = _sendUserApp.GetFirstEntity(x =>
                                    x.BlueprintId == blueprint.Id &&
                                    x.BlueprintSendGroup == groupId &&
                                    x.IsDelete == false);

                                // 如果不存在用户发放关联记录，则创建
                                if (existSendUserRecord == null)
                                {
                                    var sendUserRecord = new P_Blueprint_Send_User
                                    {
                                        Id = Guid.NewGuid().ToString(),
                                        BlueprintId = blueprint.Id,
                                        UserId = GetCurrentUser().UserID,
                                        BlueprintSendGroup = groupId,
                                        CUser = currentUser.LoginAccount,
                                        CTime = DateTime.Now,
                                        IsDelete = false
                                    };
                                    sendUserRecords.Add(sendUserRecord);
                                }
                            }
                        }
                    }
                }

                // 如果存在重复发放的记录，根据业务需求决定是否继续执行
                if (duplicateRecords.Count > 0)
                {
                    var duplicateMessage = string.Join("；", duplicateRecords);
                    
                    // 如果全部都是重复的，直接返回错误
                    if (sendRecords.Count == 0)
                    {
                        result.Message = $"发放失败，以下图纸已重复发放：{duplicateMessage}";
                        result.Code = (int)WMSStatusCode.Failed;
                        return Json(result);
                    }
                    
                    // 如果部分重复，可以选择继续执行并提示重复信息
                    // 这里采用继续执行的策略，您可以根据业务需求修改
                }

                // 为每个用户生成一封邮件，包含所有新发放给该用户的图纸信息
                var emailList = new List<Sys_Mail>();
                foreach (var kvp in userBlueprintMap)
                {
                    var userKey = kvp.Key;
                    var userBlueprints = kvp.Value;
                    var userInfo = users.Find(u => $"{u.UserID}_{u.Email}" == userKey);
                    
                    if (userInfo != null)
                    {
                        var blueprintDetails = string.Join("\n\t", userBlueprints.Select(b => 
                            $"• {b.Name}（物料编码：{b.MaterialCode}，版本：{b.Version}）"));
                        
                        var mail = new Sys_Mail
                        {
                            MailID = Guid.NewGuid().ToString(),
                            UserID = userInfo.UserID,
                            UserName = userInfo.UserName,
                            MessageTypeDesc = "图纸发放通知",
                            SenderDisplayName = "系统管理员",
                            ReceiverMail = userInfo.Email,
                            MailSubject = "图纸发放通知",
                            MailBody = $"您好：\n\t您有{userBlueprints.Count}个新的图纸需要确认：\n\t{blueprintDetails}\n\n请前往西子富沃德SRM系统查看！",
                            CUser = currentUser.LoginAccount,
                            CTime = DateTime.Now,
                            SendTime = DateTime.Now,
                            AutoSend = true,
                            IsDelete = false
                        };
                        emailList.Add(mail);
                    }
                }

                // 批量插入记录
                if (acceptRecords.Count > 0)
                {
                    _blueprintAcceptApp.Insert(acceptRecords);
                }
                if (sendRecords.Count > 0)
                {
                    _sendRecordApp.Insert(sendRecords);
                }
                if (sendUserRecords.Count > 0)
                {
                    _sendUserApp.Insert(sendUserRecords);
                }

                // 更新图纸发送状态为已发送
                if (blueprints.Count > 0)
                {
                    foreach (var blueprint in blueprints)
                    {
                        blueprint.SendStatus = 1; // 手动发放图纸时设置为已发送状态
                    }
                    _blueprintApp.Update(blueprints);
                }

                // 异步发送邮件通知
                if (emailList.Count > 0)
                {
                    Task.Run(() =>
                    {
                        foreach (var mail in emailList)
                        {
                            try
                            {
                                string message;
                                _mailApp.SendEmail(mail, out message);
                            }
                            catch (Exception ex)
                            {
                                // 邮件发送失败不影响主流程，只记录日志
                                // 可以在这里添加日志记录
                            }
                        }
                    });
                }

                // 构建返回消息
                var successMessage = $"成功发放{sendRecords.Count}条图纸记录给{users.Count}个用户";
                if (duplicateRecords.Count > 0)
                {
                    var duplicateMessage = string.Join("；", duplicateRecords);
                    successMessage += $"。以下图纸已重复发放（已跳过）：{duplicateMessage}";
                }

                result.Data = successMessage;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 获取图纸发放用户列表

        /// <summary>
        /// 根据图纸ID获取发放的用户列表及接收状态信息
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns>用户列表及接收状态信息</returns>
        [HttpGet]
        public IHttpActionResult GetBlueprintSendUsers([FromUri]GetBlueprintSendUsersRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || string.IsNullOrEmpty(request.BlueprintId))
                {
                    result.Message = "图纸ID不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 验证图纸是否存在
                var blueprint = _blueprintApp.GetEntityByKey(request.BlueprintId);
                if (blueprint == null)
                {
                    result.Message = "图纸不存在";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 使用SQL连接查询优化性能
                string sql = @"
                SELECT 
                    u.UserID, u.UserName, u.LoginAccount, u.Email, u.Mobile, u.SupplyCode, u.SupplyName,
                    a.Id AS AcceptId, a.Status, a.PurchaseOrder, a.PurchaseOrderLine, a.PurchaseOrderAndLine,
                    a.Type, a.CTime AS AcceptTime, a.MTime AS ModifyTime, a.MUser AS ModifyUser,
                    s.Id AS SendRecordId, s.CTime AS SendTime, s.CUser AS SendUser
                FROM 
                    P_Blueprint_Accept a
                JOIN 
                    Sys_User u ON a.CUser = u.LoginAccount
                LEFT JOIN 
                    P_Blueprint_Send_Record s ON a.Id = s.AcceptId AND s.IsDelete = 0
                WHERE 
                    a.Pid = @BlueprintId 
                    AND a.IsDelete = 0
                    AND (@UserName IS NULL OR u.UserName LIKE '%' + @UserName + '%')
                    AND (@LoginAccount IS NULL OR u.LoginAccount LIKE '%' + @LoginAccount + '%')
                    AND (@SupplyCode IS NULL OR u.SupplyCode LIKE '%' + @SupplyCode + '%')
                    AND (@SupplyName IS NULL OR u.SupplyName LIKE '%' + @SupplyName + '%')
                    AND (@Status IS NULL OR a.Status = @Status)
                    AND (@Type IS NULL OR a.Type = @Type)
                    AND (@PurchaseOrder IS NULL OR a.PurchaseOrder LIKE '%' + @PurchaseOrder + '%')
                ORDER BY 
                    CASE WHEN s.CTime IS NULL THEN a.CTime ELSE s.CTime END DESC";

                // 构建查询参数
                var parameters = new
                {
                    BlueprintId = request.BlueprintId,
                    UserName = string.IsNullOrEmpty(request.UserName) ? null : request.UserName,
                    LoginAccount = string.IsNullOrEmpty(request.LoginAccount) ? null : request.LoginAccount,
                    SupplyCode = string.IsNullOrEmpty(request.SupplyCode) ? null : request.SupplyCode,
                    SupplyName = string.IsNullOrEmpty(request.SupplyName) ? null : request.SupplyName,
                    Status = request.Status,
                    Type = request.Type,
                    PurchaseOrder = string.IsNullOrEmpty(request.PurchaseOrder) ? null : request.PurchaseOrder
                };

                // 执行SQL查询
                var userInfoList = _sendUserApp.DbContext.Ado.SqlQuery<BlueprintSendUserInfo>(sql, parameters);

                // 添加状态描述和类型描述
                foreach (var userInfo in userInfoList)
                {
                    userInfo.StatusDesc = GetStatusDescription(userInfo.Status);
                    userInfo.TypeDesc = GetTypeDescription(userInfo.Type);
                }

                result.Data = userInfoList;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        /// <summary>
        /// 获取状态描述
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>状态描述</returns>
        private string GetStatusDescription(int? status)
        {
            switch (status)
            {
                case 1:
                    return "待下载";
                case 2:
                    return "待确认";
                case 3:
                    return "已确认";
                default:
                    return "未知状态";
            }
        }

        /// <summary>
        /// 获取类型描述
        /// </summary>
        /// <param name="type">类型值</param>
        /// <returns>类型描述</returns>
        private string GetTypeDescription(int type)
        {
            switch (type)
            {
                case 1:
                    return "自动";
                case 2:
                    return "手动";
                default:
                    return "";
            }
        }

        #endregion

        #region 获取图纸发放用户关联列表

        /// <summary>
        /// 查询图纸发放用户关联列表
        /// </summary>
        /// <param name="request">查询请求参数</param>
        /// <returns>用户列表及发送状态信息</returns>
        [HttpGet]
        public IHttpActionResult GetBlueprintSendUserList([FromUri]BlueprintSendUserQueryRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || string.IsNullOrEmpty(request.BlueprintId))
                {
                    result.Message = "图纸ID不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 验证图纸是否存在
                var blueprint = _blueprintApp.GetEntityByKey(request.BlueprintId);
                if (blueprint == null)
                {
                    result.Message = "图纸不存在";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 使用导航属性查询
                var pagination = new Pagination { PageNumber = 1, PageSize = 1000 }; // 设置足够大的页面大小
                
                // 构建查询条件
                Expression<Func<P_Blueprint_Send_User, bool>> condition = x => 
                    x.BlueprintId == request.BlueprintId && 
                    x.IsDelete == false;
                
                // 获取所有用户（包含发送状态）
                var sendUserRecords = _sendUserApp.GetListWithNavigation(condition);
                
                // 构建用户查询条件
                Expression<Func<Sys_User, bool>> userCondition = u => u.IsDelete == false;
                
                // 根据条件动态添加筛选
                if (!string.IsNullOrEmpty(request.UserName))
                {
                    var userName = request.UserName;
                    Expression<Func<Sys_User, bool>> nameCondition = u => u.UserName.Contains(userName);
                    userCondition = PredicateBuilder.And(userCondition, nameCondition);
                }
                if (!string.IsNullOrEmpty(request.LoginAccount))
                {
                    var loginAccount = request.LoginAccount;
                    Expression<Func<Sys_User, bool>> accountCondition = u => u.LoginAccount.Contains(loginAccount);
                    userCondition = PredicateBuilder.And(userCondition, accountCondition);
                }
                if (!string.IsNullOrEmpty(request.SupplyCode))
                {
                    var supplyCode = request.SupplyCode;
                    Expression<Func<Sys_User, bool>> codeCondition = u => !string.IsNullOrEmpty(u.SupplyCode) && u.SupplyCode.Contains(supplyCode);
                    userCondition = PredicateBuilder.And(userCondition, codeCondition);
                }
                if (!string.IsNullOrEmpty(request.SupplyName))
                {
                    var supplyName = request.SupplyName;
                    Expression<Func<Sys_User, bool>> nameCondition = u => !string.IsNullOrEmpty(u.SupplyName) && u.SupplyName.Contains(supplyName);
                    userCondition = PredicateBuilder.And(userCondition, nameCondition);
                }
                
                var users = _userApp.GetList(userCondition).ToList();
                var sentUserIds = sendUserRecords.Select(x => x.UserId).ToList();
                
                // 构建返回数据
                var userInfoList = new List<BlueprintSendUserInfo>();
                
                foreach (var user in users)
                {
                    // 判断发送状态
                    bool isSent = sentUserIds.Contains(user.UserID);
                    
                    // 如果请求指定了发送状态，则过滤
                    if (request.SendStatus.HasValue && ((request.SendStatus == 1 && !isSent) || (request.SendStatus == 0 && isSent)))
                    {
                        continue;
                    }

                    var userInfo = new BlueprintSendUserInfo
                    {
                        UserID = user.UserID,
                        UserName = user.UserName,
                        LoginAccount = user.LoginAccount,
                        Email = user.Email,
                        Mobile = user.Mobile,
                        SupplyCode = user.SupplyCode,
                        SupplyName = user.SupplyName,
                        SendStatus = isSent ? 1 : 0,
                        StatusDesc = isSent ? "已发送" : "未发送"
                    };

                    // 如果已发送，添加发送相关信息
                    if (isSent)
                    {
                        var sendUserRecord = sendUserRecords.FirstOrDefault(x => x.UserId == user.UserID);
                        if (sendUserRecord != null)
                        {
                            userInfo.SendTime = sendUserRecord.CTime;
                            userInfo.SendUser = sendUserRecord.CUser;
                        }
                    }

                    userInfoList.Add(userInfo);
                }

                // 按发送状态和发送时间降序排序
                userInfoList = userInfoList.OrderByDescending(x => x.SendStatus).ThenByDescending(x => x.SendTime).ToList();

                result.Data = userInfoList;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 批量添加图纸发放用户关联

        /// <summary>
        /// 批量添加图纸发放用户关联
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult BatchAddBlueprintSendUser([FromBody] BlueprintSendUserRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || string.IsNullOrEmpty(request.BlueprintId))
                {
                    result.Message = "图纸ID不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                if (request.UserIds == null || request.UserIds.Count == 0)
                {
                    result.Message = "用户ID列表不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 验证图纸是否存在
                var blueprint = _blueprintApp.GetEntityByKey(request.BlueprintId);
                if (blueprint == null)
                {
                    result.Message = "图纸不存在";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                var currentUser = GetCurrentUser();
                var sendUserRecords = new List<P_Blueprint_Send_User>();
                var duplicateCount = 0;

                foreach (var userId in request.UserIds)
                {
                    // 检查是否已存在关联记录
                    var existRecord = _sendUserApp.GetFirstEntity(x => x.BlueprintId == request.BlueprintId && x.UserId == userId && x.IsDelete == false);
                    
                    if (existRecord == null)
                    {
                        var sendUserRecord = new P_Blueprint_Send_User
                        {
                            Id = Guid.NewGuid().ToString(),
                            BlueprintId = request.BlueprintId,
                            UserId = userId,
                            CUser = currentUser.LoginAccount,
                            CTime = DateTime.Now,
                            IsDelete = false
                        };
                        sendUserRecords.Add(sendUserRecord);
                    }
                    else
                    {
                        duplicateCount++;
                    }
                }

                // 批量插入记录
                if (sendUserRecords.Count > 0)
                {
                    _sendUserApp.Insert(sendUserRecords);
                }

                // 更新图纸发送状态为已发送
                if (blueprint != null && blueprint.SendStatus == 0)
                {
                    blueprint.SendStatus = 1;
                    _blueprintApp.Update(blueprint);
                }

                // 构建返回消息
                var successMessage = $"成功添加{sendUserRecords.Count}条图纸发放用户关联记录";
                if (duplicateCount > 0)
                {
                    successMessage += $"，{duplicateCount}条记录已存在（已跳过）";
                }

                result.Data = successMessage;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 批量删除图纸发放用户关联

        /// <summary>
        /// 批量删除图纸发放用户关联
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult BatchDeleteBlueprintSendUser([FromBody] BlueprintSendUserRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || string.IsNullOrEmpty(request.BlueprintId))
                {
                    result.Message = "图纸ID不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                if (request.UserIds == null || request.UserIds.Count == 0)
                {
                    result.Message = "用户ID列表不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                var currentUser = GetCurrentUser();
                var deleteCount = 0;

                foreach (var userId in request.UserIds)
                {
                    // 查询关联记录
                    var record = _sendUserApp.GetFirstEntity(x => x.BlueprintId == request.BlueprintId && x.UserId == userId && x.IsDelete == false);
                    
                    if (record != null)
                    {
                        // 逻辑删除
                        record.IsDelete = true;
                        record.DUser = currentUser.LoginAccount;
                        record.DTime = DateTime.Now;
                        _sendUserApp.Update(record);
                        deleteCount++;
                    }
                }

                // 检查是否还有其他用户关联记录
                var remainingRecords = _sendUserApp.GetList(x => x.BlueprintId == request.BlueprintId && x.IsDelete == false).Count();
                
                // 如果没有其他用户关联记录，更新图纸发送状态为未发送
                if (remainingRecords == 0)
                {
                    var blueprint = _blueprintApp.GetEntityByKey(request.BlueprintId);
                    if (blueprint != null && blueprint.SendStatus == 1)
                    {
                        blueprint.SendStatus = 0;
                        _blueprintApp.Update(blueprint);
                    }
                }

                result.Data = $"成功删除{deleteCount}条图纸发放用户关联记录";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 手动同步释放状态

        /// <summary>
        /// 手动同步图纸释放状态
        /// </summary>
        /// <param name="request">同步请求参数</param>
        /// <returns>同步结果</returns>
        [HttpPost]
        public IHttpActionResult SyncReleaseStatus([FromBody] SyncReleaseStatusRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || request.BlueprintIds == null || request.BlueprintIds.Count == 0)
                {
                    result.Message = "图纸ID列表不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 根据ID列表获取图纸，只处理支持批量处理的图纸
                var blueprints = _blueprintApp.GetListByKeys(request.BlueprintIds.ToArray());
                if (blueprints.Count == 0)
                {
                    result.Message = "未找到对应的图纸记录或所选图纸不支持批量处理";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 构建SAP查询请求
                List<GetMatnrBase.MatnrBase> getMatnrBaseList = blueprints.Select(blueprint => new GetMatnrBase.MatnrBase
                {
                    WERKS = "2002", // 使用固定工厂代码，与其他接口保持一致
                    MATNR = blueprint.MaterialCode,
                }).ToList().Where(t => !string.IsNullOrEmpty(t.MATNR)).ToList();

                if (getMatnrBaseList.Count == 0)
                {
                    result.Message = "没有有效的物料编码需要同步";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }
                
                string getMatnrBaseUri = ConfigurationManager.AppSettings["GetMatnrBase"];
                string requestToken = ConfigurationManager.AppSettings["SapToken"];
                string getMatnrBaseRes = HttpUtil.HttpPost($"{getMatnrBaseUri}?token={requestToken}", JsonConvert.SerializeObject(getMatnrBaseList), "POST");
                var resList = JsonConvert.DeserializeObject<List<GetMatnrBase.MatnrBase>>(getMatnrBaseRes);

                // 处理重复的MATNR，使用GroupBy取第一个
                var matnrDict = resList
                    .Where(t => !string.IsNullOrEmpty(t.MATNR))
                    .GroupBy(t => t.MATNR)
                    .ToDictionary(g => g.Key, g => g.First());
                
                // 需要更新的图纸列表
                List<Plm_Blueprint> updateBlueprints = new List<Plm_Blueprint>();
                var syncResults = new List<object>();

                // 遍历所有图纸，根据物料信息更新采购类型和释放状态
                foreach (var blueprint in blueprints)
                {
                    string oldType = blueprint.MaterialType ?? "";
                    string oldUnit = blueprint.Unit ?? "";
                    int? oldReleaseStatus = blueprint.ReleaseStatus;
                    string newType = "";
                    string newUnit = "";
                    int newReleaseStatus = 0;

                    if (matnrDict.ContainsKey(blueprint.MaterialCode))
                    {
                        var material = matnrDict[blueprint.MaterialCode];
                        newType = GetMatnrType(material);
                        newUnit = material.MEINS;
                        newReleaseStatus = string.IsNullOrEmpty(newType) ? 0 : 1;

                        // 检查是否需要更新
                        if (blueprint.MaterialType != newType ||
                            blueprint.Unit != newUnit ||
                            blueprint.ReleaseStatus != newReleaseStatus)
                        {
                            blueprint.MaterialType = newType;
                            blueprint.Unit = newUnit;
                            blueprint.ReleaseStatus = newReleaseStatus;
                            blueprint.ReleaseTime = newReleaseStatus == 1 ? DateTime.Now : (DateTime?)null;
                            blueprint.MUser = GetCurrentUser().LoginAccount;
                            blueprint.MTime = DateTime.Now;
                            updateBlueprints.Add(blueprint);
                        }
                    }

                    // 记录同步结果
                    syncResults.Add(new
                    {
                        MaterialCode = blueprint.MaterialCode,
                        Name = blueprint.Name,
                        Version = blueprint.Version,
                        OldMaterialType = oldType,
                        NewMaterialType = newType,
                        OldUnit = oldUnit,
                        NewUnit = newUnit,
                        OldReleaseStatus = oldReleaseStatus,
                        NewReleaseStatus = newReleaseStatus,
                        Updated = blueprint.MaterialType != oldType ||
                                 blueprint.Unit != oldUnit ||
                                 blueprint.ReleaseStatus != oldReleaseStatus
                    });
                }

                // 批量更新图纸信息
                if (updateBlueprints.Count > 0)
                {
                    _blueprintApp.Update(updateBlueprints);

                    // 对释放状态变更为已释放的图纸执行自动发送检查
                    foreach (var blueprint in updateBlueprints)
                    {
                        if (blueprint.ReleaseStatus == 1) // 只有已释放的图纸才执行自动发送
                        {
                            ProcessBlueprintAutoSend(blueprint);
                        }
                    }
                }

                result.Data = new
                {
                    TotalCount = blueprints.Count,
                    UpdatedCount = updateBlueprints.Count,
                    Results = syncResults
                };
                result.Code = (int)WMSStatusCode.Success;
                result.Message = $"同步完成，共处理{blueprints.Count}条记录，更新{updateBlueprints.Count}条记录";
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            
            return Json(result);
        }

        /// <summary>
        /// 批量同步所有图纸释放状态
        /// </summary>
        /// <returns>同步结果</returns>
        [HttpPost]
        public IHttpActionResult SyncAllReleaseStatus()
        {
            var result = new ResponseData();
            try
            {
                // 获取所有未删除且支持批量处理的图纸（只处理 HandMovementSendType = 0 的图纸）
                var blueprints = _blueprintApp.GetList(t => t.IsDelete == false && t.ReleaseStatus == 0 && t.HandMovementSendType == 0).ToList();
                if (blueprints == null || blueprints.Count == 0)
                {
                    result.Message = "未找到任何图纸记录";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 为了避免一次性查询过多物料导致超时，分批处理
                int batchSize = 100; // 每批处理的图纸数量
                int totalCount = blueprints.Count;
                int processedCount = 0;
                int updatedCount = 0;
                var syncResults = new List<object>();

                // 分批处理图纸
                for (int i = 0; i < totalCount; i += batchSize)
                {
                    // 获取当前批次的图纸
                    var batchBlueprints = blueprints.Skip(i).Take(batchSize).ToList();
                    processedCount += batchBlueprints.Count;

                    // 构建SAP查询请求
                    List<GetMatnrBase.MatnrBase> getMatnrBaseList = batchBlueprints.Select(blueprint => new GetMatnrBase.MatnrBase
                    {
                        WERKS = blueprint.WERKS,
                        MATNR = blueprint.MaterialCode,
                    }).ToList().Where(t => !string.IsNullOrEmpty(t.MATNR)).ToList();

                    if (getMatnrBaseList.Count == 0)
                    {
                        continue; // 跳过没有有效物料编码的批次
                    }

                    // 调用SAP接口获取物料信息
                    string getMatnrBaseUri = ConfigurationManager.AppSettings["GetMatnrBase"];
                    string requestToken = ConfigurationManager.AppSettings["SapToken"];
                    string getMatnrBaseRes = HttpUtil.HttpPost($"{getMatnrBaseUri}?token={requestToken}", JsonConvert.SerializeObject(getMatnrBaseList), "POST");
                    var resList = JsonConvert.DeserializeObject<List<GetMatnrBase.MatnrBase>>(getMatnrBaseRes);
                    var matnrDict = resList.GroupBy(t => t.MATNR).ToDictionary(t => t.Key, t => t.First());

                    // 需要更新的图纸列表
                    List<Plm_Blueprint> updateBlueprints = new List<Plm_Blueprint>();

                    foreach (var blueprint in batchBlueprints)
                    {
                        string oldType = blueprint.MaterialType ?? "";
                        string oldUnit = blueprint.Unit ?? "";
                        int? oldReleaseStatus = blueprint.ReleaseStatus;
                        string newType = "";
                        string newUnit = "";
                        int newReleaseStatus = 0;

                        if (matnrDict.ContainsKey(blueprint.MaterialCode))
                        {
                            var material = matnrDict[blueprint.MaterialCode];
                            newType = GetMatnrType(material);
                            newUnit = material.MEINS;
                            newReleaseStatus = string.IsNullOrEmpty(newType) ? 0 : 1;

                            // 检查是否需要更新
                            if (blueprint.MaterialType != newType ||
                                blueprint.Unit != newUnit ||
                                blueprint.ReleaseStatus != newReleaseStatus)
                            {
                                blueprint.MaterialType = newType;
                                blueprint.Unit = newUnit;
                                blueprint.ReleaseStatus = newReleaseStatus;
                                blueprint.ReleaseTime = newReleaseStatus == 1 ? DateTime.Now : (DateTime?)null;
                                blueprint.MUser = GetCurrentUser().LoginAccount;
                                blueprint.MTime = DateTime.Now;
                                updateBlueprints.Add(blueprint);
                            }
                        }

                        // 记录同步结果
                        syncResults.Add(new
                        {
                            MaterialCode = blueprint.MaterialCode,
                            Name = blueprint.Name,
                            Version = blueprint.Version,
                            OldMaterialType = oldType,
                            NewMaterialType = newType,
                            OldUnit = oldUnit,
                            NewUnit = newUnit,
                            OldReleaseStatus = oldReleaseStatus,
                            NewReleaseStatus = newReleaseStatus,
                            Updated = blueprint.MaterialType != oldType ||
                                     blueprint.Unit != oldUnit ||
                                     blueprint.ReleaseStatus != oldReleaseStatus
                        });
                    }

                    // 批量更新图纸
                    if (updateBlueprints.Count > 0)
                    {
                        _blueprintApp.Update(updateBlueprints);
                        updatedCount += updateBlueprints.Count;

                        // 对释放状态变更为已释放的图纸执行自动发送检查
                        foreach (var blueprint in updateBlueprints)
                        {
                            if (blueprint.ReleaseStatus == 1) // 只有已释放的图纸才执行自动发送
                            {
                                ProcessBlueprintAutoSend(blueprint);
                            }
                        }
                    }
                }

                result.Data = new
                {
                    TotalCount = totalCount,
                    ProcessedCount = processedCount,
                    UpdatedCount = updatedCount,
                    Results = syncResults.Take(100).ToList(), // 只返回前100条结果，避免响应过大
                    HasMoreResults = syncResults.Count > 100
                };
                result.Message = $"同步完成，共处理{processedCount}条记录，更新{updatedCount}条记录";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导出图纸信息

        /// <summary>
        /// 导出图纸信息到Excel
        /// </summary>
        /// <param name="request">导出请求参数</param>
        /// <returns>Excel文件</returns>
        [HttpPost]
        public IHttpActionResult ExportBlueprintsToExcel([FromBody] ExportBlueprintRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null)
                {
                    request = new ExportBlueprintRequest();
                }

                // 构建查询条件
                Expression<Func<Plm_Blueprint, bool>> condition = PredicateBuilder.True<Plm_Blueprint>();
                condition = condition.And(x => x.IsDelete == false);

                // 添加查询条件
                if (!string.IsNullOrEmpty(request.MaterialCode))
                {
                    condition = condition.And(x => x.MaterialCode.Contains(request.MaterialCode));
                }

                if (!string.IsNullOrEmpty(request.Name))
                {
                    condition = condition.And(x => x.Name.Contains(request.Name));
                }

                if (!string.IsNullOrEmpty(request.Version))
                {
                    condition = condition.And(x => x.Version.Contains(request.Version));
                }

                if (!string.IsNullOrEmpty(request.MaterialType))
                {
                    condition = condition.And(x => x.MaterialType == request.MaterialType);
                }

                if (request.ReleaseStatus.HasValue)
                {
                    condition = condition.And(x => x.ReleaseStatus == request.ReleaseStatus.Value);
                }

                if (request.CreateTimeStart.HasValue)
                {
                    condition = condition.And(x => x.CTime >= request.CreateTimeStart.Value);
                }

                if (request.CreateTimeEnd.HasValue)
                {
                    condition = condition.And(x => x.CTime <= request.CreateTimeEnd.Value);
                }

                if (!string.IsNullOrEmpty(request.Keyword))
                {
                    condition = condition.And(x => x.MaterialCode.Contains(request.Keyword) ||
                                                  x.Name.Contains(request.Keyword) ||
                                                  x.MaterialDesc.Contains(request.Keyword));
                }

                // 获取数据
                var blueprints = _blueprintApp.GetAllList(condition).ToList();

                if (blueprints == null || blueprints.Count == 0)
                {
                    result.Message = "没有找到符合条件的数据";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 创建Excel数据
                var exportData = blueprints.Select(x => new
                {
                    物料编码 = x.MaterialCode,
                    图纸名称 = x.Name,
                    版本 = x.Version,
                    物料描述 = x.MaterialDesc,
                    物料类型 = x.MaterialType,
                    单位 = x.Unit,
                    工厂代码 = x.WERKS,
                    CA号 = x.AENNR,
                    释放状态 = x.ReleaseStatus == 1 ? "已释放" : "未释放",
                    释放时间 = x.ReleaseTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    发送状态 = x.SendStatus == 1 ? "已发送" : "未发送",
                    图纸类型 = x.BlueprintType,
                    图纸编号 = x.BlueprintCode,
                    图纸类别 = x.BlueprintCategory,
                    CR号 = x.CrCode,
                    生效日期 = x.EffectiveDate?.ToString("yyyy-MM-dd"),
                    文件名称 = x.Filename,
                    创建时间 = x.CTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    创建人 = x.CUser,
                    修改时间 = x.MTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    修改人 = x.MUser,
                    备注 = x.Remark
                }).ToList();

                // 生成Excel文件
                var fileName = $"图纸信息导出_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                using (var stream = ExcelService.ExportToExcel(exportData, ExcelFileType.xlsx, "图纸信息"))
                {
                    var fileBytes = stream.ToArray();

                    result.Data = new
                    {
                        FileName = fileName,
                        FileContent = Convert.ToBase64String(fileBytes),
                        TotalCount = blueprints.Count
                    };
                    result.Message = $"导出成功，共导出{blueprints.Count}条记录";
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        /// <summary>
        /// 导出图纸信息到Excel（文件下载方式）
        /// </summary>
        /// <param name="materialCode">物料编码</param>
        /// <param name="name">图纸名称</param>
        /// <param name="version">版本</param>
        /// <param name="materialType">物料类型</param>
        /// <param name="releaseStatus">释放状态</param>
        /// <param name="createTimeStart">创建时间开始</param>
        /// <param name="createTimeEnd">创建时间结束</param>
        /// <param name="keyword">关键字</param>
        /// <returns>Excel文件下载</returns>
        [HttpGet]
        public HttpResponseMessage ExportBlueprintsToExcelFile([FromUri]string materialCode = null,
            [FromUri]string name = null, [FromUri]string version = null, [FromUri]string materialType = null,
            [FromUri]int? releaseStatus = null, [FromUri]DateTime? createTimeStart = null,
            [FromUri]DateTime? createTimeEnd = null, [FromUri]string keyword = null)
        {
            try
            {
                // 构建查询条件
                Expression<Func<Plm_Blueprint, bool>> condition = PredicateBuilder.True<Plm_Blueprint>();
                condition = condition.And(x => x.IsDelete == false);

                // 添加查询条件
                if (!string.IsNullOrEmpty(materialCode))
                {
                    condition = condition.And(x => x.MaterialCode.Contains(materialCode));
                }

                if (!string.IsNullOrEmpty(name))
                {
                    condition = condition.And(x => x.Name.Contains(name));
                }

                if (!string.IsNullOrEmpty(version))
                {
                    condition = condition.And(x => x.Version.Contains(version));
                }

                if (!string.IsNullOrEmpty(materialType))
                {
                    condition = condition.And(x => x.MaterialType == materialType);
                }

                if (releaseStatus.HasValue)
                {
                    condition = condition.And(x => x.ReleaseStatus == releaseStatus.Value);
                }

                if (createTimeStart.HasValue)
                {
                    condition = condition.And(x => x.CTime >= createTimeStart.Value);
                }

                if (createTimeEnd.HasValue)
                {
                    condition = condition.And(x => x.CTime <= createTimeEnd.Value);
                }

                if (!string.IsNullOrEmpty(keyword))
                {
                    condition = condition.And(x => x.MaterialCode.Contains(keyword) ||
                                                  x.Name.Contains(keyword) ||
                                                  x.MaterialDesc.Contains(keyword));
                }

                // 获取数据
                var blueprints = _blueprintApp.GetList(condition).ToList();

                // 创建Excel数据
                var exportData = blueprints.Select(x => new
                {
                    物料编码 = x.MaterialCode,
                    图纸名称 = x.Name,
                    版本 = x.Version,
                    物料描述 = x.MaterialDesc,
                    物料类型 = x.MaterialType,
                    单位 = x.Unit,
                    工厂代码 = x.WERKS,
                    CA号 = x.AENNR,
                    释放状态 = x.ReleaseStatus == 1 ? "已释放" : "未释放",
                    释放时间 = x.ReleaseTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    发送状态 = x.SendStatus == 1 ? "已发送" : "未发送",
                    图纸类型 = x.BlueprintType,
                    图纸编号 = x.BlueprintCode,
                    CR号 = x.CrCode,
                    生效日期 = x.EffectiveDate?.ToString("yyyy-MM-dd"),
                    文件名称 = x.Filename,
                    创建时间 = x.CTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    创建人 = x.CUser,
                    修改时间 = x.MTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    修改人 = x.MUser,
                    备注 = x.Remark
                }).ToList();

                // 生成Excel文件
                var fileName = $"图纸信息导出_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                using (var stream = ExcelService.ExportToExcel(exportData, ExcelFileType.xlsx, "图纸信息"))
                {
                    var fileBytes = stream.ToArray();

                    // 创建HTTP响应
                    var response = new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = new ByteArrayContent(fileBytes)
                    };

                    response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
                    {
                        FileName = fileName
                    };
                    response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                    return response;
                }
            }
            catch (Exception ex)
            {
                var errorResponse = new HttpResponseMessage(HttpStatusCode.InternalServerError)
                {
                    Content = new StringContent($"导出失败: {ex.Message}", Encoding.UTF8, "text/plain")
                };
                return errorResponse;
            }
        }

        #endregion

        #region 撤回未释放图纸

        /// <summary>
        /// 撤回未释放图纸
        /// </summary>
        /// <param name="request">撤回请求参数</param>
        /// <returns>撤回结果</returns>
        [HttpPost]
        public IHttpActionResult RevokeUnreleased([FromBody] RevokeUnreleasedRequest request)
        {
            var result = new ResponseData();
            try
            {
                if (request == null || request.BlueprintIds == null || request.BlueprintIds.Count == 0)
                {
                    result.Message = "图纸ID列表不能为空";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                var currentUser = GetCurrentUser();
                if (currentUser == null)
                {
                    result.Message = "用户未登录";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 获取图纸信息
                var blueprints = _blueprintApp.GetListByKeys(request.BlueprintIds.ToArray());
                if (blueprints == null || blueprints.Count == 0)
                {
                    result.Message = "未找到对应的图纸记录";
                    result.Code = (int)WMSStatusCode.Failed;
                    return Json(result);
                }

                // 执行撤回操作：设置为未释放状态，标记为只能单条处理
                foreach (var blueprint in blueprints)
                {
                    blueprint.ReleaseStatus = 0; // 设置为未释放状态
                    blueprint.HandMovementSendType = 1; // 标记为只能单条处理，不支持批量释放
                    blueprint.MUser = currentUser.LoginAccount;
                    blueprint.MTime = DateTime.Now;
                    blueprint.Remark = $"撤回原因：{request.Reason}";
                }

                // 更新数据库
                _blueprintApp.Update(blueprints);

                // 发送邮件通知
                SendRevokeNotificationEmail(blueprints, request.Reason, currentUser);

                result.Data = new
                {
                    RevokedCount = blueprints.Count,
                    RevokedBlueprints = blueprints.Select(b => new
                    {
                        MaterialCode = b.MaterialCode,
                        Name = b.Name,
                        Version = b.Version,
                        WERKS = b.WERKS
                    }).ToList()
                };
                result.Message = $"成功撤回{blueprints.Count}个图纸";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 验证用户是否有图纸未释放权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否有权限</returns>
        private bool HasBlueprintUnreleasedPermission(string userId)
        {
            try
            {
                // 通过角色名称"图纸未释放"查询权限
                var roleApp = new Sys_RoleApp();
                var userRoles = roleApp.GetUserRoles(userId);
                return userRoles.Any(role => role.RoleDesc == "图纸未释放");
            }
            catch (Exception ex)
            {
                // 记录日志
                Debug.WriteLine($"验证图纸未释放权限失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证用户是否有工厂权限
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="werks">工厂代码</param>
        /// <returns>是否有权限</returns>
        private bool HasFactoryPermission(string userId, string werks)
        {
            try
            {
                // 这里需要根据实际的工厂权限表来实现
                // 暂时返回true，实际应该查询用户工厂权限表
                // TODO: 实现具体的工厂权限验证逻辑
                return true;
            }
            catch (Exception ex)
            {
                // 记录日志
                Debug.WriteLine($"验证工厂权限失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送撤回通知邮件给有权限的用户
        /// 发送给：拥有"图纸未释放"角色 且 拥有对应工厂权限的用户
        /// </summary>
        /// <param name="blueprints">撤回的图纸列表</param>
        /// <param name="reason">撤回原因</param>
        /// <param name="currentUser">执行撤回操作的用户</param>
        private void SendRevokeNotificationEmail(List<Plm_Blueprint> blueprints, string reason, Sys_User currentUser)
        {
            try
            {
                // 获取需要接收撤回通知的用户列表（拥有"图纸未释放"角色且有对应工厂权限）
                var notificationUsers = GetBlueprintUnreleasedUsers(blueprints);

                if (notificationUsers.Count == 0)
                {
                    return;
                }

                // 构建邮件内容
                var emailSubject = "图纸撤回通知";
                var emailBody = BuildRevokeEmailBody(blueprints, reason, currentUser);

                // 发送邮件
                var mail = new Sys_Mail
                {
                    MailID = Guid.NewGuid().ToString(),
                    MailSubject = emailSubject,
                    MailBody = emailBody,
                    ReceiverMail = string.Join(";", notificationUsers.Select(u => u.Email).Where(e => !string.IsNullOrEmpty(e))),
                    SenderDisplayName = "SRM系统",
                    CTime = DateTime.Now,
                    CUser = currentUser.LoginAccount
                };

                string message;
                _mailApp.SendEmail(mail, out message);
            }
            catch (Exception ex)
            {
                // 记录日志，但不影响主流程
                Debug.WriteLine($"发送撤回通知邮件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取需要接收撤回通知邮件的用户列表
        /// 条件：拥有"图纸未释放"角色 且 拥有对应工厂权限
        /// </summary>
        /// <param name="blueprints">图纸列表</param>
        /// <returns>符合条件的用户列表</returns>
        private List<Sys_User> GetBlueprintUnreleasedUsers(List<Plm_Blueprint> blueprints)
        {
            try
            {
                var roleApp = new Sys_RoleApp();
                var userApp = new Sys_UserApp();

                // 获取"图纸未释放"角色
                var unreleasedRole = roleApp.GetList(r => r.RoleDesc == "图纸未释放" && r.IsDelete == false).ToList().FirstOrDefault();
                if (unreleasedRole == null)
                {
                    return new List<Sys_User>();
                }

                // 获取有该角色的用户
                var usersWithRole = userApp.DbContext.Queryable<Sys_User, Sys_UserRole>(
                    (u, ur) => new JoinQueryInfos(
                        JoinType.Inner,
                        u.UserID == ur.UserID && ur.RoleID == unreleasedRole.RoleID && ur.IsDelete == false
                    ))
                    .Where(u => u.IsDelete == false && u.IsEnable == true)
                    .Select(u => u)
                    .ToList();

                // 过滤有工厂权限的用户
                var result = new List<Sys_User>();
                var factoryCodes = blueprints.Select(b => b.WERKS).Distinct().ToList();

                foreach (var user in usersWithRole)
                {
                    // 检查用户是否有任一工厂的权限
                    if (factoryCodes.Any(werks => HasFactoryPermission(user.UserID, werks)))
                    {
                        result.Add(user);
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取图纸未释放用户列表失败: {ex.Message}");
                return new List<Sys_User>();
            }
        }

        /// <summary>
        /// 构建撤回邮件内容
        /// </summary>
        /// <param name="blueprints">撤回的图纸列表</param>
        /// <param name="reason">撤回原因</param>
        /// <param name="currentUser">当前用户</param>
        /// <returns>邮件内容</returns>
        private string BuildRevokeEmailBody(List<Plm_Blueprint> blueprints, string reason, Sys_User currentUser)
        {
            var sb = new StringBuilder();
            sb.AppendLine("尊敬的用户：");
            sb.AppendLine();
            sb.AppendLine($"用户 {currentUser.UserName}({currentUser.LoginAccount}) 撤回了以下图纸：");
            sb.AppendLine();

            foreach (var blueprint in blueprints)
            {
                sb.AppendLine($"- 物料编码：{blueprint.MaterialCode}，名称：{blueprint.Name}，版本：{blueprint.Version}，工厂：{blueprint.WERKS}");
            }

            sb.AppendLine();
            sb.AppendLine($"撤回原因：{reason}");
            sb.AppendLine($"撤回时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine();
            sb.AppendLine("请及时处理相关事务。");
            sb.AppendLine();
            sb.AppendLine("此邮件由SRM系统自动发送，请勿回复。");

            return sb.ToString();
        }

        #endregion

        #region 技术更改通知

        /// <summary>
        /// 发送技术更改通知
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns>发送结果</returns>
        [HttpPost]
        public IHttpActionResult SendTechnicalChangeNotification([FromBody] TechnicalChangeNotificationRequest request)
        {
            var result = new ResponseData();
            try
            {
                // 参数验证
                if (request == null)
                {
                    result.Code = (int)WMSStatusCode.ParameterError;
                    result.Message = "请求参数不能为空";
                    return Json(result);
                }

                var validationResult = request.Validate();
                if (!validationResult.IsValid)
                {
                    result.Code = (int)WMSStatusCode.ParameterError;
                    result.Message = validationResult.ErrorMessage;
                    return Json(result);
                }

                // 获取图纸信息
                var blueprints = _blueprintApp.GetListByKeys(request.BlueprintIds.ToArray());
                if (blueprints == null || blueprints.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "未找到相关图纸信息";
                    return Json(result);
                }

                // 按创建者邮箱分组
                var emailGroups = blueprints
                    .Where(b => !string.IsNullOrEmpty(b.CreateEmail))
                    .GroupBy(b => new { b.CreateEmail, b.CreateUserName })
                    .ToList();

                if (emailGroups.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "所选图纸均无创建者邮箱信息";
                    return Json(result);
                }

                var emailList = new List<Sys_Mail>();
                var currentUser = GetCurrentUser();

                // 为每个创建者生成邮件
                foreach (var group in emailGroups)
                {
                    var userBlueprints = group.ToList();
                    var blueprintDetails = string.Join("\n\t", userBlueprints.Select(b =>
                        $"• {b.Name}（物料编码：{b.MaterialCode}，版本：{b.Version}）"));

                    var mailBody = $"您好：\n\n" +
                                   $"您创建的以下图纸有技术更改通知：\n\t{blueprintDetails}\n\n" +
                                   $"更改内容：{request.ChangeContent}\n\n" +
                                   (!string.IsNullOrEmpty(request.ChangeReason) ? $"更改原因：{request.ChangeReason}\n\n" : "") +
                                   $"请及时关注相关变更。\n\n" +
                                   $"通知时间：{DateTime.Now:yyyy年MM月dd日 HH:mm}\n" +
                                   $"通知人：{currentUser.UserName}\n\n" +
                                   $"请前往西子富沃德SRM系统查看详细信息！";

                    var mail = new Sys_Mail
                    {
                        MailID = Guid.NewGuid().ToString(),
                        UserName = group.Key.CreateUserName ?? "图纸创建者",
                        MessageTypeDesc = "技术更改通知",
                        SenderDisplayName = "SRM系统",
                        ReceiverMail = group.Key.CreateEmail,
                        MailSubject = $"技术更改通知 - {userBlueprints.Count}个图纸",
                        MailBody = mailBody,
                        CUser = currentUser.LoginAccount,
                        CTime = DateTime.Now,
                        SendTime = DateTime.Now,
                        AutoSend = true,
                        IsDelete = false
                    };
                    emailList.Add(mail);
                }

                // 发送邮件
                var successCount = 0;
                var failedEmails = new List<string>();

                foreach (var mail in emailList)
                {
                    try
                    {
                        string message;
                        bool sendSuccess = _mailApp.SendEmail(mail, out message);

                        if (sendSuccess)
                        {
                            successCount++;
                        }
                        else
                        {
                            failedEmails.Add($"{mail.ReceiverMail}: {message}");
                        }
                    }
                    catch (Exception emailEx)
                    {
                        failedEmails.Add($"{mail.ReceiverMail}: {emailEx.Message}");
                    }
                }

                // 构建返回消息
                var successMessage = $"技术更改通知发送完成。成功发送 {successCount} 封邮件";
                if (failedEmails.Count > 0)
                {
                    successMessage += $"，失败 {failedEmails.Count} 封：{string.Join("；", failedEmails)}";
                }

                result.Code = (int)WMSStatusCode.Success;
                result.Message = successMessage;
                result.Data = new {
                    SuccessCount = successCount,
                    FailedCount = failedEmails.Count,
                    TotalBlueprints = blueprints.Count,
                    EmailGroups = emailGroups.Count
                };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion
    }
}