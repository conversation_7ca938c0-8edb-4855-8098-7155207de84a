using AOS.Core.Http;
using AOS.SRM.Application.SAP;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.SAP
{
    /// <summary>
    /// SAP中间库（通用）
    /// </summary>
    public class XZSAPController : ApiBaseController
    {
        #region 初始化

        private SAPApp xzsap_app = new SAPApp();

        #endregion

        #region 查询供应商账户组代码
        /// <summary>
        /// 查询供应商账户组代码
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetXZSAP_T077Y([FromUri]string keyword = "")
        {
            var result = new ResponseData();
            try
            {
                var data = xzsap_app.GetXZSAP_T077Y(keyword);
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 查询供应商国家代码
        /// <summary>
        /// 查询供应商国家代码
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetXZSAP_T005T([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var data = xzsap_app.GetXZSAP_T005T(keyword);
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 付款条件
        /// <summary>
        /// 查询采购付款条件
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetXZSAP_T052U([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var data = xzsap_app.GetXZSAP_T052U(keyword);
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 税码
        /// <summary>
        /// 税码
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetXZ_SAP_T007S([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var data = xzsap_app.GetXZ_SAP_T007S(keyword);
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 订货货币
        /// <summary>
        ///  订货货币
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetXZ_SAP_TCURT([FromUri]string keyword)
        {
            var result = new ResponseData();
            try
            {
                var data = xzsap_app.GetXZ_SAP_TCURT(keyword);
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}