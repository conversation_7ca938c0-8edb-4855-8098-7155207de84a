using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.Base;
using AOS.SRM.Application.FO;
using AOS.SRM.Entity.FO;
using AOS.SRM.Entity.FO.Dto;
using AOS.SRM.Entity.FO.ViewModel;
using AOS.SRM.Entity.Sys;
using AOS.SRM.Entity.WMS.req;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace AOS.SRM.WebAPI.Areas
{
    /// <summary>
    /// 财务对账
    /// </summary>
    public class FinancialReconciliationController : ApiBaseController
    {

        private I_InvoiceApp _mainApp = new I_InvoiceApp();
        private I_InvoiceDetailApp _detailApp = new I_InvoiceDetailApp();
        private I_InvoiceDisposalApp _disposalApp = new I_InvoiceDisposalApp();
        private P_ConsignmentNoteApp _noteApp = new P_ConsignmentNoteApp();

        /// <summary>
        /// 生成对账单号
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.IN, DocFixedNumDef.InvoiceNo);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #region 开票申请查询
        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyName"></param>
        /// <param name="BillingNo">开票单号</param>
        /// <param name="Status"></param>
        /// <param name="InvoiceNo">发票单号</param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="DocType">单据类型</param>
        /// <param name="CompanyCode">单据类型</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri]Pagination page, [FromUri]string SupplyName, [FromUri] string BillingNo, [FromUri]string Status, [FromUri]string InvoiceNo, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime, [FromUri]string DocType, [FromUri]string CompanyCode)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _mainApp.GetPageList(page, t => t.IsDelete == false
                 && (string.IsNullOrEmpty(SupplyName) || t.SupplyName.Contains(SupplyName))
                 && (string.IsNullOrEmpty(BillingNo) || t.BillingNo.Contains(BillingNo))
                 && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                 && (string.IsNullOrEmpty(InvoiceNo) || t.InvoiceNo.Contains(InvoiceNo))
                 && (string.IsNullOrEmpty(CompanyCode) || t.CompanyCode.Contains(CompanyCode))
                 && t.CTime >= StartTime && t.CTime < EndTime.AddDays(1)
                 && t.DocType == DocType
                ).OrderBy(t => t.Status).ThenByDescending(t => t.CTime).ToList();
                page.Total = itemsData.Count();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 获取收货明细
        /// <summary>
        /// 获取收货明细
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="OrderNo"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="OrderType">1:标准 2：委外</param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="page"></param>
        /// <param name="status">结算单价是否为0?   1：是 0：否</param>
        /// <param name="InspectionNo"></param>
        /// <param name="CompanyCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetReceivingDetails([FromUri]Pagination page, [FromUri] string SupplyCode, [FromUri] string InspectionNo, 
            [FromUri] string OrderNo, [FromUri] string MaterialCode, [FromUri] string OrderType, [FromUri] DateTime StartTime, [FromUri] DateTime EndTime, 
            [FromUri] string status, [FromUri] string CompanyCode,[FromUri] bool? checkFlag)
        {
            var result = new ResponseData();
            string message = "";
            bool isPosted;
            try
            {
                Sys_User userInfo = GetCurrentUser();
                var itemsData = _detailApp.GetReceivingDetails(page, SupplyCode, InspectionNo, OrderNo, MaterialCode, 
                    OrderType, StartTime, EndTime, status, CompanyCode, userInfo.LoginAccount,userInfo.IsSupplier.HasValue ? userInfo.IsSupplier.Value : false, 
                    checkFlag,out isPosted, out message);
                if (!isPosted)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = message;
                    return Json(result);
                }

                result.Data = new { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
        
        #region 收货明细导出
        /// <summary>
        ///  开票申请明细导出
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ReceivingDetailExport([FromUri]Pagination page, [FromUri] string SupplyCode, [FromUri] string InspectionNo, 
            [FromUri] string OrderNo, [FromUri] string MaterialCode, [FromUri] string OrderType, [FromUri] DateTime StartTime, [FromUri] DateTime EndTime, 
            [FromUri] string status, [FromUri] string CompanyCode,[FromUri] bool? checkFlag)
        {
            var result = new ResponseData();
            try
            {
                page.PageSize = 10000;
                Sys_User userInfo = GetCurrentUser();
                var itemsData = _detailApp.GetReceivingDetails(null, SupplyCode, InspectionNo, OrderNo, MaterialCode, OrderType, 
                    StartTime, EndTime, status, CompanyCode, userInfo.LoginAccount,userInfo.IsSupplier.HasValue ? userInfo.IsSupplier.Value : false, 
                    checkFlag, out bool isPosted, out string message);
                foreach (var item in itemsData)
                {
                    if (item.OrderType == "1")
                    {
                        item.OrderType = "标准";
                    } else if (item.OrderType == "2") {
                        item.OrderType = "委外";
                    } else if (item.OrderType == "3") {
                        item.OrderType = "退货";
                    }
                }
                List<ExcelColumn<FO_ReceivingDetails_View>> columns = ExcelService.FetchDefaultColumnList<FO_ReceivingDetails_View>();
                string[] ignoreField = new string[] { "Id", "CTime", "BaseType", "StatusCode" };
                List<ExcelColumn<FO_ReceivingDetails_View>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<FO_ReceivingDetails_View> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile<FO_ReceivingDetails_View>(itemsData, columns,"收获明细导出");
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
        
        #region 排除校验退货
        /// <summary>
        /// 开票申请提交
        /// </summary>
        /// <param name="v_Invoice"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ExclusionVerificationReceiving([FromBody] ExclusionVerificationReceivingReq req)
        {
            var user = GetCurrentUser();
            return Json(_mainApp.ExclusionVerificationReceiving(req, user));
        }
        #endregion

        #region 开票申请提交
        /// <summary>
        /// 开票申请提交
        /// </summary>
        /// <param name="v_Invoice"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult InsertInvoice([FromBody] I_Invoice_Views v_Invoice)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;
                //校验是否存在重复项
                bool isExist = v_Invoice.Invoice.GroupBy(t => t.PurchaseReceiptID)
                    .Count(t => t.Count() > 1) >= 1;
                if (isExist)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "存在重复选择的行";
                    return Json(result);
                }
                bool isExist2 = v_Invoice.Invoice.Any(t => t.SettleUnitPrice == 0);
                if (isExist2)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "存在结算单价为0的数据，不能开票！";
                    return Json(result);
                }

                var list = _mainApp.GetList(t => t.InvoiceNo == v_Invoice.InvoiceNo).ToList();
                if (list.Count > 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "存在重复的发票号";
                    return Json(result);
                }
                _mainApp.SaveInvoiceInfo(v_Invoice, userCode);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 附件上传
        /// <summary>
        /// 附件上传
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UploadFile()
        {
            var result = new ResponseData();
            try
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Data = _mainApp.UploadFile(_currentUser.UserName);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 审核开票申请单
        /// <summary>
        /// 审核开票申请单
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult CheckInvoice([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                //审核
                int j = _mainApp.CheckInvoice(ids, GetCurrentUser().LoginAccount);
                //if (j > 0)
                //{
                //    #region 过账
                //    string message = "";
                //    List<InvoiceDto> lstDto = new List<InvoiceDto>();
                //    foreach (var item in ids)
                //    {
                //        #region 取数据源
                //        InvoiceDto dto = new InvoiceDto();

                //        var main = _mainApp.GetEntityByKey(item);
                //        var detail = _detailApp.GetList(t => t.ParentId == item).ToList();
                //        var disposal = _disposalApp.GetList(t => t.InvoiceId == item).ToList();
                //        dto.main = main;
                //        dto.detail = detail;
                //        dto.disposal = disposal;
                //        lstDto.Add(dto);
                //        #endregion
                //    }
                //    if (lstDto.Count == 0)
                //    {
                //        result.Code = (int)WMSStatusCode.Failed;
                //        result.Message = "没有可操作的数据";
                //        return Json(result);
                //    }
                //    //过账
                //    var opUser = GetCurrentUser().LoginAccount;
                //    var postTime = DateTime.Now;
                //    var bResult = _mainApp.DoPost(lstDto, postTime, opUser, out message);
                //    if (bResult)
                //    {
                //        result.Code = (int)WMSStatusCode.Success;
                //        result.Message = "过账成功";
                //    }
                //    else
                //    {
                //        result.Code = (int)WMSStatusCode.Failed;
                //        result.Message = message;
                //        return Json(result);
                //    }
                //    #endregion
                //}
                //else
                //{
                //    result.Message = "审核失败";
                //    result.Code = (int)WMSStatusCode.Failed;
                //    return Json(result);
                //}
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 开票申请过账
        // <summary>
        // 开票申请单过账（sap预制发票接口）
        // </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                List<string> ids = JsonConvert.DeserializeObject<List<string>>(jo["ids"].ToString());
                var postTime = Convert.ToDateTime(getValue(jo, "postTime"));

                string message = "";
                List<InvoiceDto> lstDto = new List<InvoiceDto>();
                foreach (var item in ids)
                {
                    #region 取数据源
                    InvoiceDto dto = new InvoiceDto();

                    var main = _mainApp.GetEntityByKey(item);
                    var detail = _detailApp.GetList(t => t.ParentId == item).ToList();
                    var disposal = _disposalApp.GetList(t => t.InvoiceId == item).ToList();
                    dto.main = main;
                    dto.detail = detail;
                    dto.disposal = disposal;
                    lstDto.Add(dto);
                    #endregion
                }
                if (lstDto.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "没有可操作的数据";
                    return Json(result);
                }
                //过账
                var opUser = GetCurrentUser().LoginAccount;
                var bResult = _mainApp.DoPost(lstDto, postTime, opUser, out message);
                if (bResult)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "过账成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 驳回开票申请单
        /// <summary>
        /// 驳回开票申请单
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult RejectInvoice([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;

                result.Data = _mainApp.RejectInvoice(ids, userCode);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 删除开票申请单
        /// <summary>
        /// 删除开票申请单（带批量）
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult DeleteInvoice([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var lstEntity = _mainApp.GetListByKeys(ids);
                bool bExist = lstEntity.Any(t => t.Status == "2" || t.Status == "3" || t.Status == "4");
                if (bExist)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "存在已审核或已驳回或已完成的单据";
                    return Json(result);
                }

                result.Data = _mainApp.DeleteInvoice(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 获取供应商处置单据
        /// <summary>
        /// 获取供应商处置单据信息（包括不合格处置单、让步接收单、奖励/返还单）
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode">供应商编码</param>
        /// <param name="DocType">处置单据类型</param>
        /// <param name="DisposalNo">处置单号</param>
        /// <param name="StartTime">开始日期</param>
        /// <param name="EndTime">结束日期</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSupplierDisposalnfo([FromUri] Pagination page, [FromUri]string SupplyCode, [FromUri]string DocType, [FromUri] string DisposalNo, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _mainApp.GetSupplierDisposalnfo(page, SupplyCode, DocType, DisposalNo, StartTime, EndTime);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 查看主表，开票明细，处置单据
        /// <summary>
        /// 
        /// </summary>
        /// <param name="Id">主键</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetInvoiceInfo([FromUri] string Id)
        {
            var result = new ResponseData();
            try
            {
                InvoiceDto dto = new InvoiceDto();

                var main = _mainApp.GetEntityByKey(Id);
                var detail = _detailApp.GetList(t => t.ParentId == Id).ToList();
                detail.ForEach(t =>
                {
                    t.Unit = (t.Unit == "ST" ? "PC" : t.Unit);
                });
                var disposal = _disposalApp.GetList(t => t.InvoiceId == Id).ToList();
                dto.main = main;
                dto.detail = detail.OrderBy(t => t.InspectionNo).ThenBy(t => t.InspectionLine).ToList();
                dto.disposal = disposal;

                result.Data = dto;
                return Json(result);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(new object());
        }
        #endregion

        #region 弹框：物流开票明细
        /// <summary>
        /// 弹框：物流开票明细
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyName"></param>
        /// <param name="LogisticsNo">物流单号</param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetLogisInvoiceDetailInfo([FromUri] Pagination page, [FromUri]string LogisticsNo, [FromUri]string SupplyName, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                //已过帐，未开票
                var data = _noteApp.GetPageList(page, t =>
                 (string.IsNullOrEmpty(LogisticsNo) || t.LogisticsNo.Contains(LogisticsNo))
                 && (string.IsNullOrEmpty(SupplyName) || t.SupplyName.Contains(SupplyName))
                 && t.CTime >= StartTime && t.CTime < EndTime.AddDays(1)
                 && string.IsNullOrEmpty(t.InvoiceId)
                && t.IsPosted == true
                ).ToList();

                result.Data = new ResponsePageData { items = data, total = page.Total };
                return Json(result);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(new object());
        }
        #endregion

        #region 物流开票申请提交
        /// <summary>
        /// 物流开票申请提交
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult InsertLogisticsInvoice([FromBody] LogisticsInvoiceDto dto)
        {
            var result = new ResponseData();
            try
            {
                _mainApp.InsertLogisticsInvoice(dto, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 查看物流开票申请主表，开票明细，处置单据
        /// <summary>
        /// 
        /// </summary>
        /// <param name="Id">主键</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetLogisticsInvoiceInfo([FromUri] string Id)
        {
            var result = new ResponseData();
            try
            {
                LogisticsInvoiceDto dto = new LogisticsInvoiceDto();

                var main = _mainApp.GetEntityByKey(Id);
                var logisticsDetail = _noteApp.GetList(t => t.InvoiceId == Id).ToList();
                var disposal = _disposalApp.GetList(t => t.InvoiceId == Id).ToList();
                dto.main = main;
                dto.LogisticsDetail = logisticsDetail;
                dto.disposal = disposal;

                result.Data = dto;
                return Json(result);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(new object());
        }
        #endregion

        #region 物流开票申请过账
        // <summary>
        // 物流开票申请单过账（sap预制发票接口）
        // </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult LogisticsDoPost([FromUri]string[] Ids)
        {
            var result = new ResponseData();
            try
            {
                string message = "";
                List<LogisticsInvoiceDto> lstDto = new List<LogisticsInvoiceDto>();
                foreach (var item in Ids)
                {
                    #region 取数据源
                    LogisticsInvoiceDto dto = new LogisticsInvoiceDto();

                    var main = _mainApp.GetEntityByKey(item);
                    var detail = _noteApp.GetList(t => t.InvoiceId == item).ToList();
                    var disposal = _disposalApp.GetList(t => t.InvoiceId == item).ToList();
                    dto.main = main;
                    dto.LogisticsDetail = detail;
                    dto.disposal = disposal;
                    lstDto.Add(dto);
                    #endregion
                }
                if (lstDto.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "没有可操作的数据";
                    return Json(result);
                }
                //过账
                var opUser = GetCurrentUser().LoginAccount;
                var postTime = DateTime.Now;
                var bResult = _mainApp.LogisticsDoPost(lstDto, postTime, opUser, out message);
                if (bResult)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "过账成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 物流开票申请审核
        /// <summary>
        /// 审核开票申请单
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        //
        public IHttpActionResult CheckLogisticsInvoice([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                //审核
                int j = _mainApp.CheckInvoice(ids, GetCurrentUser().LoginAccount);
                //if (j > 0)
                //{
                //    #region 过账
                //    string message = "";
                //    List<LogisticsInvoiceDto> lstDto = new List<LogisticsInvoiceDto>();
                //    foreach (var item in ids)
                //    {
                //        #region 取数据源
                //        LogisticsInvoiceDto dto = new LogisticsInvoiceDto();

                //        var main = _mainApp.GetEntityByKey(item);
                //        var detail = _noteApp.GetList(t => t.InvoiceId == item).ToList();
                //        var disposal = _disposalApp.GetList(t => t.InvoiceId == item).ToList();
                //        dto.main = main;
                //        dto.LogisticsDetail = detail;
                //        dto.disposal = disposal;
                //        lstDto.Add(dto);
                //        #endregion
                //    }
                //    if (lstDto.Count == 0)
                //    {
                //        result.Code = (int)WMSStatusCode.Failed;
                //        result.Message = "没有可操作的数据";
                //        return Json(result);
                //    }
                //    //过账
                //    var opUser = GetCurrentUser().LoginAccount;
                //    var postTime = DateTime.Now;
                //    var bResult = _mainApp.LogisticsDoPost(lstDto, postTime, opUser, out message);
                //    if (bResult)
                //    {
                //        result.Code = (int)WMSStatusCode.Success;
                //        result.Message = "过账成功";
                //    }
                //    else
                //    {
                //        result.Code = (int)WMSStatusCode.Failed;
                //        result.Message = message;
                //    }
                //    #endregion
                //}
                //else
                //{
                //    result.Message = "审核失败";
                //    result.Code = (int)WMSStatusCode.Failed;
                //}
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 驳回物流开票申请
        /// <summary>
        /// 驳回物流开票申请单
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult RejectLogisticsInvoice([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _mainApp.RejectLogisticsInvoice(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 删除物流开票申请单
        /// <summary>
        /// 删除物流开票申请单（带批量）
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult DeleteLogisticsInvoice([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var lstEntity = _mainApp.GetListByKeys(ids);
                bool bExist = lstEntity.Any(t => t.Status == "2" || t.Status == "3" || t.Status == "4");
                if (bExist)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "存在已审核或已驳回或已完成的单据";
                    return Json(result);
                }

                result.Data = _mainApp.DeleteLogisticsInvoice(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 发票冲销过账
        /// <summary>
        /// 发票冲销过账
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        //public IHttpActionResult RevDoPost(string[] Ids,string revType, DateTime postTime)
        public IHttpActionResult RevDoPost(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                List<string> ids = JsonConvert.DeserializeObject<List<string>>(jo["ids"].ToString());
                var revType = getValue(jo, "revType"); //冲销类型
                var postTime = Convert.ToDateTime(getValue(jo, "postTime"));
                var credentialType = getValue(jo, "credentialType"); //凭证类型

                string message = "";
                var lstEntity = _mainApp.GetListByKeys(ids.ToArray());

                if (lstEntity.Count == 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "没有可操作的数据";
                    return Json(result);
                }
                //过账
                var opUser = GetCurrentUser().LoginAccount;
                var bResult = _mainApp.RevDoPost(lstEntity, revType, postTime, opUser, credentialType, out message);
                if (bResult)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "过账成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 开票申请明细导出
        /// <summary>
        ///  开票申请明细导出
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult InvoiceDetailExport(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var ids = JsonConvert.DeserializeObject<List<string>>(jo["ids"].ToString());
                var itemsData = _detailApp.GetAllExportData(ids.ToArray());
                List<ExcelColumn<V_FO_InvoiceDetailExport>> columns = ExcelService.FetchDefaultColumnList<V_FO_InvoiceDetailExport>();
                string[] ignoreField = new string[] { "Id" };

                List<ExcelColumn<V_FO_InvoiceDetailExport>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<V_FO_InvoiceDetailExport> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile<V_FO_InvoiceDetailExport>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 补录发票
        /// <summary>
        /// 发票冲销过账
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult FillInvoiceNo(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var userInfo = GetCurrentUser();

                var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "id"));
                var id = ids.FirstOrDefault();
                var invoiceNo = getValue(jo, "invoiceNo");
                var InvoiceTime = getValue(jo, "InvoiceTime");

                var entity = _mainApp.GetEntityByKey(id);
                if (entity == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "没有可操作的数据";
                    return Json(result);
                }
                entity.InvoiceNo = invoiceNo;
                entity.InvoiceTime = Convert.ToDateTime(InvoiceTime);
                entity.MUser = userInfo.LoginAccount;
                entity.MTime = DateTime.Now;
                _mainApp.Update(entity);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出选择的收货记录
        /// <summary>
        ///  导出选择的收货记录
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ExportStorageRecord(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var storgeRecord = JsonConvert.DeserializeObject<List<I_InvoiceDetails>>(jo["storgeRecord"].ToString());
                List<ExcelColumn<I_InvoiceDetails>> columns = ExcelService.FetchDefaultColumnList<I_InvoiceDetails>();
                string[] ignoreField = new string[] { "Id", "ParentId", "PurchaseReceiptID", "SapDocNum", "SapLine" , "OrderType",
                "Remark","IsDelete","CUser","CTime","MUser","MTime","DUser","DTime"};

                List<ExcelColumn<I_InvoiceDetails>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<I_InvoiceDetails> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile<I_InvoiceDetails>(storgeRecord, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

    }
}
