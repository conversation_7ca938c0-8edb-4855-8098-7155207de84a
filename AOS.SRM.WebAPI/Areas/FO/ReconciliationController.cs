using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application;
using AOS.SRM.Application.FO;
using AOS.SRM.Application.SHM;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.FO;
using AOS.SRM.Entity.FO.Dto;
using AOS.SRM.Entity.PXC;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.FO
{
    /// <summary>
    /// 
    /// </summary>
    public class ReconciliationController : ApiBaseController
    {
        I_ReconciliationApp _app = new I_ReconciliationApp();
        private Sys_MailApp _mailApp = new Sys_MailApp();
        I_ReconciliationDetailApp _detailApp = new I_ReconciliationDetailApp();
        I_ReturnNoApp _returnNoApp = new I_ReturnNoApp();
        private SupplierInfoApp _supplierApp = new SupplierInfoApp();
        private MD_AttachmentManagementApp attachmentManagementapp = new MD_AttachmentManagementApp();
        private string isSend = ConfigurationManager.AppSettings["IsSend"]?.ToString();

        #region 对账单

        #region 对账单列表查询
        /// <summary>
        /// 对账单列表查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="Status"></param>
        /// <param name="DocType"></param>
        /// <param name="ReconciliationNo"></param>
        /// <param name="SupplyName"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetReconciliation([FromUri] Pagination page, [FromUri]string Status, [FromUri] string DocType, [FromUri]string ReconciliationNo, [FromUri]string SupplyName, [FromUri] DateTime StartTime, [FromUri]DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetReconciliation(page, ReconciliationNo, SupplyName, StartTime, EndTime, DocType, Status);
                //var Details = _app.GetSupplyInfo(SupplyName);
                result.Data = new { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 获取收件人
        /// <summary>
        /// 获取收件人
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetReceiver()
        {
            var result = new ResponseData();
            try
            {
                //获取财务应付会计
                var list = _app.GetReceiver();
                result.Data = new { list };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 提交对账单
        /// <summary>
        /// 提交对账单
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult CreateReconciliation([FromBody]I_Reconciliation entity)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                if (string.IsNullOrEmpty(entity.ReconciliationNo) || _app.Any(t => t.ReconciliationNo == entity.ReconciliationNo))
                {
                    entity.ReconciliationNo = DateTime.Now.ToString("yyyyMMddHHmmss");
                }
                _app.CreateReconciliation(entity, loginInfo.UserName);

                #region 邮件发送
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == true)
                    {
                        var message = "";
                        var typeDesc = "财务对账申请通知";
                        if (string.IsNullOrEmpty(entity.Email))
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            string msg = "邮件发送失败：收件人邮箱不能为空";
                            result.Message = msg;
                            return Json(result);
                        }

                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = loginInfo.SupplyName;
                        mail.ReceiverMail = entity.Email;
                        mail.MailSubject = typeDesc;
                        mail.MailBody = "您好：" + "\n" + "\t" +
                            "对账单(" + entity.ReconciliationNo + ")已下达，请前往西子富沃德SRM系统确认！";
                        mail.CUser = loginInfo.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 修改对账单
        /// <summary>
        /// 修改对账单
        /// </summary>
        /// <param name="Reconciliation"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult UpdateReconciliation([FromUri]I_Reconciliation Reconciliation, [FromUri] string id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.UpdateReconciliation(Reconciliation, id);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 接收
        /// <summary>
        /// 接收
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult UpdateStatue([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.UpdateStatue(ids);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 取消制作对账单(删除)
        /// <summary>
        /// 取消制作对账单
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult CancelStatue([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.CancelStatue(ids);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 对账单驳回
        /// <summary>
        /// 对账单驳回
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ReconciliationRejected([FromUri]string[] ids, [FromUri] string errMsg)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                result.Data = _app.ReconciliationRejected(ids, loginInfo.LoginAccount, errMsg);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出

        /// <summary>
        /// 对账单导出
        /// </summary>
        /// <param name="ReconciliationNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <param name="DocType"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile(string ReconciliationNo, string SupplyCode, string Status, DateTime StartTime, DateTime EndTime, string DocType)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetAllExportData(ReconciliationNo, SupplyCode, Status, StartTime, EndTime, DocType);
                List<ExcelColumn<I_Reconciliation>> columns = ExcelService.FetchDefaultColumnList<I_Reconciliation>();
                string[] ignoreField = null;
                if (DocType == "1")
                {
                    ignoreField = new string[] { "Id", "Status", "Path", "DocType", "ParentId", "FileName", "BookTotalAmount","SettleTotalAmount",
                        "SAPDocNum", "SAPMaterialNum","IsDelete","DUser","","DTime"};
                }

                else
                {
                    ignoreField = new string[] { "Id", "Status", "Path", "DocType", "ParentId", "FileName", "IsDelete", "DUser", "", "DTime" };
                }

                List<ExcelColumn<I_Reconciliation>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<I_Reconciliation> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        #region 生成对账单号
        /// <summary>
        /// 生成对账单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                result.Data = base.GenerateDocNum(DocType.FO, "");
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 物流对账-获取弹框物流信息
        /// <summary>
        /// 弹框获取物流信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode">供应商编号</param>
        /// <param name="LogisticsNo">物流单号</param>
        /// <param name="StartTime">开始时间</param>
        /// <param name="EndTime">结束时间</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetLogisticsInfo([FromUri]Pagination page, [FromUri]string LogisticsNo, [FromUri]string SupplyCode, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                var data = _detailApp.GetLogisticsOrderInfo(page, LogisticsNo, SupplyCode, StartTime, EndTime);
                result.Data = new { total = page.Total, items = data };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);

        }
        #endregion

        #region 物流对账-提交物流对账单
        /// <summary>
        /// 提交物流对账单
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult CreateLogisticsReconciliation([FromBody]LogisticsReconciliationDto dto)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                if (string.IsNullOrEmpty(dto.main.ReconciliationNo) || _app.Any(t => t.ReconciliationNo == dto.main.ReconciliationNo))
                {
                    dto.main.ReconciliationNo = DateTime.Now.ToString("yyyyMMddHHmmss");
                }
                _app.CreateLogisticsReconciliation(dto, loginInfo.LoginAccount);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == true)
                    {
                        var message = "";
                        var typeDesc = "财务对账申请通知";
                        var main = dto.main;
                        if (string.IsNullOrEmpty(main.Email))
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            string msg = "邮件发送失败：收件人邮箱不能为空";
                            result.Message = msg;
                            return Json(result);
                        }

                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = loginInfo.SupplyName;
                        mail.ReceiverMail = main.Email;
                        mail.MailSubject = typeDesc;
                        mail.MailBody = "您好：" + "\n" + "\t" +
                            "对账单(" + main.ReconciliationNo + ")已下达，请前往西子富沃德SRM系统确认！";
                        mail.CUser = loginInfo.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 修改物流对账单
        /// <summary>
        /// 修改对账单
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateLogisticsReconciliation([FromBody]LogisticsReconciliationDto dto)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;

                _app.UpdateLogisticsReconciliation(dto, userCode);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 取消物流对账单
        /// <summary>
        /// 取消物流对账单
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult CancelLogisticsStatue([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;

                result.Data = _app.CancelLogisticsStatue(ids, userCode);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 物流对账-过账
        /// <summary>
        /// 物流对账过账
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult LogisticsDoPost([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;
                var postTime = DateTime.Now;
                string message = "";
                var bResult = _app.DoPost(ids, userCode, postTime, out message);
                if (bResult)
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = "过账成功";
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 物流对账-查看物流对账明细
        /// <summary>
        /// 获取物流对账明细
        /// </summary>
        /// <param name="ParentId"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetLogisticsReconciliationDetails([FromUri]string ParentId)
        {
            var result = new ResponseData();
            try
            {
                var data = _detailApp.GetList(t => t.ParentId == ParentId).ToList();
                result.Data = data;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 获取物流对账接收人
        /// <summary>
        /// 获取收件人
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetLogisticsReceiver()
        {
            var result = new ResponseData();
            try
            {
                //获取财务应付会计
                var list = _app.GetLogisticsReceiver();
                result.Data = new { list };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 物流对账导出

        /// <summary>
        /// 物流对账导出
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult LogisticsExportToExcelFile([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetLogisticsExportData(ids);
                List<ExcelColumn<PXC_ConsignmentNote_Export>> columns = ExcelService.FetchDefaultColumnList<PXC_ConsignmentNote_Export>();
                string[] ignoreField = new string[] {
                    "LogisticsNo","LogisticsSupplierCode","Status"
                };

                List<ExcelColumn<PXC_ConsignmentNote_Export>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<PXC_ConsignmentNote_Export> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "States")
                //    {
                //        column.Formattor = ExcelExportFormatter.IsOrNoFormatter;
                //    }
                //});
                return ExportToExcelFile<PXC_ConsignmentNote_Export>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        #endregion

        #region 返还单

        #region 返还单列表查询
        /// <summary>
        /// 返还单列表查询
        /// </summary>
        /// <param name="page"></param>
        /// <param name="ReturnNo"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetReturnNoteList([FromUri]Pagination page, [FromUri]string ReturnNo, [FromUri]string SupplyCode, [FromUri]DateTime StartTime, [FromUri] DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _returnNoApp.GetReturnNoteList(page, ReturnNo, SupplyCode, StartTime, EndTime);
                result.Data = new ResponsePageData { items = itemsData, total = page.Total };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 生成返还单号
        /// <summary>
        /// 生成返还单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetReturnNo()
        {
            var result = new ResponseData();
            try
            {
                var Time = DateTime.Now;
                var ReturnNo = base.GenerateDocNum(DocType.RO, DocFixedNumDef.ReturnNo);
                result.Data = new { Time, ReturnNo };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 制作返还单
        /// <summary>
        /// 制作返还单
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        //public IHttpActionResult CreateReturnNote([FromBody]Returnno_view Invoice)
        public IHttpActionResult CreateReturnNote([FromBody]I_ReturnNote entity)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                if (string.IsNullOrEmpty(entity.ReturnNo) || _returnNoApp.Any(t => t.ReturnNo == entity.ReturnNo))
                {
                    entity.ReturnNo = "R" + DateTime.Now.ToString("yyyyMMddHHmmss");
                }
                _returnNoApp.CreateReturnNote(entity, loginInfo.LoginAccount, loginInfo.UserName);
                //更新对账单状态为已完成
                var recData = _app.GetFirstEntity(t => t.ReconciliationNo == entity.ReconciliationNo);
                recData.Status = 4;
                recData.MUser = loginInfo.LoginAccount;
                _app.Update(recData);

                #region 邮件发送
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == false)
                    {
                        var message = "";
                        var typeDesc = "财务对账返还单通知";
                        var supplierInfo = _supplierApp.GetFirstEntityByFieldValue("SupplyerCode", entity.SupplyCode);
                        if (supplierInfo != null && !string.IsNullOrEmpty(supplierInfo.EMail))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = loginInfo.UserName;
                            mail.ReceiverMail = supplierInfo.EMail;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "返还单(" + entity.ReturnNo + ")已下达，请前往西子富沃德SRM系统确认！";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 对账单上传
        /// <summary>
        /// 对账单上传
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="SupplyName"></param>
        /// <param name="TypeOrder"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Upload([FromUri] string SupplyCode, [FromUri] string SupplyName, [FromUri]string TypeOrder)
        {
            var result = new ResponseData();

            try
            {
                string typecode = "Reconciliation";
                string typename = "对账表";
                string newid = System.Guid.NewGuid().ToString();
                string MenuType = "财务对账-对账单管理";

                //获取参数信息
                HttpContextBase context = (HttpContextBase)Request.Properties["MS_HttpContext"];
                HttpRequestBase request = context.Request;
                string fileName = request.Params["TempleteFile"]?.ToString();
                string newFileName = DateTime.Now.ToString("yyyyMMddhhmmss").ToString() + Path.GetExtension(fileName);//重新命名（序列）
                string resPath = ConfigurationManager.AppSettings[typecode]?.ToString() + newFileName;
                string filePath = HttpContext.Current.Server.MapPath("~/") + resPath;
                //对名称进行重命名
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Headers.Add("FileName", HttpUtility.UrlEncode(fileName));
                request.Files[0].SaveAs(filePath);

                #region 存入附件列表
                //存入附件管理表
                MD_AttachmentManagement attachmentmanagement = new MD_AttachmentManagement();
                attachmentmanagement.Id = newid;
                attachmentmanagement.AttachTypeCode = typecode;
                attachmentmanagement.AttachType = typename;
                //attachmentmanagement.FileId = newid;
                attachmentmanagement.FileName = fileName;
                //attachmentmanagement.FileCodeName = resPath.Replace(Path.GetFileNameWithoutExtension(fileName), DateTime.Now.ToString("yyyyMMddhhmmss").ToString());
                attachmentmanagement.Address = resPath;
                attachmentmanagement.MenuType = MenuType;
                attachmentmanagement.SupplyCode = SupplyCode;
                attachmentmanagement.SupplyName = SupplyName;
                attachmentmanagement.TypeOrder = TypeOrder;
                attachmentManagementapp.Insert(attachmentmanagement);
                #endregion

                result.Data = new { FilePath = "" + resPath.Replace("\\", "/"), newid };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 修改返还单
        /// <summary>
        /// 修改返还单
        /// </summary>
        /// <param name="ReturnNote"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult UpdateReturnNo(I_ReturnNote ReturnNote)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _returnNoApp.UpdateReturnNo(ReturnNote);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 下发
        /// <summary>
        /// 下发
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Release([FromUri]string[] ids)
        {
            var result = new ResponseData();
            var userCode = GetCurrentUser().LoginAccount;
            try
            {
                //result.Data = _returnNoApp.DeleteFiles(ids);
                var lstEntity = _returnNoApp.GetListByKeys(ids);
                lstEntity.ForEach(t =>
                {
                    t.Status = 1;
                    t.MUser = userCode;
                    t.MTime = DateTime.Now;
                });
                _returnNoApp.UpdateWithTran(lstEntity);

                //邮件发送功能待开发

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 接收打印返还单
        /// <summary>
        /// 打印返还单
        /// </summary>
        /// <param name="docNums"></param>
        /// <param name="templateCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Print([FromUri]string[] docNums, [FromUri]string templateCode)
        {
            var result = new ResponseData();
            string[] ReturnNo = docNums;
            if (ReturnNo == null || ReturnNo.Length == 0 || string.IsNullOrEmpty(templateCode))
            {
                result.Code = (int)WMSStatusCode.Failed;
            }
            else
            {
                //MD_LabelTempleteApp _labelTemplateApp = new MD_LabelTempleteApp();
                //MD_LabelTemplete template = _labelTemplateApp.GetFirstEntity(x => x.Remark == templateCode);
                //if (template == null)
                //{
                //    result.Code = (int)WMSStatusCode.Failed;
                //}
                //else
                //{
                DevExpress.XtraReports.UI.XtraReport report =
                    DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;
                //switch (templateCode)
                //{
                //    case "PP_BarCodeReturn":
                //    case "PP_BarCode":
                //    case "MM_BarCode":
                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "@ReturnNo",
                    Type = typeof(string),
                    Value = ReturnNo[0]
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                //dataSource.Fill();
                return base.PrintToPDF(report);

                //default:
                //    break;


            }
            return Json(result);
        }
        #endregion

        #region 返还单-上传签章返还单
        /// <summary>
        /// 返还单-上传签章返还单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UploadReturn([FromUri] string id)
        {

            var loginInfo = GetCurrentUser();
            string attachType = "ReturnOrder2";
            string attachTypeName = "签章返还单";
            string MenuType = "财务对账-返还单管理";
            string newid = System.Guid.NewGuid().ToString();
            var result = new ResponseData();

            try
            {
                var data = _returnNoApp.GetEntityByKey(id);
                if (data == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "没有找到该条数据";
                    return Json(result);
                }

                //获取参数信息
                HttpContextBase context = (HttpContextBase)Request.Properties["MS_HttpContext"];
                HttpRequestBase request = context.Request;
                string fileName = request.Params["TempleteFile"]?.ToString();
                string newFileName = DateTime.Now.ToString("yyyyMMddhhmmss").ToString() + Path.GetExtension(fileName);//重新命名（序列）
                string resPath = ConfigurationManager.AppSettings[attachType]?.ToString() + newFileName;
                string filePath = HttpContext.Current.Server.MapPath("~/") + resPath;
                //对名称进行重命名
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Headers.Add("FileName", HttpUtility.UrlEncode(fileName));
                request.Files[0].SaveAs(filePath);

                #region 存入附件
                //存入附件管理表
                MD_AttachmentManagement attachmentmanagement = new MD_AttachmentManagement();
                attachmentmanagement.Id = newid;
                attachmentmanagement.AttachTypeCode = attachType;
                attachmentmanagement.AttachType = attachTypeName;
                //attachmentmanagement.FileId = newid;
                attachmentmanagement.FileName = fileName;
                //attachmentmanagement.FileCodeName = resPath.Replace(Path.GetFileNameWithoutExtension(fileName), DateTime.Now.ToString("yyyyMMddhhmmss").ToString());
                attachmentmanagement.Address = resPath;
                attachmentmanagement.MenuType = MenuType;
                attachmentmanagement.SupplyCode = data.SupplyCode;
                attachmentmanagement.SupplyName = data.SupplyName;
                //attachmentmanagement.TypeOrder = TypeOrder;
                attachmentManagementapp.Insert(attachmentmanagement);
                #endregion

                result.Data = new { FilePath = "" + resPath.Replace("\\", "/"), id, fileName };

                //更新单据
                string signPath = resPath.Replace("\\", "/");
                data.SignAttachId = newid;
                data.SignFileName = fileName;
                data.SignPath = signPath;
                data.Status = 3;
                data.MUser = loginInfo.LoginAccount;
                data.MTime = DateTime.Now;
                result.Data = _returnNoApp.Update(data);

                #region 邮件发送
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == true)
                    {
                        var message = "";
                        var typeDesc = "签章返还单上传通知";
                        var rec = _app.GetFirstEntity(t => t.ReconciliationNo == data.ReconciliationNo);
                        if (string.IsNullOrEmpty(rec.Email))
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            string msg = "邮件发送失败：收件人邮箱不能为空";
                            result.Message = msg;
                            return Json(result);
                        }

                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = loginInfo.SupplyName;
                        mail.ReceiverMail = rec.Email;
                        mail.MailSubject = typeDesc;
                        mail.MailBody = "您好：" + "\n" + "\t" +
                            "返还单号(" + data.ReturnNo + ")的签章返还单已上传，请前往西子富沃德SRM系统确认！";
                        mail.CUser = loginInfo.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 确认对账完成
        /// <summary>
        /// 修改状态为已确认(对账完成)
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult UpdateStatus([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;
                var userName = GetCurrentUser().UserName;

                result.Data = _returnNoApp.UpdateStatus(ids, userCode, userName);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 返还单导出
        /// <summary>
        /// 返还单导出
        /// </summary>
        /// <param name="Status"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportReturnNo(string Status, string SupplyCode, DateTime StartTime, DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _returnNoApp.GetReturnNoData(Status, SupplyCode, StartTime, EndTime);
                List<ExcelColumn<I_ReturnNote>> columns = ExcelService.FetchDefaultColumnList<I_ReturnNote>();
                string[] ignoreField = new string[] { };

                List<ExcelColumn<I_ReturnNote>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<I_ReturnNote> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        #region 删除
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult DeleteFiles([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _returnNoApp.DeleteFiles(ids);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #endregion

    }
}
