using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.Store;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;

namespace AOS.SRM.WebAPI.Areas.Store
{
    /// <summary>
    /// 供应商库存
    /// </summary>
    public class StoreSupplierController : ApiBaseController
    {
        private StoreSupplierApp _app = new StoreSupplierApp();
        private StoreSupplierDetailApp _appDetail = new StoreSupplierDetailApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] string PartNo,
            [FromUri] string SupplierCode)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime";
                var itemData = _app.GetPageList(page, x =>
                    (string.IsNullOrEmpty(PartNo) || x.PartNo.Contains(PartNo)) &&
                    (string.IsNullOrEmpty(SupplierCode) || x.SupplierCode.Contains(SupplierCode))).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 查询明细分页列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDetailPageList([FromUri] Pagination page, [FromUri] string supplierCode,
            [FromUri] string partNo)
        {
            var result = new ResponseData();
            try
            {
                page.Sort = "CTime";
                var itemData = _appDetail.GetPageList(page, x =>
                    (string.IsNullOrEmpty(supplierCode) || x.SupplierCode.Contains(supplierCode)) &&
                    (string.IsNullOrEmpty(partNo) || x.PartNo.Contains(partNo))).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 获取件号列表

        /// <summary>
        /// 查询明细分页列表
        /// </summary>
        /// <param name="supplierCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPartList([FromUri] string SupplierCode)
        {
            var result = new ResponseData();
            try
            {
                var itemData = _app.GetList(x =>
                        string.IsNullOrEmpty(SupplierCode) || x.SupplierCode.Contains(SupplierCode)).ToList()
                    .Select(t => t.PartNo)
                    .Distinct().ToList();
                result.Data = itemData;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 入库

        /// <summary>
        /// 入库
        /// </summary>
        /// <param name="storeSupplier"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Warehousing([FromBody] Store_SupplierDetail storeSupplierDetail)
        {
            var result = new ResponseData();
            try
            {
                return Json(_app.Warehousing(storeSupplierDetail, GetCurrentUser()));
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 创建

        /// <summary>
        /// 创建
        /// </summary>
        /// <param name="storeSupplier"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Create([FromBody] Store_Supplier storeSupplier)
        {
            var result = new ResponseData();
            try
            {
                var list = _app.DbContext.Queryable<Store_Supplier>().Where(t =>
                    t.PartNo == storeSupplier.PartNo && t.SupplierCode == storeSupplier.SupplierCode).ToList();
                if (list.Count > 0)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "物料不存在";
                    return Json(result);
                }
                storeSupplier.ID = Guid.NewGuid().ToString();
                storeSupplier.StoreNo = 0;
                _app.Insert(storeSupplier);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="PartNo"></param>
        /// <param name="SupplierCode"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string PartNo, [FromUri] string SupplierCode)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(x => (string.IsNullOrEmpty(PartNo) || x.PartNo.Contains(PartNo)) &&
                                                  (string.IsNullOrEmpty(SupplierCode) ||
                                                   x.SupplierCode.Contains(SupplierCode))).ToList();
                List<ExcelColumn<Store_Supplier>> columns = ExcelService.FetchDefaultColumnList<Store_Supplier>();
                string[] ignoreField = new string[] { };

                List<ExcelColumn<Store_Supplier>> ignoreFieldList =
                    columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<Store_Supplier> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<Store_Supplier>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="storeSupplier"></param>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody] String[] ids)
        {
            var result = new ResponseData();
            try
            {
                _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                var detailIds = _appDetail.GetList(t => ids.Contains(t.PID)).ToList().Select(t => t.ID).Distinct()
                    .ToArray();
                _appDetail.DeleteByKeys(detailIds, GetCurrentUser().LoginAccount);
                result.Data = "删除成功";
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }

            return Json(result);
        }

        #endregion
    }
}