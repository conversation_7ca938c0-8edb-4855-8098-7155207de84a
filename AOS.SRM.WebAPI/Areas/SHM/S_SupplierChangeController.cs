using AOS.Core.Http;
using AOS.SRM.Application.SHM;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.SHM.ViewModel;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 供应商信息变更
    /// </summary>
    public class S_SupplierChangeController : ApiBaseController
    {
        /// <summary>
        /// 
        /// </summary>
        public S_SupplierChangeApp _app = new S_SupplierChangeApp();
        private string isSend = ConfigurationManager.AppSettings["IsSend"]?.ToString();

        #region 供应商变更申请查询(分页)
        /// <summary>
        /// 供应商准入查询(分页)
        /// </summary>
        /// <param name="page">分页实体</param>
        /// <param name="keyword">模糊查询关键字</param>
        /// <param name="supplierName">供应商名称</param>
        /// <param name="dateTimes">查询日期区间</param>
        /// <param name="inOrOut">内部外部</param>
        /// <param name="states">准入状态</param>
        /// <param name="buyerCode">所属采购员</param>
        /// <param name="supplierType">供应商类型</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageListForChange([FromUri] Pagination page, [FromUri] string supplierType, [FromUri] DateTime[] dateTimes, [FromUri] string inOrOut, [FromUri] string states, [FromUri] string supplierName, [FromUri] string buyerCode, [FromUri] string keyword = null)
        {
            var result = new ResponseData();
            try
            {
                var itemDatas = _app.GetPageListForChange(page, keyword, supplierName, supplierType, dateTimes, inOrOut, states, buyerCode, _currentUser);
                result.Data = new ResponsePageData { total = page.Total, items = itemDatas };
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商信息变更申请
        /// <summary>
        /// 供应商信息变更申请
        /// </summary>
        /// <param name="data">新增实体</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierInfoChangeRequest([FromBody] ChangeSupplier data)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.SupplierInfoChangeRequest(data.entity, data.fileIds, _currentUser.LoginAccount);
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 供应商变更申请审核(同意)
        /// <summary>
        /// 供应商变更申请审核
        /// </summary>
        /// <param name="ids">id集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierChangeApproved([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string msg = string.Empty;
                if (!_app.SupplierChangeApproved(ids, _currentUser, out msg))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = msg;
                    return Json(result);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商信息变更申请驳回
        /// <summary>
        /// 供应商信息变更申请驳回
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierChangeReject(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "ids"));
                var rejectReason = getValue(jo, "rejectReason");

                string msg = string.Empty;
                if (!_app.SupplierChangeReject(ids.ToArray(), rejectReason, _currentUser, out msg))

                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = msg;
                    return Json(result);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商主数据信息更新
        /// <summary>
        /// 供应商信息更新
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierChangeUpdate([FromBody] ChangeSupplier data)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.SupplierChangeUpdate(data.entity, data.fileIds, _currentUser.LoginAccount);
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商变更申请删除
        /// <summary>
        /// 供应商变更申请删除
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierChangeDelete(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "ids"));
                result.Data = _app.SuppplierChangeDelete(ids.ToArray(),_currentUser.LoginAccount);
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #endregion
    }
}