using AOS.Core.Http;
using AOS.SRM.Application.SHM;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 审批流
    /// </summary>
    public class SupplierAuditController : ApiBaseController
    {
        SupplierAuditApp _app = new SupplierAuditApp();

        #region 供应商驳回审核
        /// <summary>
        /// 供应商驳回审核
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetAuditList(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var Id = getValue(jo, "Id");
                var data = _app.GetList(t => t.DocId.Equals(Id), "CTime asc").ToList();
                result.Data = data;
               
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}