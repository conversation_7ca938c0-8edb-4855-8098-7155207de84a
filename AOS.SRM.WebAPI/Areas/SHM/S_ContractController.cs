using AOS.Core.Extensions;
using AOS.Core.Http;
using AOS.SRM.Application.SHM;
using AOS.SRM.Entity.SHM;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 合同管理
    /// </summary>
    public class S_ContractController : ApiBaseController
    {
        S_ContractApp _app = new S_ContractApp();

        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList(JObject jo)
        {
            var result = new ResponseData();
            //try
            //{
            //    var page = JsonConvert.DeserializeObject<Pagination>(getValue(jo, "page"));
            //    var contractCode = getValue(jo, "contractCode");
            //    var supplierCode = getValue(jo, "supplierCode");
            //    var startTime = getValue(jo, "startTime").ToDateTime().Value;
            //    var endTime = getValue(jo, "endTime").ToDateTime().Value;

            //    var itemsData = _app.GetPageList(page,
            //        t => t.CTime >= startTime && t.CTime < endTime.AddDays(1)
            //        && (string.IsNullOrEmpty(supplierCode) || t.SupplierCode == supplierCode)
            //        && (string.IsNullOrEmpty(contractCode) || t.ContractCode == contractCode)).ToList();
            //    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            //}
            //catch (Exception ex)
            //{
            //    result.Code = (int)WMSStatusCode.UnHandledException;
            //    result.Message = ex.InnerException?.Message ?? ex.Message;
            //}
            return Json(result);
        }
        #endregion

        #region 新增

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var entity = JsonConvert.DeserializeObject<S_Contract>(getValue(jo, "entity"));
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                string msg = "";
                result.Data = _app.AddEntity(entity, fileIds, _currentUser, out msg);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var entity = JsonConvert.DeserializeObject<S_Contract>(getValue(jo, "entity"));
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                string msg = "";
                result.Data = _app.UpdateEntity(entity, fileIds, _currentUser, out msg);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetListByKeys(ids);
                data.ForEach(t =>
                {
                    t.DUser = _currentUser.LoginAccount;
                    t.DTime = DateTime.Now;
                    t.IsDelete = true;
                });

                result.Data = _app.Update(data);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供方盖章回传
        /// <summary>
        /// 供方盖章回传
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierUploadReturn([FromUri] string id)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetEntityByKey(id);
                if (data == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "没有找到该条数据";
                    return Json(result);
                }
                string msg = "";
                var flag = _app.UploadReturn(id, "供方已回传", _currentUser, out msg);
                if (!flag)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = msg;
                    return Json(result);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 采购盖章回传
        /// <summary>
        /// 供方盖章回传
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult PurChaseUploadReturn([FromUri] string id)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetEntityByKey(id);
                if (data == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "没有找到该条数据";
                    return Json(result);
                }
                string msg = "";
                var flag= _app.UploadReturn(id, "采购已回传", _currentUser, out msg);
                if (!flag)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = msg;
                    return Json(result);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}