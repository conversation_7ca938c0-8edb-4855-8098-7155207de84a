using AOS.Core.Extensions;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.SHM;
using AOS.SRM.Application.SHM.ViewModel;
using AOS.SRM.Entity.SHM;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 技术资料图纸发放
    /// </summary>
    public class S_DrawingDistributionRecyclingController: ApiBaseController
    {
        S_DrawingDistributionRecyclingApp _app = new S_DrawingDistributionRecyclingApp();

        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList(JObject jo)
        {
            var result = new ResponseData();
            //    try
            //    {
            //        var page = JsonConvert.DeserializeObject<Pagination>(getValue(jo, "page"));
            //        //var contractCode = getValue(jo, "contractCode");
            //        var supplierCode = getValue(jo, "supplierCode");
            //        var startTime = getValue(jo, "startTime").ToDateTime().Value;
            //        var endTime = getValue(jo, "endTime").ToDateTime().Value;

            //        var itemsData = _app.GetPageList(page,
            //            t => t.CTime >= startTime && t.CTime < endTime.AddDays(1)
            //            && (string.IsNullOrEmpty(supplierCode) || t.SupplierCodes.Contains(supplierCode))
            //            && (string.IsNullOrEmpty(supplierCode) || t.SupplierNames.Contains(supplierCode))).ToList();
            //        result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            //    }
            //    catch (Exception ex)
            //    {
            //        result.Code = (int)WMSStatusCode.UnHandledException;
            //        result.Message = ex.InnerException?.Message ?? ex.Message;
            //    }
            return Json(result);
        }
        #endregion

        #region 新增

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddEntity([FromBody] S_DrawingDistributionRecycling entity)
        {
            var result = new ResponseData();
            try
            {
                entity.CUser = _currentUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.IsDelete = false;
                entity.Status = "已提交";

                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateEntity([FromBody] S_DrawingDistributionRecycling entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = _currentUser.LoginAccount;
                entity.MTime = DateTime.Now;

                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetListByKeys(ids);
                data.ForEach(t =>
                {
                    t.DUser = _currentUser.LoginAccount;
                    t.DTime = DateTime.Now;
                    t.IsDelete = true;
                });

                result.Data = _app.Update(data);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody] List<S_DrawingDistributionRecycling> entitys)
        {
            var result = new ResponseData();
            try
            {
                if (_app.ImprotExcelToBaseData(entitys, _currentUser.LoginAccount))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="supplierCode"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string supplierCode,[FromUri]DateTime startTime,[FromUri] DateTime endTime)
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<S_DrawingDistributionRecycling>();
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[] { "IsDelete", "MUser", "MTime", "DUser", "DTime", "Id" };
                var itemsData = _app.GetList(t => t.CTime >= startTime && t.CTime < endTime.AddDays(1)
                    && (string.IsNullOrEmpty(supplierCode) || t.SupplierCodes.Contains(supplierCode))
                    && (string.IsNullOrEmpty(supplierCode) || t.SupplierNames.Contains(supplierCode))).ToList();
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<S_DrawingDistributionRecycling> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //需要格式转换的字段
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "CTime")
                //    {
                //        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                //    }
                //});
                return ExportToExcelFile<S_DrawingDistributionRecycling>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        #region 分配供应商

        /// <summary>
        /// 分配供应商
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DistributeSupplier(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "ids"));
                var lstSupplierDto = JsonConvert.DeserializeObject<List<SupplierDto>>(getValue(jo, "suppliers"));

                var supplierCodes = string.Join(",", lstSupplierDto.Select(t => t.SupplierCode).Distinct().ToArray());
                var supplierNames = string.Join(",", lstSupplierDto.Select(t => t.SupplierName).Distinct().ToArray());
                var lstEntity = _app.GetListByKeys(ids.ToArray());
                lstEntity.ForEach(t =>
                {
                    t.SupplierCodes = supplierCodes;
                    t.SupplierNames = supplierNames;
                    t.MUser = _currentUser.LoginAccount;
                    t.MTime = DateTime.Now;
                });


                result.Data = _app.Update(lstEntity);
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 发放
        /// <summary>
        /// 发放
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SendOut(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "ids"));

                string msg = "";
                _app.SendOut(ids.ToArray(),_currentUser,out msg);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }
        #endregion

        #region 回收
        /// <summary>
        /// 
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Recovery(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "ids"));

                string msg = "";
                _app.SendOut(ids.ToArray(), _currentUser, out msg);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }
        #endregion

        #region 审核

        /// <summary>
        /// 审核
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Audit([FromBody]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var entitys = _app.GetListByKeys(ids);
                entitys.ForEach(t =>
                {
                    t.Status = "已审核";
                    t.MUser = _currentUser.LoginAccount;
                    t.MTime = DateTime.Now;
                });

                result.Data = _app.Update(entitys);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

    }
}