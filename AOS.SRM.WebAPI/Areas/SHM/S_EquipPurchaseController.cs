using AOS.Core.Extensions;
using AOS.Core.Http;
using AOS.SRM.Application;
using AOS.SRM.Application.SHM;
using AOS.SRM.Entity.SHM;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 设备采购跟踪
    /// </summary>
    public class S_EquipPurchaseController : ApiBaseController
    {
        S_EquipPurchaseApp _app = new S_EquipPurchaseApp();

        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList(JObject jo)
        {
            var result = new ResponseData();
            //try
            //{
            //    var page = JsonConvert.DeserializeObject<Pagination>(getValue(jo, "page"));
            //    var contractCode = getValue(jo, "contractCode");
            //    var EPNO = getValue(jo, "EPNO");
            //    var startTime = getValue(jo, "startTime").ToDateTime().Value;
            //    var endTime = getValue(jo, "endTime").ToDateTime().Value;

            //    var itemsData = _app.GetPageList(page,
            //        t => t.CTime >= startTime && t.CTime < endTime.AddDays(1)
            //        && (string.IsNullOrEmpty(EPNO) || t.EPNO == EPNO)
            //        && (string.IsNullOrEmpty(contractCode) || t.ContractCode == contractCode)).ToList();
            //    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            //}
            //catch (Exception ex)
            //{
            //    result.Code = (int)WMSStatusCode.UnHandledException;
            //    result.Message = ex.InnerException?.Message ?? ex.Message;
            //}
            return Json(result);
        }
        #endregion

        #region 生成单号
        /// <summary>
        /// 生成单号
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDocNum()
        {
            var result = new ResponseData();
            try
            {
                string docNum = "";
            num:
                docNum = base.GenerateDocNum(DocType.LX, DocFixedNumDef.EquipPurchase);
                if (string.IsNullOrEmpty(docNum)) { goto num; }
                result.Data = docNum;
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 新增

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var entity = JsonConvert.DeserializeObject<S_EquipPurchase>(getValue(jo, "entity"));
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                string msg = "";
                var flag=_app.AddEntity(entity, fileIds, _currentUser, out msg);
                if (!flag)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = msg;
                    return Json(result);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var entity = JsonConvert.DeserializeObject<S_EquipPurchase>(getValue(jo, "entity"));
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                string msg = "";
                var flag = _app.UpdateEntity(entity, fileIds, _currentUser, out msg);
                if (!flag)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = msg;
                    return Json(result);
                }

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetListByKeys(ids);
                data.ForEach(t =>
                {
                    t.DUser = _currentUser.LoginAccount;
                    t.DTime = DateTime.Now;
                    t.IsDelete = true;
                });

                result.Data = _app.Update(data);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 指派人
        /// <summary>
        /// 指派人
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AssignUser(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "ids"));
                var userCode = getValue(jo, "userCode");

                _app.AssignUser(ids, userCode, _currentUser);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新附件信息
        /// <summary>
        /// 更新附件信息
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateAttachInfo(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var id = getValue(jo, "id");
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                _app.UpdateAttachInfo(id, fileIds, _currentUser);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}