using AOS.Core.Extensions;
using AOS.Core.Http;
using AOS.SRM.Application.SHM;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 供应商警告函
    /// </summary>
    public class S_SupplierWarningLetterController : ApiBaseController
    {
        S_SupplierWarningLetterApp _app = new S_SupplierWarningLetterApp();


        #region 查询
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList(JObject jo)
        {
            var result = new ResponseData();
            //try
            //{
            //    var docNum = getValue(jo, "docNum");
            //    var supplierCode = getValue(jo, "supplierCode");
            //    var pageNumber = getValue(jo, "PageNumber");
            //    var pageSize = getValue(jo, "PageSize");

            //    Pagination page = new Pagination();
            //    page.PageNumber = pageNumber.ToInt().Value;
            //    page.PageSize = pageSize.ToInt().Value;

            //    var itemsData = _app.GetPageList(page,
            //        t => (string.IsNullOrEmpty(docNum) || t.DocNum.Contains(docNum))
            //        && (string.IsNullOrEmpty(supplierCode) || t.SupplierCode == supplierCode)
            //        ).ToList();
            //    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            //}
            //catch (Exception ex)
            //{
            //    result.Code = (int)WMSStatusCode.UnHandledException;
            //    result.Message = ex.InnerException?.Message ?? ex.Message;
            //}
            return Json(result);
        }
        #endregion

        #region 接收并查看
        /// <summary>
        /// 接收并查看
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateEntity(JObject jo)
        {
            var result = new ResponseData();
  
                var id = getValue(jo, "id");
                var templateCode = getValue(jo, "templateCode");

                #region 更新状态
                var entity = _app.GetEntityByKey(id);
                entity.Status = "1";
                entity.MUser = _currentUser.LoginAccount;
                entity.MTime = DateTime.Now;
                _app.Update(entity);
                #endregion

                //生成模板
                DevExpress.XtraReports.UI.XtraReport report =
                DevExpress.XtraReports.UI.XtraReport.FromFile(HttpContext.Current.Server.MapPath("~/") + ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString() + templateCode, true);
                DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;

                DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                {
                    Name = "Id",
                    Type = typeof(string),
                    Value = id
                };

                dataSource.Queries[0].Parameters[0] = parameter;
                return base.PrintToPDF(report);
          
        }
        #endregion
    }
}