using System;
using System.Linq;
using System.Web.Http;
using AOS.SRM.WebAPI.Controllers;
using AOS.SRM.Application.SHM;
using AOS.Core.Http;
using AOS.SRM.Entity.Sys;
using AOS.SRM.Entity.SHM;
using AOS.Core.Office;
using System.Collections.Generic;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM.ViewModel;
using System.Web;
using System.Configuration;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace AOS.SRM.WebAPI.Areas.SHM.Controllers
{
    /// <summary>
    /// 供应商主数据
    /// </summary>
    public class SupplierInfoController : ApiBaseController
    {
        private SupplierInfoApp _app = new SupplierInfoApp();
        private Sys_MailApp _mailApp = new Sys_MailApp();
        private MD_AttachmentManagementApp _attachApp = new MD_AttachmentManagementApp();
        private string labelTemplateUploadPath = ConfigurationManager.AppSettings["LabelTemplateUploadPath"]?.ToString();
        private string s_Upload = ConfigurationManager.AppSettings["S_Upload"]?.ToString();
        //private string baseURL = ConfigurationManager.AppSettings["BaseURL"]?.ToString();
        private string isSend = ConfigurationManager.AppSettings["IsSend"]?.ToString();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="page">分页信息</param>
        /// <param name="CompanyCode">参数</param>
        /// <param name="SupplierCode">日期区间</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageList([FromUri] Pagination page, [FromUri] string CompanyCode, [FromUri] string SupplierCode)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetPageList(page, t => t.SupplyerCode.Contains(SupplierCode) || t.SupplyerCode.Contains(SupplierCode)
                || t.SupplyerName1.Contains(SupplierCode)
                                                ).ToList();
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 新增
        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Add([FromBody] S_SupplierInfo entity)
        {
            var result = new ResponseData();
            try
            {
                if (entity == null)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = string.Format("接收的参数无效");
                    return Json(result);
                }
                bool isExist = _app.Any(t => t.SupplyerCode == entity.SupplyerCode);
                if (isExist)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = string.Format("供应商编码[{0}]已存在", entity.SupplyerCode);
                    return Json(result);
                }

                Sys_User currLoginUser = GetCurrentUser();
                entity.CUser = currLoginUser.LoginAccount;
                entity.MUser = currLoginUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.MTime = DateTime.Now;
                _app.Insert(entity);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 更新
        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Update([FromBody] S_SupplierInfo entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = GetCurrentUser().LoginAccount;
                entity.MTime = DateTime.Now;
                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 删除（包括批量）

        /// <summary>
        /// 删除
        /// </summary>
        /// <returns></returns>
        [HttpDelete]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteByKeys(ids, GetCurrentUser().LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 同步到SAP
        /// <summary>
        /// 同步供应商主数据到SAP
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult DoPost([FromBody] string[] ids)
        {
            string error_message = string.Empty;
            var result = new ResponseData();
            try
            {
                var data = _app.GetListByKeys(ids);
                var isPosted = _app.DoPost(data, out error_message);
                if (!isPosted)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = error_message;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion


        #region 新代码

        #region 校验是否已经存在相同供应商名称
        /// <summary>
        /// 校验是否已经存在相同供应商名称
        /// </summary>
        /// <param name="supplierName">供应商名称</param>
        /// <param name="supplierCode">供应商名称</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult CheckIsSameSupplier([FromUri] string supplierName, [FromUri] string supplierCode)
        {
            var result = new ResponseData();
            try
            {
                result.Code = (int)WMSStatusCode.Success;
                result.Data = _app.CheckIsSameSupplier(supplierName, supplierCode);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 查询所属采购员的账户信息 
        /// <summary>
        /// 查询所属采购员的账户信息 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetNotSupplier()
        {
            var result = new ResponseData();
            try
            {
                result.Data = new Sys_UserApp().GetNotSupplier();
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商信息注册（内部/外部）
        /// <summary>
        /// 供应商信息注册（内部/外部）
        /// </summary>
        /// <param name="data">entity是实体，fileIds是附件ID集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult CreateSupplierInfo([FromBody] CreateSupplier data)
        {
            var result = new ResponseData();
            try
            {
                if (_app.CreateSupplierInfo(data.entity, data.fileIds, _currentUser.LoginAccount, _currentUser.UserName))
                {
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商外部查询
        /// <summary>
        /// 供应商外部查询
        /// </summary>
        /// <param name="name">供应商名称</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetOutInfoByName([FromUri] string name)
        {
            var result = new ResponseData();
            try
            {
                var address = "";
                var list = _app.GetOutInfoByName(name);
                if (list.Count > 0)
                {
                    var supplierId = list[0].Id;
                    var attach = _attachApp.GetFirstEntity(t => t.ReferenceDocNumber == supplierId && t.TypeOrder == "合格供应商资质通知函");
                    if (attach != null)
                    {
                        address = attach.Address;
                    }
                }
                result.Data = new { list, address };
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商准入查询(分页)
        /// <summary>
        /// 供应商准入查询(分页)
        /// </summary>
        /// <param name="page">分页实体</param>
        /// <param name="keyword">模糊查询关键字</param>
        /// <param name="supplierName">供应商名称</param>
        /// <param name="dateTimes">查询日期区间</param>
        /// <param name="inOrOut">内部外部</param>
        /// <param name="states">准入状态</param>
        /// <param name="buyerCode">所属采购员</param>
        /// <param name="supplierType">供应商类型</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageListForAccess([FromUri] Pagination page, [FromUri] string supplierType, [FromUri] DateTime[] dateTimes, [FromUri] string inOrOut, [FromUri] string states, [FromUri] string supplierName, [FromUri] string buyerCode, [FromUri] string keyword = null)
        {
            var result = new ResponseData();
            try
            {
                var itemDatas = _app.GetPageListForAccess(page, keyword, supplierName, supplierType, dateTimes, inOrOut, states, buyerCode, _currentUser);
                result.Data = new ResponsePageData { total = page.Total, items = itemDatas };
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商准入审核
        /// <summary>
        /// 供应商准入审核
        /// </summary>
        /// <param name="ids">id集合</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierApproved([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                string msg = "";
                List<S_SupplierInfo> allowInList = null;
                if (_app.SupplierApproved(ids, _currentUser, out msg, out allowInList))
                {
                    //发放合格资质供应商通知函
                    if (allowInList.Count > 0)
                    {
                        foreach (var item in allowInList)
                        {
                            #region 生成附件
                            string dirTemplate = HttpContext.Current.Server.MapPath("~/") + labelTemplateUploadPath;
                            string templateFileName = "合格供应商资质通知函模板.repx";
                            DevExpress.XtraReports.UI.XtraReport report =
                            DevExpress.XtraReports.UI.XtraReport.FromFile(dirTemplate + templateFileName, true);
                            DevExpress.DataAccess.Sql.SqlDataSource dataSource = report.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;

                            DevExpress.DataAccess.Sql.QueryParameter parameter = new DevExpress.DataAccess.Sql.QueryParameter()
                            {
                                Name = "SupplierCode",
                                Type = typeof(string),
                                Value = item.SupplyerCode
                                //Value = "S001500016"
                            };
                            dataSource.Queries[0].Parameters[0] = parameter;
                            var timeSapce = DateTime.Now.ToString("yyyyMMddHHmmss");
                            string filePath = s_Upload + item.SupplyerName1 + "\\" + timeSapce + ".pdf";
                            //string filePath = s_Upload + "浙江杰立建设工程有限公司" + "\\" + "S001500016" + ".pdf";
                            string pdfFilePath = HttpContext.Current.Server.MapPath("~/") + filePath;
                            base.GetPrintPDFPath(report, pdfFilePath);

                            //生成附件记录
                            string address = "";
                            MD_AttachmentManagement attachMent = _attachApp.ChoiceType(1, out address);
                            attachMent.FileName = item.SupplyerCode + item.SupplyerName1 + ".pdf";
                            attachMent.Address = filePath;
                            attachMent.SupplyCode = item.SupplyerCode;
                            attachMent.SupplyName = item.SupplyerName1;
                            attachMent.ReferenceDocNumber = item.Id;
                            attachMent.CUser = _currentUser.LoginAccount;
                            attachMent.CTime = DateTime.Now;
                            attachMent.TypeOrder = "合格供应商资质通知函";
                            _attachApp.Insert(attachMent);

                            //更新附件记录信息
                            var files = _attachApp.GetList(x => x.ReferenceDocNumber == item.Id)?.ToList();
                            foreach (var file in files)
                            {
                                file.SupplyCode = item.SupplyerCode;
                            }
                            _attachApp.Update(files);
                            #endregion

                            #region 邮件发送
                            if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                            {
                                var message = "";
                                var typeDesc = "合格供应商资质通知函";
                                if (string.IsNullOrEmpty(item.EMail))
                                {
                                    result.Code = (int)WMSStatusCode.Failed;
                                    string errMsg = "邮件发送失败：收件人邮箱不能为空";
                                    result.Message = errMsg;
                                    return Json(result);
                                }

                                Sys_Mail mail = new Sys_Mail();
                                mail.MessageTypeDesc = typeDesc;
                                mail.SenderDisplayName = "西子富沃德SRM系统";
                                mail.ReceiverMail = item.EMail;
                                //mail.ReceiverMail = "<EMAIL>";
                                mail.MailSubject = typeDesc;
                                mail.MailBody = "您好：" + "\n\t" +
                                    "附件为西子富沃德发放的合格供应商资质通知函，请查收，谢谢！";
                                mail.CUser = _currentUser.LoginAccount;
                                mail.SendTime = DateTime.Now;
                                mail.AttachmentAddr = pdfFilePath;//附件地址
                                _mailApp.SendEmail(mail, out message);
                            }
                            #endregion
                        }
                    }
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = msg;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商驳回审核
        /// <summary>
        /// 供应商驳回审核
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierReject(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "ids"));
                var rejectReason = getValue(jo, "rejectReason");
                string msg = string.Empty;
                if (_app.SupplierReject(ids.ToArray(), rejectReason, _currentUser, out msg))
                {
                    result.Code = (int)WMSStatusCode.Success;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商信息更新并重新提交审核
        /// <summary>
        /// 供应商信息更新并重新提交审核
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierUpdateAndResubmit([FromBody] CreateSupplier data)
        {

            var result = new ResponseData();
            string errMsg = "";
            try
            {
                if (_app.SupplierUpdateAndResubmit(data.entity, data.fileIds, _currentUser.LoginAccount, _currentUser.UserName, out errMsg))
                {
                    result.Code = (int)WMSStatusCode.Success;
                    result.Message = errMsg;
                }
                else
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "提交失败";
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            result.Message = errMsg;
            return Json(result);
        }
        #endregion

        #region 供应商准入导出
        /// <summary>
        /// 供应商准入导出
        /// </summary>
        /// <param name="SupplyerName"></param>
        /// <param name="SupplierCategory"></param>
        /// <param name="InOrOut"></param>MaintainExportData
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult AdmiExportData(string SupplyerName, string SupplierCategory, string InOrOut, string Status, DateTime StartTime, DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.AdmittanceExportData(SupplyerName, SupplierCategory, InOrOut, Status, StartTime, EndTime);
                List<ExcelColumn<S_SupplierInfo>> columns = ExcelService.FetchDefaultColumnList<S_SupplierInfo>();
                string[] ignoreField = new string[] { };

                List<ExcelColumn<S_SupplierInfo>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<S_SupplierInfo> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        #region 供应商主数据查询(分页)
        /// <summary>
        /// 供应商信息维护查询(分页)
        /// </summary>
        /// <param name="page">分页实体</param>
        /// <param name="keyword">模糊查询关键字</param>
        /// <param name="supplierName">供应商名称</param>
        /// <param name="dateTimes">查询日期区间</param>
        /// <param name="inOrOut">内部外部</param>
        /// <param name="states">审核状态</param>
        /// <param name="supplierType">供应商类型</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetPageListForMaintain([FromUri] Pagination page, [FromUri] string supplierType, [FromUri] DateTime[] dateTimes, [FromUri] string inOrOut, [FromUri] string states, [FromUri] string supplierName, [FromUri] string keyword = null)
        {
            var result = new ResponseData();
            try
            {
                var itemDatas = _app.GetPageListForMaintain(page, keyword, supplierName, supplierType, dateTimes, inOrOut, states, _currentUser);
                result.Data = new ResponsePageData { total = page.Total, items = itemDatas };
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商主数据信息更新
        /// <summary>
        /// 供应商主数据信息更新
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierInfoUpdate([FromBody] CreateSupplier data)
        {
            var result = new ResponseData();
            try
            {
                string errMsg = "";
                if (!_app.SupplierInfoUpdate(data.entity, data.fileIds, _currentUser.UserName, out errMsg))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = errMsg;
                    return Json(result);
                }
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 供应商主数据导出
        /// <summary>
        /// 供应商主数据导出
        /// </summary>
        /// <param name="SupplyerName"></param>
        /// <param name="SupplierCategory"></param>
        /// <param name="InOrOut"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult MaintainExportData(string SupplyerName, string SupplierCategory, string InOrOut, string Status, DateTime StartTime, DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.MaintainExportData(SupplyerName, SupplierCategory, InOrOut, Status, StartTime, EndTime);
                List<ExcelColumn<S_SupplierInfo>> columns = ExcelService.FetchDefaultColumnList<S_SupplierInfo>();
                string[] ignoreField = new string[] { };

                List<ExcelColumn<S_SupplierInfo>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<S_SupplierInfo> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                return ExportToExcelFile(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

        #region 发放供应商编码
        /// <summary>
        /// 发放供应商编码
        /// </summary>
        /// <param name="id">主键</param>
        /// <param name="supplierCode">供应商编码</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ReleaseSupplierCode([FromUri] string id, [FromUri] string supplierCode)
        {
            var result = new ResponseData();
            try
            {
                var entity = _app.GetEntityByKey(id);
                entity.SupplyerCode = supplierCode;
                entity.MUser = _currentUser.LoginAccount;
                entity.MTime = DateTime.Now;
                _app.Update(entity);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 上传协议资料
        /// <summary>
        /// 上传协议资料
        /// entity:供应商信息实体；
        /// files:附件对象数组；
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AssessmentUpload()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.AssessmentUpload(_currentUser.UserName);
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion



        #endregion
    }
}