using AOS.Core.Extensions;
using AOS.Core.Http;
using AOS.SRM.Application.SHM;
using AOS.SRM.Entity.SHM;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 
    /// </summary>
    public class S_StandardsOfBankController : ApiBaseController
    {
        S_StandardsOfBankApp _app = new S_StandardsOfBankApp();

        #region 分页查询
        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList(JObject jo)
        {
            var result = new ResponseData();
            //try
            //{
            //    var page = JsonConvert.DeserializeObject<Pagination>(getValue(jo, "page"));
            //    var supplierCode = getValue(jo, "supplierCode");
            //    var docNum = getValue(jo, "docNum");
            //    var startTime = getValue(jo, "startTime").ToDateTime().Value;
            //    var endTime = getValue(jo, "endTime").ToDateTime().Value;

            //    var itemsData = _app.GetPageList(page,
            //        t => t.CTime >= startTime && t.CTime < endTime.AddDays(1)
            //        && (string.IsNullOrEmpty(supplierCode) || t.SupplierCode == supplierCode)
            //        && (string.IsNullOrEmpty(docNum) || t.DocNum.Contains(docNum))).ToList();
            //    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            //}
            //catch (Exception ex)
            //{
            //    result.Code = (int)WMSStatusCode.UnHandledException;
            //    result.Message = ex.InnerException?.Message ?? ex.Message;
            //}
            return Json(result);
        }
        #endregion

        #region 新增

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var entity = JsonConvert.DeserializeObject<S_StandardsOfBank>(getValue(jo, "entity"));
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                string msg = "";
                result.Data = _app.AddEntity(entity, fileIds, _currentUser, out msg);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateEntity(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var entity = JsonConvert.DeserializeObject<S_StandardsOfBank>(getValue(jo, "entity"));
                var fileIds = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "fileIds"));

                string msg = "";
                result.Data = _app.UpdateEntity(entity, fileIds, _currentUser, out msg);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetListByKeys(ids);
                data.ForEach(t =>
                {
                    t.DUser = _currentUser.LoginAccount;
                    t.DTime = DateTime.Now;
                    t.IsDelete = true;
                });

                result.Data = _app.Update(data);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供方接收

        /// <summary>
        /// 供方接收
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult SupplierReceive(JObject jo)
        {
            var result = new ResponseData();
            try
            {
                var ids = JsonConvert.DeserializeObject<List<string>>(getValue(jo, "ids"));

                var entitys = _app.GetListByKeys(ids.ToArray());
                entitys.ForEach(t =>
                {
                    t.Status = "供方已接收";
                    t.MUser = _currentUser.LoginAccount;
                    t.MTime = DateTime.Now;
                });

                _app.UpdateWithTran(entitys);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}