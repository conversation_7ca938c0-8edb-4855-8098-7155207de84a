using AOS.Core.Extensions;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.SHM;
using AOS.SRM.Entity.SHM;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 绩效考核综合评定
    /// </summary>
    public class S_PerformanceAppraisalController : ApiBaseController
    {
        S_PerformanceAppraisalApp _app = new S_PerformanceAppraisalApp();

        #region 查询
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList(JObject jo)
        {
            var result = new ResponseData();
            //try
            //{
            //    var docNum = getValue(jo, "docNum");

            //    var pageNumber = getValue(jo, "PageNumber");
            //    var pageSize = getValue(jo, "PageSize");
            //    Pagination page = new Pagination();
            //    page.PageNumber = pageNumber.ToInt().Value;
            //    page.PageSize = pageSize.ToInt().Value;
            //    page.Sort = "Line";

            //    var itemsData = _app.GetPageList(page,
            //        t => (string.IsNullOrEmpty(docNum) || t.DocNum == docNum)
            //        ).ToList();
            //    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            //}
            //catch (Exception ex)
            //{
            //    result.Code = (int)WMSStatusCode.UnHandledException;
            //    result.Message = ex.InnerException?.Message ?? ex.Message;
            //}
            return Json(result);
        }
        #endregion

        #region 新增

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddEntitys([FromBody] List<S_PerformanceAppraisal> entitys)
        {
            var result = new ResponseData();
            try
            {
                if (entitys.GroupBy(t => t.DocNum).Any(g => g.Count() > 1))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = "不同月份的不能一起操作";
                    return Json(result);
                }

                string msg = "";
                if (!_app.AddEntitys(entitys, _currentUser, out msg))
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = msg;
                    return Json(result);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateEntity([FromBody] S_PerformanceAppraisal entity)
        {
            var result = new ResponseData();
            try
            {
                var entitys = new List<S_PerformanceAppraisal>();
                entitys.Add(entity);

                result.Data = _app.UpdateEntitys(entitys, _currentUser);
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }
        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] List<S_PerformanceAppraisal> entitys)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.Delete(entitys, _currentUser.LoginAccount);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody] List<S_PerformanceAppraisal> entitys)
        {
            var result = new ResponseData();
            try
            {
                string msg = "";
                var flag = _app.ImprotExcelToBaseData(entitys, _currentUser, out msg);
                if (!flag)
                {
                    result.Code = (int)WMSStatusCode.Failed;
                    result.Message = msg;
                    return Json(result);
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="docNum"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string docNum)
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<S_PerformanceAppraisal>();
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[] { "IsDelete", "MUser", "MTime", "DUser", "DTime", "Id" };
                var itemsData = _app.GetList(x => (string.IsNullOrEmpty(x.DocNum) || x.DocNum == docNum)
                                           )?.ToList();
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<S_PerformanceAppraisal> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //需要格式转换的字段
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "CTime")
                //    {
                //        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                //    }
                //});
                return ExportToExcelFile<S_PerformanceAppraisal>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion
    }
}