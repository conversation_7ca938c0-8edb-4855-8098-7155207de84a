using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application;
using AOS.SRM.Application.SHM;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.SHM.DisposalManagement;
using AOS.SRM.Entity.SHM.ViewModel;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web.Http;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 让步接收处置单
    /// </summary>
    public class CompromiseDisposalController : ApiBaseController
    {
        private Sys_UserApp _userApp = new Sys_UserApp();
        private Sys_DictionaryApp _dictionaryApp = new Sys_DictionaryApp();
        private CompromiseDisposalApp _app = new CompromiseDisposalApp();
        CompromiseDisposalDetailsApp _detailApp = new CompromiseDisposalDetailsApp();
        private Sys_MailApp _mailApp = new Sys_MailApp();
        private SupplierInfoApp _supplierApp = new SupplierInfoApp();
        private string isSend = ConfigurationManager.AppSettings["IsSend"]?.ToString();

        /// <summary>
        /// 获取物料信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="MaterialCode"></param>
        /// <param name="MaterialName"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetMaterial([FromUri]Pagination page, [FromUri]string MaterialCode, string MaterialName)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetMaterial(page, MaterialCode, MaterialName);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 获取让步接收处置单列表信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="DisposalNo"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri]Pagination page, [FromUri]string SupplyCode, [FromUri]string DisposalNo, [FromUri]string Status, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetList(page, SupplyCode, DisposalNo, Status, StartTime, EndTime);
                result.Data = new { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 提交让步接收处置单
        /// </summary>
        /// <param name="CompromiseDisposal"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult InsertCompromiseDisposal([FromBody]I_CompromiseDisposal_Views CompromiseDisposal)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                result.Data = _app.InsertCompromiseDisposal(CompromiseDisposal, loginInfo.LoginAccount);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 通知财务确认
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == false)
                    {
                        var message = "";
                        var typeDesc = "让步接收处置单";
                        var entity = _app.GetFirstEntity(t => t.DisposalNo == CompromiseDisposal.DisposalNo);
                        string sendUserCode = "";
                        string email = "";
                        sendUserCode = _dictionaryApp.GetEntity(4, "DisposalCheckUser").EnumValue1;//财务确认
                        if (!string.IsNullOrEmpty(sendUserCode))
                        {
                            email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                        }
                        if (!string.IsNullOrEmpty(email))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = email;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "您有要确认的让步接收处置单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",处置单号：" + entity.DisposalNo + "，请前往西子富沃德SRM系统确认！";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #region 财务确认
        /// <summary>
        /// 财务定价
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult FinancialPricing([FromBody]ConcessionAcceptDto dto)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                _app.FinancialPricing(dto, loginInfo.LoginAccount, loginInfo.UserName);

                #region 邮件发送
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == false)
                    {
                        var message = "";
                        var typeDesc = "让步接收处置单";
                        var supplierInfo = _supplierApp.GetFirstEntityByFieldValue("SupplyerCode", dto.main.SupplyCode);
                        if (supplierInfo != null && !string.IsNullOrEmpty(supplierInfo.EMail))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = supplierInfo.EMail;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "让步接收处置单(" + dto.main.DisposalNo + ")已下达，请前往西子富沃德SRM系统确认！" +
                                "\n\t 回复截止日期：" + dto.main.DisposalDate.Value.ToShortDateString() + "，逾期未回复视为默认";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        /// <summary>
        /// 供应商回复意见
        /// </summary>
        /// <param name="CompromiseDisposal"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult InsertSupplyOptions([FromUri]S_CompromiseDisposal CompromiseDisposal, [FromUri]string[] Id)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                result.Data = _app.InsertSupplyOptions(CompromiseDisposal, Id, loginInfo.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 通知发起部门负责人
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == true)
                    {
                        var message = "";
                        var typeDesc = "让步接收处置单";
                        var entity = _app.GetEntityByKey(CompromiseDisposal.Id);
                        string sendUserCode = "";
                        string email = "";
                        if (entity.InitiatingDept == "质量中心")
                        {
                            sendUserCode = _dictionaryApp.GetEntity(2, "DisposalCheckUser").EnumValue1;//质量中心负责人
                        }
                        else if (entity.InitiatingDept == "CLC")
                        {
                            sendUserCode = _dictionaryApp.GetEntity(1, "DisposalCheckUser").EnumValue1;//CLC负责人

                        }
                        if (!string.IsNullOrEmpty(sendUserCode))
                        {
                            email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                        }
                        if (!string.IsNullOrEmpty(email))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = email;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "您有要审批的让步接收处置单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",处置单号：" + entity.DisposalNo + "，请前往西子富沃德SRM系统确认！";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 发起部门回复意见
        /// </summary>
        /// <param name="CompromiseDisposal"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult InsertRepDeptOptions([FromUri]S_CompromiseDisposal CompromiseDisposal, [FromUri] string[] Id)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                result.Data = _app.InsertRepDeptOptions(CompromiseDisposal, Id, loginInfo.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 如果金额小于等于20000,总经理不审核，财务直接接收
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == false)
                    {
                        var message = "";
                        var typeDesc = "让步接收处置单";
                        var entity = _app.GetEntityByKey(CompromiseDisposal.Id);
                        string sendUserCode = "";
                        string email = "";
                        if (entity.DisposalMoney <= 20000)
                        {
                            sendUserCode = _dictionaryApp.GetEntity(6, "DisposalCheckUser").EnumValue1;//财务
                        }
                        else
                        {
                            sendUserCode = _dictionaryApp.GetEntity(3, "DisposalCheckUser").EnumValue1;//总经理

                        }
                        email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                        if (!string.IsNullOrEmpty(email))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = email;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "您有要审批的让步接收处置单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",处置单号：" + entity.DisposalNo + "，请前往西子富沃德SRM系统确认！";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 总经理回复意见
        /// </summary>
        /// <param name="CompromiseDisposal"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult InsertGMOOptions([FromUri]S_CompromiseDisposal CompromiseDisposal, [FromUri] string[] Id)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                result.Data = _app.InsertGMOOptions(CompromiseDisposal, Id, loginInfo.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 通知财务签收
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == false)
                    {
                        var message = "";
                        var typeDesc = "让步接收处置单";
                        var entity = _app.GetEntityByKey(CompromiseDisposal.Id);
                        string sendUserCode = "";
                        string email = "";
                        sendUserCode = _dictionaryApp.GetEntity(6, "DisposalCheckUser").EnumValue1;//财务
                        email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                        if (!string.IsNullOrEmpty(email))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = email;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "您有要签收的让步接收处置单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",处置单号：" + entity.DisposalNo + "，请前往西子富沃德SRM系统确认！";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 财务审核
        /// </summary>
        /// <param name="CompromiseDisposal"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult InsertFinanceOptions([FromUri]S_CompromiseDisposal CompromiseDisposal, [FromUri] string[] Id)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                result.Data = _app.InsertFinanceOptions(CompromiseDisposal, Id, loginInfo.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    var message = "";
                    var typeDesc = "让步接收处置单财务确认";
                    var lstEntity = _app.GetListByKeys(Id);
                    var arrUserCode = lstEntity.Select(t => t.CUser).Distinct().ToArray();
                    var userList = _userApp.GetList(t => arrUserCode.Contains(t.LoginAccount)).ToList();
                    foreach (var item in userList)
                    {
                        if (string.IsNullOrEmpty(item.Email))
                        {
                            result.Code = (int)WMSStatusCode.Failed;
                            string msg = "邮件发送失败：收件人[" + item.UserName + "]邮箱不能为空";
                            result.Message = msg;
                            return Json(result);
                        }
                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = loginInfo.UserName;
                        mail.ReceiverMail = item.Email;
                        mail.MailSubject = typeDesc;
                        var disposalOrder = lstEntity.FindAll(t => t.CUser == item.LoginAccount)
                            .Select(t => t.DisposalNo).ToArray();
                        var mailBody = "您好：" + "\n" + "\t" +
                            "让步接收处置单(" + string.Join(",", disposalOrder) + ")财务已签收，请前往西子富沃德SRM系统确认！";
                        mail.MailBody = mailBody;
                        mail.CUser = loginInfo.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 删除让步接收处置单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult DeleteCompromiseDisposal([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteCompromiseDisposal(ids);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 跳转页面直接获取数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        //
        public IHttpActionResult GetList()
        {
            var result = new ResponseData();
            try
            {
                //查询不合格处置单据的特定字典项
                var itemsData = _dictionaryApp.GetList().OrderBy(x => x.EnumKey).OrderBy(x => x.EnumValue1).Where(x => x.TypeCode == "SHM002").ToList();
                //查询当前登录用户名和部门
                var login = _userApp.GetUserByAccount(GetCurrentUser().LoginAccount);
                //获取序号
                var getDoc = base.GenerateDocNum(DocType.PO, DocFixedNumDef.SHM_UnqualifiedNoticeNum);
                var time = DateTime.Now.ToString();
                result.Data = new { login, itemsData, getDoc, time };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 通过处置单号查询明细信息和主信息
        /// </summary>
        /// <param name="disposalNo"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetCompromiseDisposalInfo([FromUri]string disposalNo)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetInfo(disposalNo); //查询主信息
                var itemsDetailsData = _detailApp.GetList(disposalNo); //查询明细信息

                result.Data = new { mainData = itemsData, items = itemsDetailsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 关闭
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Close([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;

                var data = _app.GetListByKeys(ids);
                data.ForEach(t =>
                {
                    t.Status = "10";
                    t.MTime = DateTime.Now;
                    t.MUser = userCode;
                });
                _app.UpdateWithTran(data);

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #region 导出
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="DisposalNo"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string SupplyCode, [FromUri] string DisposalNo, [FromUri] string Status, [FromUri] DateTime StartTime, [FromUri] DateTime EndTime)
        {

            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetList(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(DisposalNo) || t.DisposalNo.Contains(DisposalNo))
                ).ToList();
                itemsData.ForEach(t =>
                {
                    if (t.Status == "1") { t.Status = "已下达"; }
                    else if (t.Status == "2") { t.Status = "供方已接收"; }
                    else if (t.Status == "3") { t.Status = "部门已审核"; }
                    else if (t.Status == "4") { t.Status = "总经理已审核"; }
                    else if (t.Status == "5") { t.Status = "财务已签收"; }
                    else if (t.Status == "6") { t.Status = "财务已定价"; }
                    else if (t.Status == "10") { t.Status = "已完成"; }
                });
                List<ExcelColumn<S_CompromiseDisposal>> columns = ExcelService.FetchDefaultColumnList<S_CompromiseDisposal>();
                string[] ignoreField = new string[] {
                    "Id","CompanyCode","FactoryCode","HandlingUserName","HandlingDate","RelDeptOptions","RelDeptUserName",
                    "RelDeptDate","GMOptions","GMOUserName","GMODate","SupplyOptions","SupplyUserName","SupplyDate","FinanceOptions","FinanceUserName",
                    "FinanceDate","IsDelete","CUser","MUser","MTime","DUser","DTime","MakePriceUser","MakePriceDate"
                };

                List<ExcelColumn<S_CompromiseDisposal>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<S_CompromiseDisposal> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<S_CompromiseDisposal>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

    }
}
