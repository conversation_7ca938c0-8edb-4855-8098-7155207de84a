using AOS.Core.Http;
using AOS.SRM.Application;
using AOS.SRM.Application.SHM;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Configuration;
using AOS.SRM.Entity.Sys;
using AOS.Core.Office;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 供应商奖励/扣款返还申请单
    /// </summary>
    public class RRDFController : ApiBaseController
    {
        private Sys_UserApp _userApp = new Sys_UserApp();
        private Sys_DictionaryApp _dictionaryApp = new Sys_DictionaryApp();
        private RRDFApp _app = new RRDFApp();
        private Sys_MailApp _mailApp = new Sys_MailApp();
        private SupplierInfoApp _supplierApp = new SupplierInfoApp();
        private string isSend = ConfigurationManager.AppSettings["IsSend"]?.ToString();

        #region 查询
        /// <summary>
        /// 
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="RNO"></param>
        /// <param name="Status"></param>
        /// <param name="ApplyType"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetList([FromUri] Pagination page, [FromUri] string SupplyCode, [FromUri] string RNO, [FromUri] string Status, [FromUri] string ApplyType, [FromUri] DateTime StartTime, [FromUri] DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetRRDF(page, SupplyCode, RNO, Status, ApplyType, StartTime, EndTime);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        /// <summary>
        /// 提交奖励/扣款返还申请单
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddRRDF([FromBody] S_RRDF RRDF)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.InsertRRDF(RRDF, _currentUser.LoginAccount, _currentUser.UserName);

                #region 邮件发送
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    var message = "";
                    var typeDesc = "奖励/扣款返还申请单";
                    string email = "";
                    if (!string.IsNullOrEmpty(RRDF.AssignSQE))
                    {
                        email = _userApp.GetFirstEntity(t => t.LoginAccount == RRDF.AssignSQE).Email;
                    }
                    if (!string.IsNullOrEmpty(email))
                    {
                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = "System";
                        mail.ReceiverMail = email;
                        mail.MailSubject = typeDesc;
                        mail.MailBody = "您好：" + "\n\t" +
                            "您有要审批的奖励/扣款返还申请单,供应商：" + RRDF.SupplyCode + " " + RRDF.SupplyName + ",申请单号：" + RRDF.RNo + "，请前往西子富沃德SRM系统确认！";
                        mail.CUser = _currentUser.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 删除当前数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult DeleteRRDF([FromUri] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteRRDF(ids);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 供应商管理意见（西子SQE）
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult InsertSupplyOptions([FromUri] S_RRDF RRDF, [FromUri] string[] Id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.SupplyManageOptions(RRDF, Id, _currentUser);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 通知品质部负责人
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    var message = "";
                    var typeDesc = "奖励/扣款返还申请单";
                    var entity = _app.GetEntityByKey(RRDF.Id);
                    string email = "";
                    string sendUserCode = _dictionaryApp.GetEntity(2, "DisposalCheckUser").EnumValue1;//质量中心=品质部
                    if (!string.IsNullOrEmpty(sendUserCode))
                    {
                        email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                    }
                    if (!string.IsNullOrEmpty(email))
                    {
                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = "System";
                        mail.ReceiverMail = email;
                        mail.MailSubject = typeDesc;
                        mail.MailBody = "您好：" + "\n\t" +
                            "您有要审批的奖励/扣款返还申请单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",申请单号：" + entity.RNo + "，请前往西子富沃德SRM系统确认！";
                        mail.CUser = _currentUser.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 品质部意见
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult InsertRepDeptOptions([FromUri] S_RRDF RRDF, [FromUri] string[] Id)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                result.Data = _app.InsertRepDeptOptions(RRDF, Id, loginInfo.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 通知财务副总审核
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    var message = "";
                    var typeDesc = "奖励/扣款返还申请单";
                    var entity = _app.GetEntityByKey(RRDF.Id);
                    string sendUserCode = "";
                    string email = "";
                    sendUserCode = _dictionaryApp.GetEntity(5, "DisposalCheckUser").EnumValue1;//财务副总
                    if (!string.IsNullOrEmpty(sendUserCode))
                    {
                        email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                    }
                    if (!string.IsNullOrEmpty(email))
                    {
                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = "System";
                        mail.ReceiverMail = email;
                        mail.MailSubject = typeDesc;
                        mail.MailBody = "您好：" + "\n" + "\t" +
                            "您有要审批的奖励/扣款返还申请单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",申请单号：" + entity.RNo + "，请前往西子富沃德SRM系统确认！";
                        mail.CUser = loginInfo.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 财务部意见
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult InsertFinanceOptions([FromUri] S_RRDF RRDF, [FromUri] string[] Id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.InsertFinanceOptions(RRDF, Id, _currentUser.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 如果金额小于等于20000,总经理不审核，财务直接接收
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    var message = "";
                    var typeDesc = "奖励/扣款返还申请单";
                    var entity = _app.GetEntityByKey(RRDF.Id);
                    string sendUserCode = "";
                    string email = "";
                    if (entity.DisposalMoney <= 20000)
                    {
                        sendUserCode = _dictionaryApp.GetEntity(6, "DisposalCheckUser").EnumValue1;//财务
                    }
                    else
                    {
                        sendUserCode = _dictionaryApp.GetEntity(3, "DisposalCheckUser").EnumValue1;//总经理

                    }
                    email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                    if (!string.IsNullOrEmpty(email))
                    {
                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = "System";
                        mail.ReceiverMail = email;
                        mail.MailSubject = typeDesc;
                        mail.MailBody = "您好：" + "\n\t" +
                            "您有要审批的奖励/扣款返还申请单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",申请单号：" + entity.RNo + "，请前往西子富沃德SRM系统确认！";
                        mail.CUser = _currentUser.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 总经理意见
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult InsertGMOOptions([FromUri] S_RRDF RRDF, [FromUri] string[] Id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.InsertGMOOptions(RRDF, Id, _currentUser.UserName);
                result.Code = (int)WMSStatusCode.Success;
                #region 邮件发送 通知财务签收
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    var message = "";
                    var typeDesc = "奖励/扣款返还申请单";
                    var entity = _app.GetEntityByKey(RRDF.Id);
                    string sendUserCode = "";
                    string email = "";
                    sendUserCode = _dictionaryApp.GetEntity(6, "DisposalCheckUser").EnumValue1;//财务
                    email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                    if (!string.IsNullOrEmpty(email))
                    {
                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = "System";
                        mail.ReceiverMail = email;
                        mail.MailSubject = typeDesc;
                        mail.MailBody = "您好：" + "\n\t" +
                            "您有要签收的奖励/扣款返还申请单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",申请单号：" + entity.RNo + "，请前往西子富沃德SRM系统确认！";
                        mail.CUser = _currentUser.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 财务部门签字
        /// </summary>
        /// <param name="RRDF"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult InsertFinanceSign([FromUri] S_RRDF RRDF, [FromUri] string[] Id)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.InsertFinanceSign(RRDF, Id, _currentUser.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件通知发起人
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    var message = "";
                    var typeDesc = "奖励/扣款返还申请单";
                    var entity = _app.GetEntityByKey(RRDF.Id);
                    string sendUserCode = entity.CUser;
                    string email = "";
                    email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                    if (!string.IsNullOrEmpty(email))
                    {
                        Sys_Mail mail = new Sys_Mail();
                        mail.MessageTypeDesc = typeDesc;
                        mail.SenderDisplayName = "System";
                        mail.ReceiverMail = email;
                        mail.MailSubject = typeDesc;
                        mail.MailBody = "您好：" + "\n\t" +
                            "财务已签收奖励/扣款返还申请单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",申请单号：" + entity.RNo + "，请前往西子富沃德SRM系统确认！";
                        mail.CUser = _currentUser.LoginAccount;
                        mail.SendTime = DateTime.Now;
                        _mailApp.SendEmail(mail, out message);
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 跳转页面直接获取数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        //
        public IHttpActionResult GetDictionary()
        {
            var result = new ResponseData();
            try
            {
                //查询处置单据的特定字典项
                var itemsData = _dictionaryApp.GetList(t => t.TypeCode == "SHM003").OrderByDescending(t => t.EnumKey).ToList();
                var itemdata = _dictionaryApp.GetList(t => t.TypeCode == "SHM004").OrderByDescending(t => t.EnumKey).ToList();
                //查询当前登录用户名和部门
                var login = _userApp.GetUserByAccount(_currentUser.LoginAccount);
                //获取序号
                var getDoc = base.GenerateDocNum(DocType.PO, DocFixedNumDef.SHM_UnqualifiedNoticeNum);
                var time = DateTime.Now.ToString();
                result.Data = new { login, itemsData, itemdata, getDoc, time };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        /// <summary>
        /// 关闭
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Close([FromUri] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = _currentUser.LoginAccount;

                var data = _app.GetListByKeys(ids);
                data.ForEach(t =>
                {
                    t.Status = "10";
                    t.MTime = DateTime.Now;
                    t.MUser = userCode;
                });
                _app.UpdateWithTran(data);

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }


        #region 根据角色描述获取用户信息
        /// <summary>
        /// 根据角色描述获取用户信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetUserInfoByRoleDesc()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _userApp.GetUserInfoByRoleDesc("SQE");
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 导出
        /// <summary>
        /// 
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="RNO"></param>
        /// <param name="Status"></param>
        /// <param name="ApplyType"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string SupplyCode, [FromUri] string RNO, [FromUri] string Status, [FromUri] string ApplyType, [FromUri] DateTime StartTime, [FromUri] DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetList(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(ApplyType) || t.ApplyType.Contains(ApplyType))
                && (string.IsNullOrEmpty(RNO) || t.RNo.Contains(RNO))
                ).ToList();
                itemsData.ForEach(t =>
                {
                    if (t.Status == "1") { t.Status = "已申请"; }
                    else if (t.Status == "2") { t.Status = "供应商管理已审核"; }
                    else if (t.Status == "3") { t.Status = "品质部已审核"; }
                    else if (t.Status == "4") { t.Status = "财务副总已审核"; }
                    else if (t.Status == "5") { t.Status = "总经理已审核"; }
                    else if (t.Status == "6") { t.Status = "财务已签收"; }
                    else if (t.Status == "10") { t.Status = "已完成"; }

                    if (t.ApplyType == "3") { t.ApplyType = "奖励返还"; }
                    else if (t.ApplyType == "4") { t.ApplyType = "扣款返还"; }
                });
                List<ExcelColumn<S_RRDF>> columns = ExcelService.FetchDefaultColumnList<S_RRDF>();
                string[] ignoreField = new string[] {
                    "Id","CompanyCode","FactoryCode","HandlingUserName","HandlingDate","RelDeptOptions","RelDeptUserName",
                    "RelDeptDate","GMOptions","GMOUserName","GMODate","SupplyOptions","SupplyUserName","SupplyDate","FinanceOptions","FinanceUserName",
                    "FinanceDate","IsDelete","CUser","MUser","MTime","DUser","DTime","SQEOptions","SQEUserName","SQEDate","FinanceSignOptions",
                    "FinanceSignUserName","FinanceSignDate","AssignSQE"
                };

                List<ExcelColumn<S_RRDF>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<S_RRDF> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<S_RRDF>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion%
    }
}
