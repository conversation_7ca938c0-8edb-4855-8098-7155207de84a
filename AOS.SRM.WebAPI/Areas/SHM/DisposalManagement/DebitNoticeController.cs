using AOS.Core.Http;
using AOS.SRM.Application;
using AOS.SRM.Application.SHM;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.SHM;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Configuration;
using AOS.Core.Office;
using AOS.SRM.Application.Base;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    /// 扣款通知单
    /// </summary>
    public class DebitNoticeController : ApiBaseController
    {
        private Sys_UserApp _userApp = new Sys_UserApp();
        private Sys_DictionaryApp _dictionaryApp = new Sys_DictionaryApp();
        private DebitNoticeApp _app = new DebitNoticeApp();
        private Sys_MessageNotifySettingApp messagenotifysettingapp = new Sys_MessageNotifySettingApp();
        private SupplierInfoApp _supplierApp = new SupplierInfoApp();
        private string isSend = ConfigurationManager.AppSettings["IsSend"]?.ToString();

        private Sys_MailApp _mailApp = new Sys_MailApp();

        #region 提交扣款通知单
        /// <summary>
        /// 提交扣款通知单
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddDebitNotice([FromBody]S_DebitNotice entity)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();

                _app.InsertDebitNotice(entity, entity.CompanyCode, loginInfo.UserName, loginInfo.LoginAccount);

                #region 邮件发送  通知供应商
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == false)
                    {
                        var message = "";
                        var typeDesc = "扣款通知单";
                        var supplierInfo = _supplierApp.GetFirstEntityByFieldValue("SupplyerCode", entity.SupplyCode);
                        if (supplierInfo != null && !string.IsNullOrEmpty(supplierInfo.EMail))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = supplierInfo.EMail;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "扣款通知单(" + entity.DisposalOrder + ")已下达，请前往西子富沃德SRM系统确认！" +
                                "\n\t 回复截止日期：" + entity.DisposalDate.Value.ToShortDateString() + "，逾期未回复视为默认";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 供应商回复
        /// <summary>
        /// 供应商回复
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult SupplierReply([FromUri] S_DebitNotice debitNotice, [FromUri]string[] Id)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();

                result.Data = _app.SupplierReply(debitNotice, Id, loginInfo.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 通知发起部门负责人
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == true)
                    {
                        var message = "";
                        var typeDesc = "扣款通知单";
                        var entity = _app.GetEntityByKey(debitNotice.Id);
                        string sendUserCode = "";
                        string email = "";
                        if (entity.InitiatingDept == "质量中心")
                        {
                            sendUserCode = _dictionaryApp.GetEntity(2, "DisposalCheckUser").EnumValue1;//质量中心负责人
                        }
                        else if (entity.InitiatingDept == "CLC")
                        {
                            sendUserCode = _dictionaryApp.GetEntity(1, "DisposalCheckUser").EnumValue1;//CLC负责人

                        }
                        if (!string.IsNullOrEmpty(sendUserCode))
                        {
                            email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                        }
                        if (!string.IsNullOrEmpty(email))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = email;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "您有要审批的扣款通知单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",处置单号：" + entity.DisposalOrder + "，请前往西子富沃德SRM系统确认！";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 发布部门意见提交
        /// <summary>
        /// 发出部门意见提交
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult RepDeptOptions([FromUri] S_DebitNotice debitNotice, [FromUri]string[] Id)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                result.Data = _app.RepDeptOptions(debitNotice, Id, loginInfo.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 如果金额小于等于20000,总经理不审核，财务直接接收
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == false)
                    {
                        var message = "";
                        var typeDesc = "扣款通知单";
                        var entity = _app.GetEntityByKey(debitNotice.Id);
                        string sendUserCode = "";
                        string email = "";
                        if (entity.DisposalMoney <= 20000)
                        {
                            sendUserCode = _dictionaryApp.GetEntity(6, "DisposalCheckUser").EnumValue1;//财务
                        }
                        else
                        {
                            sendUserCode = _dictionaryApp.GetEntity(3, "DisposalCheckUser").EnumValue1;//总经理

                        }
                        email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                        if (!string.IsNullOrEmpty(email))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = email;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "您有要审批的扣款通知单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",处置单号：" + entity.DisposalOrder + "，请前往西子富沃德SRM系统确认！";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 总经理意见提交
        /// <summary>
        /// 总经理意见提交
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GMOOptions([FromUri] S_DebitNotice DebitNotice, [FromUri]string[] Id)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                result.Data = _app.GMOOptions(DebitNotice, Id, loginInfo.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件发送 通知财务签收
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == false)
                    {
                        var message = "";
                        var typeDesc = "扣款通知单";
                        var entity = _app.GetEntityByKey(DebitNotice.Id);
                        string sendUserCode = "";
                        string email = "";
                        sendUserCode = _dictionaryApp.GetEntity(6, "DisposalCheckUser").EnumValue1;//财务
                        email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                        if (!string.IsNullOrEmpty(email))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = email;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "您有要签收的扣款通知单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",处置单号：" + entity.DisposalOrder + "，请前往西子富沃德SRM系统确认！";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 财务审核
        /// <summary>
        /// 财务审核
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult FinanceOptions([FromUri] S_DebitNotice DebitNotice, [FromUri]string[] Id)
        {
            var result = new ResponseData();
            try
            {
                var loginInfo = GetCurrentUser();
                var lstEntity = _app.GetListByKeys(Id);
                result.Data = _app.FinanceOptions(DebitNotice, Id, loginInfo.UserName);
                result.Code = (int)WMSStatusCode.Success;

                #region 邮件通知发起人
                if (isSend == "1") //邮件发送开关  1：发送  0：不发送
                {
                    if (loginInfo.IsSupplier == false)
                    {
                        var message = "";
                        var typeDesc = "扣款通知单";
                        var entity = _app.GetEntityByKey(DebitNotice.Id);
                        string sendUserCode = entity.CUser;
                        string email = "";
                        email = _userApp.GetFirstEntity(t => t.LoginAccount == sendUserCode).Email;
                        if (!string.IsNullOrEmpty(email))
                        {
                            Sys_Mail mail = new Sys_Mail();
                            mail.MessageTypeDesc = typeDesc;
                            mail.SenderDisplayName = "System";
                            mail.ReceiverMail = email;
                            mail.MailSubject = typeDesc;
                            mail.MailBody = "您好：" + "\n" + "\t" +
                                "财务已签收扣款通知单,供应商：" + entity.SupplyCode + " " + entity.SupplyName + ",处置单号：" + entity.DisposalOrder + "，请前往西子富沃德SRM系统确认！";
                            mail.CUser = loginInfo.LoginAccount;
                            mail.SendTime = DateTime.Now;
                            _mailApp.SendEmail(mail, out message);
                        }
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 删除
        /// <summary>
        /// 删除扣款通知单
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult DeleteDebitNotice([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.DeleteDebitNotice(ids);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 查询
        /// <summary>
        /// 查询扣款通知单列表
        /// </summary>
        /// <param name="page"></param>
        /// <param name="SupplyCode"></param>
        /// <param name="DisposalOrder"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetDebitNotice([FromUri]Pagination page, [FromUri]string SupplyCode, [FromUri]string DisposalOrder, [FromUri]string Status, [FromUri]DateTime StartTime, [FromUri]DateTime EndTime)
        {
            var result = new ResponseData();
            try
            {
                var itemsData = _app.GetDebitNotice(page, SupplyCode, DisposalOrder, Status, StartTime, EndTime);
                result.Data = new ResponsePageData { total = page.Total, items = itemsData };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 跳转页面直接获取数据
        /// <summary>
        /// 跳转页面直接获取数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        //
        public IHttpActionResult GetList()
        {
            var result = new ResponseData();
            try
            {
                //查询扣款通知单据的特定字典项
                var itemsData = _dictionaryApp.GetList().OrderBy(x => x.EnumKey).OrderBy(x => x.EnumValue1).Where(x => x.TypeCode == "SHM005").ToList();
                //查询当前登录用户名和部门
                var login = _userApp.GetUserByAccount(GetCurrentUser().LoginAccount);
                //获取序号
                var getDoc = base.GenerateDocNum(DocType.PO, DocFixedNumDef.SHM_UnqualifiedNoticeNum);
                var time = DateTime.Now.ToString();
                result.Data = new { login, itemsData, getDoc, time };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion

        #region 关闭
        /// <summary>
        /// 关闭
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult Close([FromUri]string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var userCode = GetCurrentUser().LoginAccount;

                var data = _app.GetListByKeys(ids);
                data.ForEach(t =>
                {
                    t.Status = "10";
                    t.MTime = DateTime.Now;
                    t.MUser = userCode;
                });
                _app.UpdateWithTran(data);

                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion


        #region 导出
        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="SupplyCode"></param>
        /// <param name="DisposalOrder"></param>
        /// <param name="Status"></param>
        /// <param name="StartTime"></param>
        /// <param name="EndTime"></param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string SupplyCode, [FromUri] string DisposalOrder, [FromUri] string Status, [FromUri] DateTime StartTime, [FromUri] DateTime EndTime)
        {

            var result = new ResponseData();
            try
            {
                Sys_User currLoginUser = GetCurrentUser();
                var itemsData = _app.GetList(t => (t.CTime >= StartTime) && (t.CTime < EndTime.AddDays(1))
                && (string.IsNullOrEmpty(Status) || t.Status.Contains(Status))
                && (string.IsNullOrEmpty(SupplyCode) || t.SupplyCode.Contains(SupplyCode))
                && (string.IsNullOrEmpty(DisposalOrder) || t.DisposalOrder.Contains(DisposalOrder))
                ).ToList();
                itemsData.ForEach(t =>
                {
                    if (t.Status == "1") { t.Status = "已下达"; }
                    else if (t.Status == "2") { t.Status = "供方已接收"; }
                    else if (t.Status == "3") { t.Status = "部门已审核"; }
                    else if (t.Status == "4") { t.Status = "总经理已审核"; }
                    else if (t.Status == "5") { t.Status = "财务已签收"; }
                    else if (t.Status == "10") { t.Status = "已完成"; }
                });
                List<ExcelColumn<S_DebitNotice>> columns = ExcelService.FetchDefaultColumnList<S_DebitNotice>();
                string[] ignoreField = new string[] {
                    "Id","CompanyCode","FactoryCode","HandlingOpinions","HandlingDate","RelDeptOptions","RelDeptUserName",
                    "RelDeptDate","GMOptions","GMOUserName","GMODate","SupplyOptions","SupplyUserName","SupplyDate","FinanceOptions","FinanceUserName",
                    "FinanceDate","IsDelete","CUser","MUser","MTime","DUser","DTime"
                };

                List<ExcelColumn<S_DebitNotice>> ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<S_DebitNotice> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }

                return ExportToExcelFile<S_DebitNotice>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion
    }
}
