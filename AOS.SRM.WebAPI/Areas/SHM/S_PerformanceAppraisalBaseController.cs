using AOS.Core.Extensions;
using AOS.Core.Http;
using AOS.Core.Office;
using AOS.SRM.Application.SHM;
using AOS.SRM.Entity.SHM;
using AOS.SRM.WebAPI.Controllers;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.SHM
{
    /// <summary>
    ///  绩效考核基础配置
    /// </summary>
    public class S_PerformanceAppraisalBaseController : ApiBaseController
    {
        S_PerformanceAppraisalBaseApp _app = new S_PerformanceAppraisalBaseApp();

        #region 查询分页列表

        /// <summary>
        /// 查询分页列表
        /// </summary>
        /// <param name="jo"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult GetPageList(JObject jo)
        {
            var result = new ResponseData();
            //try
            //{
            //    var year = getValue(jo, "year");
            //    var supplierCode = getValue(jo, "supplierCode");
            //    var assessMode = getValue(jo, "assessMode");

            //    var pageNumber = getValue(jo, "PageNumber");
            //    var pageSize = getValue(jo, "PageSize");
            //    Pagination page = new Pagination();
            //    page.PageNumber = pageNumber.ToInt().Value;
            //    page.PageSize = pageSize.ToInt().Value;

            //    var itemsData = _app.GetPageList(page,
            //        t => (t.AssessYear == year)
            //        && (string.IsNullOrEmpty(supplierCode) || t.SupplierCode == supplierCode)
            //        && (string.IsNullOrEmpty(assessMode) || t.AssessMode == assessMode)).ToList();
            //    result.Data = new ResponsePageData { total = page.Total, items = itemsData };
            //}
            //catch (Exception ex)
            //{
            //    result.Code = (int)WMSStatusCode.UnHandledException;
            //    result.Message = ex.InnerException?.Message ?? ex.Message;
            //}
            return Json(result);
        }
        #endregion

        #region 新增

        /// <summary>
        /// 新增
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult AddEntity([FromBody] S_PerformanceAppraisalBase entity)
        {
            var result = new ResponseData();
            try
            {
                entity.CUser = _currentUser.LoginAccount;
                entity.CTime = DateTime.Now;
                entity.IsDelete = false;

                result.Data = _app.Insert(entity);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

        #endregion

        #region 更新

        /// <summary>
        /// 更新
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult UpdateEntity([FromBody] S_PerformanceAppraisalBase entity)
        {
            var result = new ResponseData();
            try
            {
                entity.MUser = _currentUser.LoginAccount;
                entity.MTime = DateTime.Now;

                result.Data = _app.Update(entity);
                result.Code = (int)WMSStatusCode.Success;

            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 删除

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Delete([FromBody] string[] ids)
        {
            var result = new ResponseData();
            try
            {
                var data = _app.GetListByKeys(ids);
                data.ForEach(t =>
                {
                    t.DUser = _currentUser.LoginAccount;
                    t.DTime = DateTime.Now;
                    t.IsDelete = true;
                });

                result.Data = _app.Update(data);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        #endregion


        #region 导入

        /// <summary>
        /// 导入
        /// </summary>
        /// <param name="entitys"></param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult ImportExcelToData([FromBody] List<S_PerformanceAppraisalBase> entitys)
        {
            var result = new ResponseData();
            try
            {
                if (_app.ImprotExcelToBaseData(entitys, _currentUser.LoginAccount))
                {
                    result.Data = true;
                    result.Code = (int)WMSStatusCode.Success;
                }
                else
                {
                    result.Data = false;
                    result.Code = (int)WMSStatusCode.Failed;
                }
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
            return Json(result);
        }

        #endregion

        #region 导出

        /// <summary>
        /// 导出
        /// </summary>
        /// <param name="year">客户编码</param>
        /// <param name="supplierCode">客户名称</param>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult ExportToExcelFile([FromUri] string year, [FromUri] string supplierCode)
        {
            var result = new ResponseData();
            try
            {
                var columns = ExcelService.FetchDefaultColumnList<S_PerformanceAppraisalBase>();
                //排除不需要的列名称 字符串
                string[] ignoreField = new string[] { "IsDelete", "MUser", "MTime", "DUser", "DTime", "Id" };
                var itemsData = _app.GetList(x => (x.AssessYear == year)
                                               && (string.IsNullOrEmpty(supplierCode) || x.SupplierCode.Contains(supplierCode))
                                           )?.ToList();
                var ignoreFieldList = columns.Where(t => ignoreField.Contains(t.ColumnName)).ToList();
                foreach (ExcelColumn<S_PerformanceAppraisalBase> userColumn in ignoreFieldList)
                {
                    columns.Remove(userColumn);
                }
                //需要格式转换的字段
                //columns.ForEach((column) =>
                //{
                //    if (column.ColumnName == "CTime")
                //    {
                //        column.Formattor = ExcelExportFormatter.DateTimeFormatter;
                //    }
                //});
                return ExportToExcelFile<S_PerformanceAppraisalBase>(itemsData, columns);
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
                return Json(result);
            }
        }
        #endregion

    }
}