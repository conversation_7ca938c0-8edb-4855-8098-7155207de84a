
using AOS.Core.Http;
using AOS.SRM.Application.Sys;
using AOS.SRM.Entity.Sys;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.Common
{
    /// <summary>
    /// 公共方法
    /// </summary>
    public class CommonController : ApiBaseController
    {
        MD_AttachmentManagementApp attachmentManagementapp = new MD_AttachmentManagementApp();
        ///// <summary>
        ///// 下载文件
        ///// </summary>
        //[HttpGet]
        //public HttpResponseMessage DownloadFile(string FilName)
        //{
        //    string fileName = FilName;
        //    //string filePath = HttpContext.Current.Server.MapPath("~/") + "UploadFiles\\LabelTemplate\\" + "报检单.repx";
        //    string filePath = HttpContext.Current.Server.MapPath("~/") + "UploadFiles\\files\\FO\\" + fileName ;
        //    FileStream stream = new FileStream(filePath, FileMode.Open);
        //    HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
        //    response.Content = new StreamContent(stream);
        //    response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
        //    response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
        //    {
        //        FileName = HttpUtility.UrlEncode(fileName)
        //    };
        //    response.Headers.Add("Access-Control-Expose-Headers", "FileName");
        //    response.Headers.Add("FileName", HttpUtility.UrlEncode(fileName));
        //    return response;
        //}
        /// <summary>
        /// 下载文件
        /// </summary>
        [HttpGet]
        public HttpResponseMessage DownloadFile(string id)
        {
            var list = attachmentManagementapp.DbContext.Queryable<MD_AttachmentManagement>().Where(t => t.FileId == id).ToList();
            //下载地址
            string address = "";
            string fileName = "";
            foreach (var item in list)
            {
                if (id != null)
                {
                    if (item.FileId == id)
                    {
                        address = item.Address;
                    }
                }
            }
            //string filePath = (HttpContext.Current.Server.MapPath("~/") + address).Replace("\\",@"\");
            //return filePath;
            string filePath = HttpContext.Current.Server.MapPath("~/") + address;
            FileStream stream = new FileStream(filePath, FileMode.Open);
            //FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
            //StreamReader sr = new StreamReader(fs, System.Text.Encoding.Default);
            HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
            response.Content = new StreamContent(stream);
            response.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
            response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
            {
                FileName = HttpUtility.UrlEncode(fileName)
            };
            response.Headers.Add("Access-Control-Expose-Headers", "FileName");
            response.Headers.Add("FileName", HttpUtility.UrlEncode(filePath));
            return response;
        }

        /// <summary>
        /// 上传附件
        /// </summary>
        /// <param name="SupplyCode">供应商编码</param>
        /// <param name="SupplyName">供应商名称</param>
        /// <param name="AttachType">附件类型</param>
        /// <returns></returns>
        [HttpPost]
        public IHttpActionResult Upload([FromUri] string SupplyCode, [FromUri] string SupplyName, [FromUri]string AttachType)
        {

            string attachTypeName = ""; //附件类型
            string menuType = "";  //菜单
            string newid = System.Guid.NewGuid().ToString();
            
            var result = new ResponseData();
            try
            {
                switch (AttachType)
                {
                    case "Reconciliation": //对账单
                        attachTypeName = "对账单";
                        menuType= "财务对账-对账单管理";
                        break;
                    case "ReturnOrder":
                        attachTypeName = "返还单";
                        menuType = "财务对账-返还单管理";
                        break;
                    default: break;
                };
                string address = ConfigurationManager.AppSettings[AttachType]?.ToString();

                //获取参数信息
                HttpContextBase context = (HttpContextBase)Request.Properties["MS_HttpContext"];
                HttpRequestBase request = context.Request;
                string fileName = request.Params["TempleteFile"]?.ToString();
                string newFileName = DateTime.Now.ToString("yyyyMMddhhmmss").ToString() + Path.GetExtension(fileName);//重新命名（序列）
                string resPath = address + newFileName;
                string filePath = HttpContext.Current.Server.MapPath("~/") + resPath;
                //对名称进行重命名
                HttpResponseMessage response = new HttpResponseMessage(HttpStatusCode.OK);
                response.Headers.Add("FileName", HttpUtility.UrlEncode(fileName));

                #region 存入附件管理表
                //存入附件管理表
                MD_AttachmentManagement attachmentmanagement = new MD_AttachmentManagement();
                attachmentmanagement.Id = newid;
                attachmentmanagement.AttachTypeCode = AttachType;
                attachmentmanagement.AttachType = attachTypeName;
                //attachmentmanagement.FileId = newid;
                attachmentmanagement.FileName = fileName;
                //attachmentmanagement.FileCodeName = resPath.Replace(Path.GetFileNameWithoutExtension(fileName), DateTime.Now.ToString("yyyyMMddhhmmss").ToString());
                attachmentmanagement.Address = resPath;
                attachmentmanagement.MenuType = menuType;
                attachmentmanagement.SupplyCode = SupplyCode;
                attachmentmanagement.SupplyName = SupplyName;
                //attachmentmanagement.TypeOrder = TypeOrder;
                attachmentManagementapp.Insert(attachmentmanagement);
                #endregion

                request.Files[0].SaveAs(filePath);
                result.Data = new { FilePath = "" + resPath.Replace("\\", "/"), newid };
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }

    }
}