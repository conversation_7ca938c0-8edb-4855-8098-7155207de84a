using AOS.Core.Http;
using AOS.SRM.Application;
using AOS.SRM.Application.SHM;
using AOS.SRM.Application.Sys;
using AOS.SRM.WebAPI.Controllers;
using System;
using System.Web.Http;

namespace AOS.SRM.WebAPI.Areas.PXC
{
    /// <summary>
    /// 查询公司主数据
    /// </summary>
    public class CompanyController : ApiBaseController
    {
        private SupplierInfoApp supplierApp = new SupplierInfoApp();
        private Sys_DictionaryApp _app = new Sys_DictionaryApp();
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetCompanyCode()
        {
            var result = new ResponseData();
            try
            {
                result.Data = _app.GetList().OrderBy(x => x.EnumKey).OrderBy(x => x.EnumValue1).Where(x => x.TypeCode == "XZ_COMPANY").ToList();
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
        /// <summary>
        /// 查询供应商
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IHttpActionResult GetSupplyCode([FromUri]string keyword = "")
        {
            var result = new ResponseData();
            try
            {
                result.Data = supplierApp.GetSupplierInfo(GetCurrentUser(), keyword);
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
        }
    }
}
