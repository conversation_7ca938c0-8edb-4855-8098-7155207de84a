using AOS.SRM.Entity.Sys;
using AOS.SRM.Entity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using AOS.SRM.Application.Sys;
using AOS.Core.Http;
using AOS.SRM.Application;
using System.Configuration;
using System.Data;
using AOS.Core.Office;
using System.IO;
using System.Net.Http.Headers;
using DevExpress.XtraPrinting;
using System.Web;
using System.Data.SqlClient;
using AOS.Core.Security;
using Newtonsoft.Json.Linq;
using AOS.SRM.Application.Base;
using Microsoft.ReportingServices.ReportProcessing.ReportObjectModel;

namespace AOS.SRM.WebAPI.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    public class ApiBaseController : ApiController
    {
        private Sys_UserApp _app = new Sys_UserApp();
        private Sys_RoleApp _roleApp = new Sys_RoleApp();
        private int CFG_ExportMaxRowsLimit = int.Parse(ConfigurationManager.AppSettings["ExportMaxRowsLimit"]);
        private string CFG_ExportExcelType = ConfigurationManager.AppSettings["ExportExcelType"];
        private string CFG_PrintPath = ConfigurationManager.AppSettings["PrintPath"];
        private string CFG_ApiVisualPathName = ConfigurationManager.AppSettings["ApiVisualPathName"];
        BaseApp<BaseEntity> _baseApp = new BaseApp<BaseEntity>();


        #region 当前登陆用户
        /// <summary>
        /// 当前登陆用户
        /// </summary>
        protected Sys_User _currentUser
        {
            get
            {
                return this.GetCurrentUser();
            }
        }
        #endregion

        #region 获取当前用户

        /// <summary>
        /// 除了doLogin 之外都可以获取到
        /// </summary>
        /// <returns></returns>
        public Sys_User GetCurrentUser()
        {
            IEnumerable<string> userIDs = new List<string>();
            var canGetUserID = Request.Headers.TryGetValues("Uid", out userIDs);
            if (canGetUserID)
            {
                var account = userIDs.First().ToString();
                return _app.GetUserByAccount(account);
            }
            else
            {
                throw new Exception("当前用户已登出或者登录已过期，请重新登录！");
            }
        }

        #endregion

        #region 通过角色名称判断当前用户是否含有该角色

        /// <summary>
        /// 除了doLogin 之外都可以获取到
        /// </summary>
        /// <returns></returns>
        public bool ContainRoleByRoleName(string roleName)
        {
            Sys_User currentUser = GetCurrentUser();
            return ContainRoleByUserIdAndRoleName(currentUser.UserID, roleName);
        }

        #endregion

        #region 通过用户ID和角色名称判断该用户是否含有该角色

        /// <summary>
        /// 除了doLogin 之外都可以获取到
        /// </summary>
        /// <returns></returns>
        public bool ContainRoleByUserIdAndRoleName(string userId, string roleName)
        {
            List<Sys_Role> roles = _app.GetUserRoles(userId);
            foreach (var sysRole in roles)
            {
                if (sysRole.RoleDesc == roleName)
                {
                    return true;
                }
            }
            return false;
        }
        #endregion

        #region 获取AppID
        /// <summary>
        /// 获取当前请求的客户端ID
        /// </summary>
        /// <returns></returns>
        public string GetRequestClientAppID()
        {
            IEnumerable<string> appIDs = new List<string>();
            var hasAppID = Request.Headers.TryGetValues("AppID", out appIDs);

            if (hasAppID)
            {
                return appIDs.First().ToString();
            }
            else
            {
                throw new Exception("当前请求中未包含AppID信息，请联系开发人员！");
            }

        }


        #endregion
             
        #region 获取系统生成的单号


        /// <summary>
        /// 获取系统生成的单号
        /// </summary>
        /// <param name="docType"></param>
        /// <param name="fixedNum"></param>
        /// <returns></returns>
        public string GenerateDocNum(string docType, string fixedNum)
        {
            return _baseApp.GetNewDocNum(docType, fixedNum);
        }

        #endregion

        #region 后端导出到Excel

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="exportFileName"></param>
        /// <returns></returns>
        public IHttpActionResult ExportToExcelFile(DataTable dt, string exportFileName = "")
        {
            ExcelUtil excelUtil = new ExcelUtil();
            MemoryStream stream = excelUtil.CreateExcel(dt);
            return ResponseMessage(ExportToExcel(stream, exportFileName));
        }



        /// <summary>
        /// 
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <param name="columns"></param>
        /// <param name="exportFileName"></param>
        /// <param name="sheetName"></param>
        /// <returns></returns>
        public IHttpActionResult ExportToExcelFile<T>(List<T> list, List<ExcelColumn<T>> columns, string exportFileName = "", string sheetName = "Sheet1")
        {
            var result = new ResponseData();
            if (list.Count > CFG_ExportMaxRowsLimit)
            {
                result.Code = (int)WMSStatusCode.Failed;
                result.Message = "ui.Sys.Sys_User.UserName";
            }
            else
            {
                ExcelFileType fileType = ExcelFileType.xls;
                switch (CFG_ExportExcelType)
                {
                    case "xls":
                        fileType = ExcelFileType.xls;
                        break;
                    case "xlsx":
                        fileType = ExcelFileType.xlsx;
                        break;
                }

                return ResponseMessage(ExportToExcel(ExcelService.ExportToExcel<T>(list, fileType, sheetName, columns), exportFileName));
            }

            return Json(result);
        }


        private HttpResponseMessage ExportToExcel(MemoryStream stream, string exportFile = "")
        {
            if (string.IsNullOrEmpty(exportFile))
            {
                exportFile = GetCurrentUser().LoginAccount + "_" + DateTime.Now.ToString("yyyyMMddHHmmssfff"); //到毫秒
            }
            if (stream == null)
            {
                return new HttpResponseMessage(HttpStatusCode.NoContent);
            }

            HttpResponseMessage result = new HttpResponseMessage(HttpStatusCode.OK);

            result.Content = new StreamContent(stream, (int)stream.Length);
            result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
            //result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.ms-excel");      // Excel97 - 2003(xls)
            //result.Content.Headers.ContentType = new MediaTypeHeaderValue("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");      // Excel 2007(xlsx)

            //result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment");    //content-disposition
            result.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment");
            result.Content.Headers.ContentDisposition.FileName = System.Web.HttpUtility.UrlEncode(exportFile);    // HttpUtility.UrlEncode(exportFile);        // 中文名注意乱码问题处理
            result.Content.Headers.ContentLength = stream.Length;
            return result;
        }


        #endregion

        #region 后端导出PDF

        /// <summary>
        /// 后端导出PDF
        /// </summary>
        /// <param name="rpt"></param>
        /// <param name="pdfFilePath"></param>
        /// <returns></returns>
        public IHttpActionResult PrintToPDF(DevExpress.XtraReports.UI.XtraReport rpt,string pdfFilePath="")
        {
            var result = new ResponseData();
            string printUrl = "";
            try
            {
                //动态重设数据源
                SqlConnectionStringBuilder scb = new SqlConnectionStringBuilder(DES.Decrypt(ConfigurationManager.ConnectionStrings["DbConnection"].ToString()));
                String strDB = scb.InitialCatalog;
                String strUser = scb.UserID;
                String strPwd = scb.Password;

                DevExpress.DataAccess.Sql.SqlDataSource dataSource = rpt.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;

                if (dataSource != null)
                {
                    DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters connectionParam = dataSource.ConnectionParameters as DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters;
                    connectionParam.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
                    connectionParam.DatabaseName = scb.InitialCatalog;
                    connectionParam.ServerName = scb.DataSource;
                    connectionParam.Password = scb.Password;
                    connectionParam.UserName = scb.UserID;

                    dataSource.ConnectionParameters = connectionParam;
                    //重新执行数据源查询
                    dataSource.Fill();
                }

                
                //
                string printTemplateFile = DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".pdf";
                if (string.IsNullOrEmpty(pdfFilePath))
                {
                    pdfFilePath = HttpContext.Current.Server.MapPath("~/") + CFG_PrintPath + printTemplateFile;
                }



                PdfExportOptions pdfOptions = rpt.ExportOptions.Pdf;
                // 设置导出选项
                pdfOptions.Compressed = true;
                pdfOptions.ImageQuality = PdfJpegImageQuality.High;
                rpt.ExportToPdf(pdfFilePath);

                //if (!string.IsNullOrEmpty(CFG_ApiVisualPathName))
                //{
                //    printUrl = Request.RequestUri.ToString().Replace(Request.RequestUri.PathAndQuery, "") + "/" + CFG_ApiVisualPathName + "/";
                //}
                //else
                //{
                //    printUrl = Request.RequestUri.ToString().Replace(Request.RequestUri.PathAndQuery, "") + "/";
                //}

                //if (!string.IsNullOrEmpty(CFG_ApiVisualPathName))
                //{
                //    printUrl = "/" + CFG_ApiVisualPathName + "/";
                //}


                AOS.Core.Log.LogUtil.WriteLog(Request.RequestUri.AbsoluteUri);
                AOS.Core.Log.LogUtil.WriteLog(Request.RequestUri.LocalPath);

                string webFilePath = printUrl + CFG_PrintPath + printTemplateFile;

                result.Data = new { PrintedPDF= webFilePath.Replace("\\","/") };
                result.Code = (int)WMSStatusCode.Success;
            }
            catch (Exception ex)
            {
                result.Code = (int)WMSStatusCode.UnHandledException;
                result.Message = ex.InnerException?.Message ?? ex.Message;
            }
            return Json(result);
            
        }

        /// <summary>
        /// 后端导出PDF
        /// </summary>
        /// <param name="rpt"></param>
        /// <param name="pdfFilePath"></param>
        /// <returns></returns>
        public string GetPrintPDFPath(DevExpress.XtraReports.UI.XtraReport rpt,string pdfFilePath="")
        {
            string printUrl = "";
            //动态重设数据源
            SqlConnectionStringBuilder scb = new SqlConnectionStringBuilder(DES.Decrypt(ConfigurationManager.ConnectionStrings["DbConnection"].ToString()));
            String strDB = scb.InitialCatalog;
            String strUser = scb.UserID;
            String strPwd = scb.Password;

            DevExpress.DataAccess.Sql.SqlDataSource dataSource = rpt.DataSource as DevExpress.DataAccess.Sql.SqlDataSource;

            if (dataSource != null)
            {
                DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters connectionParam = dataSource.ConnectionParameters as DevExpress.DataAccess.ConnectionParameters.MsSqlConnectionParameters;
                connectionParam.AuthorizationType = DevExpress.DataAccess.ConnectionParameters.MsSqlAuthorizationType.SqlServer;
                connectionParam.DatabaseName = scb.InitialCatalog;
                connectionParam.ServerName = scb.DataSource;
                connectionParam.Password = scb.Password;
                connectionParam.UserName = scb.UserID;
                dataSource.ConnectionParameters = connectionParam;
                //重新执行数据源查询
                dataSource.Fill();
            }
            //
            string printTemplateFile = DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".pdf";
            if (string.IsNullOrEmpty(pdfFilePath))
            {
                pdfFilePath = HttpContext.Current.Server.MapPath("~/") + CFG_PrintPath + printTemplateFile;
            }
            PdfExportOptions pdfOptions = rpt.ExportOptions.Pdf;
            // 设置导出选项
            pdfOptions.Compressed = true;
            pdfOptions.ImageQuality = PdfJpegImageQuality.High;
            rpt.ExportToPdf(pdfFilePath);
            //if (!string.IsNullOrEmpty(CFG_ApiVisualPathName))
            //{
            //    printUrl = "/" + CFG_ApiVisualPathName + "/";
            //}

            AOS.Core.Log.LogUtil.WriteLog(Request.RequestUri.AbsoluteUri);
            AOS.Core.Log.LogUtil.WriteLog(Request.RequestUri.LocalPath);

            string webFilePath = printUrl + CFG_PrintPath + printTemplateFile;
            return webFilePath.Replace("\\", "/");
        }
        #endregion

        #region 根据属性名称以字符串格式获取值
        /// <summary>
        /// 根据属性名称以字符串格式获取值
        /// </summary>
        /// <param name="job"></param>
        /// <param name="propertyName"></param>
        /// <returns></returns>
        public static string getValue(JObject job, string propertyName)
        {
            string result = string.Empty;
            if (job == null || string.IsNullOrEmpty(propertyName)) return result;
            if (job.Property(propertyName) == null) return result;
            return job[propertyName].ToString();
        }

        #endregion

    }
}
