using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;

namespace AOS.SRM.WebAPI.Filter
{
    /// <summary>
    /// 异常处理逻辑（Action执行之前检查）
    /// </summary>
    public class ValidateActionFilterAttribute: ActionFilterAttribute
    {
        /// <summary>
        /// 认证之前执行(参数检查)
        /// 在Action开始时检查ModelState的IsValid属性，如果校验不通过直接返回View
        /// </summary>
        /// <param name="actionContext"></param>
        public override void OnActionExecuting(HttpActionContext actionContext)
        {
            if (!actionContext.ModelState.IsValid)
            {
                actionContext.Response = actionContext.Request.CreateErrorResponse(HttpStatusCode.BadRequest, actionContext.ModelState);
            }
        }
    }
}