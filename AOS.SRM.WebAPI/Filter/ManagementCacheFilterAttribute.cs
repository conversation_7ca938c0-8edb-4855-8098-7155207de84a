using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Runtime.Caching;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;


namespace AOS.SRM.WebAPI.Filter
{
    /// <summary>
    /// 缓存服务--防止重复提交
    /// </summary>
    public class ManagementCacheFilterAttribute : ActionFilterAttribute
    {
        private static readonly ObjectCache WebApiCache = MemoryCache.Default;
        private static readonly List<string> urlList = new List<string>();

        // Whitelist for POST timeout validation
        private static readonly List<string> postWhiteList = new List<string>();


        /// <summary>
        /// Action调用前执行的方法
        /// </summary>
        /// <param name="actionContext"></param>
        public override void OnActionExecuting(HttpActionContext actionContext)
        {
            //初始化 GET拦截校验地址
            if (urlList.Count == 0)
            {
                urlList.Add("/PXC/PurchaseOrder/GetPurchaseOrder");
                urlList.Add("/PXC/PurchaseDeliveryBatch/GetPurchaseDeliveryBatch");
                urlList.Add("/PXC/MakeInspection/GetMakeInspection");
                urlList.Add("/PXC/OutsourcingOrder/GetOutsourcingOrder");
                urlList.Add("/PXC/DeliveryPlan/GetPurchaseDeliveryBatch");
                urlList.Add("/PXC/Inspection/GetMakeInspection");
            }

            //初始化 POST白名单地址 - 这些URL不进行超时校验
            if (postWhiteList.Count == 0)
            {
                // 添加需要白名单的POST请求URL，例如：
                postWhiteList.Add("/Plm/Plm_Blueprint/AcceptBlueprint");
                postWhiteList.Add("/SupplierAudit/GetAuditList");
                // 可以根据需要添加更多白名单URL
            }
            //HttpContextBase context = (HttpContextBase)actionContext.Request.Properties["MS_HttpContext"];//获取传统context
            //HttpRequestBase request = context.Request;//定义传统request对象
            CacheItemPolicy policy = new CacheItemPolicy();
            // actionContext.Request
            string requestUrl = actionContext.Request.RequestUri.ToString();
            string token = actionContext.Request.Headers.SingleOrDefault(x => x.Key.ToLower() == "x-token").Value?.FirstOrDefault();
            string key = getKey(requestUrl, token);
            var tokenCache = WebApiCache.Get(key);
            string requestMethod = actionContext.Request.Method.ToString();
            // 默认请求参数
            string data = "1";
            // 默认禁止时长（秒）
            int second = 3;
            // actionContext.Request.Headers
            bool flag = false;

            if ("POST".Equals(requestMethod.ToUpper()) || "PUT".Equals(requestMethod.ToUpper()) || "DELETE".Equals(requestMethod.ToUpper()))
            {
                // Check if the current URL is in the POST whitelist
                bool isInWhitelist = IsInPostWhitelist(requestUrl);

                // Only apply timeout validation if the URL is not in the whitelist
                if (!isInWhitelist)
                {
                    data = GetDate(actionContext);
                    DateTimeOffset dateTime = DateTimeOffset.Now;
                    second = 20;
                    policy.AbsoluteExpiration = dateTime.AddSeconds(second);
                    flag = true;
                }
            }
            else if ("GET".Equals(requestMethod.ToUpper()) && ifContain(requestUrl))
            {
                DateTimeOffset dateTime = DateTimeOffset.Now;
                policy.AbsoluteExpiration = dateTime.AddSeconds(second);
                flag = true;
            }

            if (flag)
            {
                if (tokenCache == null)
                {
                    WebApiCache.Set(key, data, policy);
                }
                else
                {
                    if (tokenCache.Equals(data))
                    {
                        actionContext.Response =
                            actionContext.Request.CreateErrorResponse(HttpStatusCode.InternalServerError,
                                new HttpError("您点的太快了，请在" + second + "秒后在尝试！"));
                    }
                }
            }
        }

        private bool ifContain(string requestUrl)
        {
            for (var i = 0; i < urlList.Count; i++)
            {
                if (requestUrl.Contains(urlList[i]))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// Check if the URL is in the POST whitelist
        /// </summary>
        /// <param name="requestUrl">The URL to check</param>
        /// <returns>True if the URL is in the whitelist, false otherwise</returns>
        private bool IsInPostWhitelist(string requestUrl)
        {
            for (var i = 0; i < postWhiteList.Count; i++)
            {
                if (requestUrl.Contains(postWhiteList[i]))
                {
                    return true;
                }
            }
            return false;
        }

        private string getKey(string requestUrl, string token)
        {
            return requestUrl + "-" + token;
        }

        public string GetDate(HttpActionContext actionContext)
        {

            var content = JsonConvert.SerializeObject(actionContext.ActionArguments);
            JObject jObject = JObject.Parse(content);
            JToken entity = jObject.GetValue("entity");
            if (entity != null)
            {
                jObject = JObject.Parse(JsonConvert.SerializeObject(entity));
                jObject.Remove("CTime");
                jObject.Remove("MTime");
                content = JsonConvert.SerializeObject(jObject);
            }
            return content;
        }
    }
}