using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using System.Web.Http.Controllers;
using System.Web.Http.Filters;
using AOS.Core.Security;
using AOS.Core.Log;
using Newtonsoft.Json;
using AOS.Core.Utilities;

namespace AOS.SRM.WebAPI.Filter
{
    /// <summary>
    /// WebAPI 统一授权认证
    /// </summary>
    public class HZAuthorizeFilterAttribute : AuthorizationFilterAttribute
    {



        /// <summary>
        /// 
        /// </summary>
        /// <param name="actionContext"></param>
        public override void OnAuthorization(HttpActionContext actionContext)
        {
            //HttpActionContext


            //如果用户方位的Action带有AllowAnonymousAttribute，则不进行授权验证
            if (actionContext.ActionDescriptor.GetCustomAttributes<AllowAnonymousAttribute>().Any())
            {
                return;
            }


            //分别验证在method和controller中的AllowAnonymousAttribute属性
            if (((ReflectedHttpActionDescriptor)actionContext.ActionDescriptor).MethodInfo.IsDefined(typeof(AllowAnonymousAttribute), true)
                    || actionContext.ActionDescriptor.ControllerDescriptor.ControllerType.IsDefined(typeof(AllowAnonymousAttribute), true))
            {
                return;
            }

            //Token验证逻辑(解析用户基本信息,验证签名)
            //var verifyResult = actionContext.Request.Headers.Authorization != null &&  //要求请求中需要带有Authorization头
            //                   actionContext.Request.Headers.Authorization.Parameter == "admin-token"; //并且Authorization参数为admin-token则验证通过

            string error_message = "";

            //LogUtil.WriteLog(JsonConvert.SerializeObject(actionContext.Request.Headers));
            var appId = actionContext.Request.Headers.SingleOrDefault(x => x.Key.ToLower() == "appid").Value?.FirstOrDefault();
            //actionContext.Request.Headers.SingleOrDefault(x => x.Key == "Uid").Value?.First();
            if (string.IsNullOrEmpty(appId))
            {
                actionContext.Response = actionContext.Request.CreateErrorResponse(HttpStatusCode.Unauthorized, new HttpError("请求中未包含AppID信息"));
                return;
            }
            if (appId.ToUpper() != "P0002")  //P0002  来自西子OA的请求
            {
                var token = actionContext.Request.Headers.SingleOrDefault(x => x.Key.ToLower() == "x-token").Value?.FirstOrDefault();
                var verifyResult = IsValidateAuthorizeRequest(token, out error_message);
                if (verifyResult)
                {
                    actionContext.Request.Headers.Add("Uid", TokenUtil.GetUid(token));
                    actionContext.Request.Headers.Add("AppID", appId);
                }
                else
                {
                    //如果验证不通过，则返回401错误，并且Body中写入错误原因
                    actionContext.Response = actionContext.Request.CreateErrorResponse(HttpStatusCode.Unauthorized, new HttpError(error_message));
                }
            }

        }

        private bool IsValidateAuthorizeRequest(string token, out string error_message)
        {
            error_message = "";
            // 1 : 无效Token 2：Token过期 3：Token不正确

            if (token == null)
            {
                error_message = "请求中未包含验证Token信息！";
                return false;
            };

            if (DateTime.Now > TokenUtil.GetExpireTime(token))
            {
                error_message = "Token已失效,请重新登录获取！";
                return false;
            }

            if (!TokenUtil.CheckSign(token))
            {
                error_message = "Token签名错误";
                return false;
            }

            if (!LicenseManager.CheckLicense(out error_message))
            {
                return false;
            }

            return true;
        }

    }
}