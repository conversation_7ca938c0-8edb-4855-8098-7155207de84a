using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Web.Http.Filters;
using AOS.Core;
using AOS.Core.Log;

namespace AOS.SRM.WebAPI.Filter
{
    /// <summary>
    /// 这个Filter是用来进行异常处理的，
    /// 当业务发生未处理的异常，我们是不希望用户接收到黄页或者其他用户无法解析的信息的，我们可以使用ExceptionFilter来进行统一处理
    /// </summary>
    public class APIExceptionFilterAttribute: ExceptionFilterAttribute
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="actionExecutedContext"></param>
        public override void OnException(HttpActionExecutedContext actionExecutedContext)
        {
            //如果截获异常为我们自定义，可以处理的异常则通过我们自己的规则处理
            //if (actionExecutedContext.Exception is BusinessException)
            //{
            //    //TODO:记录日志
            //    actionExecutedContext.Response = actionExecutedContext.Request.CreateResponse(
            //            HttpStatusCode.BadRequest, new { Message = actionExecutedContext.Exception.Message });
            //}
            //else
            //{
            //    //如果截获异常是我没无法预料的异常，则将通用的返回信息返回给用户，避免泄露过多信息，也便于用户处理

            //    //TODO:记录日志
            //    actionExecutedContext.Response =
            //            actionExecutedContext.Request.CreateResponse(HttpStatusCode.InternalServerError,
            //                new { Message = "服务器被外星人拐跑了！" });
            //}


            //LogUtil.WriteLog(actionExecutedContext.Exception.StackTrace.ToString());

            //TODO:记录日志
            actionExecutedContext.Response =
                    actionExecutedContext.Request.CreateResponse(HttpStatusCode.InternalServerError,
                        new { Message = "服务器发生异常了，请联系管理员！" });
        }
    }
}