<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{539182E9-B8C9-44C8-807D-4833D0CA7927}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AOS.SRM.WebAPI</RootNamespace>
    <AssemblyName>AOS.SRM.WebAPI</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode>
    </IISExpressUseClassicPipelineMode>
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>Publish\HZ.WMS.WebAPI.xml</DocumentationFile>
    <IncludeIisSettings>false</IncludeIisSettings>
    <PlatformTarget>x64</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\AOS.SRM.WebAPI.xml</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Chloe">
      <HintPath>..\packages\Chloe.3.9.0\lib\net40\Chloe.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Charts.v16.1.Core">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.Charts.v16.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.CodeParser.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.CodeParser.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.Data.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.DataAccess.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Dev Express 16.1\DevExpress.DataAccess.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Office.v16.1.Core">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.Office.v16.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Pdf.v16.1.Core">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.Pdf.v16.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Pdf.v16.1.Drawing">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.Pdf.v16.1.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.PivotGrid.v16.1.Core">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.PivotGrid.v16.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v16.1.Core">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.Printing.v16.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v16.1.Core, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Dev Express 16.1\DevExpress.Printing.v16.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v16.1.Core">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.RichEdit.v16.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Sparkline.v16.1.Core">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.Sparkline.v16.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.Utils.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v16.1">
      <HintPath>..\packages\Dev Express 16.1\DevExpress.Xpo.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.XtraBars.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.XtraCharts.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.XtraEditors.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGauges.v16.1.Core">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.XtraGauges.v16.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.XtraLayout.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.XtraPrinting.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Dev Express 16.1\DevExpress.XtraPrinting.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.XtraReports.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v16.1, Version=16.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Dev Express 16.1\DevExpress.XtraReports.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v16.1">
      <HintPath>..\..\..\oms\oms-api\DLL\DevExpress.XtraTreeList.v16.1.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.ReportViewer.Common, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1484.0\lib\net40\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.DataVisualization, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1484.0\lib\net40\Microsoft.ReportViewer.DataVisualization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Design, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1484.0\lib\net40\Microsoft.ReportViewer.Design.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.ProcessingObjectModel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1484.0\lib\net40\Microsoft.ReportViewer.ProcessingObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WinForms, Version=15.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.Winforms.150.1484.0\lib\net40\Microsoft.ReportViewer.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.314.76\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib" />
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess, Version=4.122.21.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=MSIL">
      <HintPath>..\packages\Oracle.ManagedDataAccess.21.18.0\lib\net462\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="Quartz">
      <HintPath>..\packages\Quartz.3.0.7\lib\net452\Quartz.dll</HintPath>
    </Reference>
    <Reference Include="sapnco">
      <HintPath>..\..\..\oms\oms-api\DLL\sapnco.dll</HintPath>
    </Reference>
    <Reference Include="sapnco_utils">
      <HintPath>..\..\..\oms\oms-api\DLL\sapnco_utils.dll</HintPath>
    </Reference>
    <Reference Include="SqlSugar, Version=5.1.4.185, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SqlSugar.5.1.4.185\lib\SqlSugar.dll</HintPath>
    </Reference>
    <Reference Include="Swashbuckle.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cd1bb07a5ac7c7bc, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Core.5.6.0\lib\net40\Swashbuckle.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <Private>false</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Formats.Asn1, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Formats.Asn1.8.0.1\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Memory, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Caching" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.6.0.0\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=6.0.0.10, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.6.0.10\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Cors, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.7\lib\net45\System.Web.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Http, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Cors, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Cors.5.2.7\lib\net45\System.Web.Http.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.4\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.4\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.4\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.4\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\AddRequiredHeaderParameter.cs" />
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\SwaggerConfig.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Areas\Common\CommonController.cs" />
    <Compile Include="Areas\CompanyController.cs" />
    <Compile Include="Areas\FO\FinancialReconciliationController.cs" />
    <Compile Include="Areas\FO\ReconciliationController.cs" />
    <Compile Include="Areas\HelpPage\ApiDescriptionExtensions.cs" />
    <Compile Include="Areas\HelpPage\App_Start\HelpPageConfig.cs" />
    <Compile Include="Areas\HelpPage\Controllers\HelpController.cs" />
    <Compile Include="Areas\HelpPage\HelpPageAreaRegistration.cs" />
    <Compile Include="Areas\HelpPage\HelpPageConfigurationExtensions.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\CollectionModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ComplexTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\DictionaryModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\EnumValueDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\IModelDocumentationProvider.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\KeyValuePairModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelDescriptionGenerator.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameAttribute.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ModelNameHelper.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterAnnotation.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\ParameterDescription.cs" />
    <Compile Include="Areas\HelpPage\ModelDescriptions\SimpleTypeModelDescription.cs" />
    <Compile Include="Areas\HelpPage\Models\HelpPageApiModel.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\HelpPageSampleKey.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ImageSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\InvalidSample.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\ObjectGenerator.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\SampleDirection.cs" />
    <Compile Include="Areas\HelpPage\SampleGeneration\TextSample.cs" />
    <Compile Include="Areas\HelpPage\XmlDocumentationProvider.cs" />
    <Compile Include="Areas\IX\IX_NoticeController.cs" />
    <Compile Include="Areas\IX\IX_NoticeReceiptController.cs" />
    <Compile Include="Areas\Plm\Controllers\GetMatnrBase.cs" />
    <Compile Include="Areas\Plm\Controllers\Plm_BlueprintController.cs" />
    <Compile Include="Areas\Plm\SysAreaRegistration.cs" />
    <Compile Include="Areas\PUB\PUBAreaRegistration.cs" />
    <Compile Include="Areas\PXC\BlueprintAccept\BlueprintAcceptController.cs" />
    <Compile Include="Areas\PXC\LogisticsIOrder\LogisticsIOrderController.cs" />
    <Compile Include="Areas\PXC\PurchaseOrder\MakeInspectionController.cs" />
    <Compile Include="Areas\PXC\OutsourcingProcurement\DeliveryPlanController.cs" />
    <Compile Include="Areas\PXC\OutsourcingProcurement\InspectionController.cs" />
    <Compile Include="Areas\PXC\OutsourcingProcurement\OutsourcingOrderController.cs" />
    <Compile Include="Areas\PXC\P_Blueprint_Non_StandardController.cs" />
    <Compile Include="Areas\PXC\P_Blueprint_Send_RecordController.cs" />
    <Compile Include="Areas\PXC\PurchaseOrder\PurchaseDeliveryBatchController.cs" />
    <Compile Include="Areas\PXC\PurchaseOrder\PurchaseOrderController.cs" />
    <Compile Include="Areas\PXC\PurchaseRequest\PurchaseRequestController.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_InvoiceDetailsController.cs" />
    <Compile Include="Areas\RPT\Controllers\PurchaseDeliveryController.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_FTT_ViewController.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_PLineOutPutController.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_PO_InOut_ViewController.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_PO_Inspection_ViewController.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_SD_Delivery_ViewController.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_StockMove_ViewController.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_SupplierItem_ViewController.cs" />
    <Compile Include="Areas\RPT\Controllers\RPT_V_NotPostStockMoveController.cs" />
    <Compile Include="Areas\RPT\RPTAreaRegistration.cs" />
    <Compile Include="Areas\SAP\XZSAPController.cs" />
    <Compile Include="Areas\SHM\DisposalManagement\CompromiseDisposalController.cs" />
    <Compile Include="Areas\SHM\DisposalManagement\RRDFController.cs" />
    <Compile Include="Areas\SHM\DisposalManagement\DebitNoticeController.cs" />
    <Compile Include="Areas\SHM\DisposalManagement\UnqualifiedNoticeController.cs" />
    <Compile Include="Areas\SHM\SupplierAuditController.cs" />
    <Compile Include="Areas\SHM\SupplierInfoController.cs" />
    <Compile Include="Areas\SHM\S_ContractController.cs" />
    <Compile Include="Areas\SHM\S_DisqualifySupplyController.cs" />
    <Compile Include="Areas\SHM\S_DrawingDistributionRecyclingController.cs" />
    <Compile Include="Areas\SHM\S_EquipPurchaseController.cs" />
    <Compile Include="Areas\SHM\S_PerformanceAppraisalBaseController.cs" />
    <Compile Include="Areas\SHM\S_PerformanceAppraisalController.cs" />
    <Compile Include="Areas\SHM\S_ResumeSupplyQualificationController.cs" />
    <Compile Include="Areas\SHM\S_StandardsOfBankController.cs" />
    <Compile Include="Areas\SHM\S_TechnicalNoticeController.cs" />
    <Compile Include="Areas\SHM\S_SupplierChangeController.cs" />
    <Compile Include="Areas\SHM\S_SupplierWarningLetterController.cs" />
    <Compile Include="Areas\Store\StoreSupplierController.cs" />
    <Compile Include="Areas\Sys\Controllers\MD_AttachmentManagementController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_ApiLogConfigController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_AppVersionController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_DbBackupConfigController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_DbBackupController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_JobController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_LogController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MailController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MailServerConfigController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MessageController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MessageNotifySettingController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_MessageTypeController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_OrganizationController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_ResourceController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_RoleController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_RoleResourceController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_SwithConfigController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_DictionaryController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_UserController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_UserMessageController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_UserRoleController.cs" />
    <Compile Include="Areas\Sys\Controllers\Sys_UserSapAccountController.cs" />
    <Compile Include="Areas\Sys\SysAreaRegistration.cs" />
    <Compile Include="Areas\WMS\XZWMSController.cs" />
    <Compile Include="Controllers\ApiBaseController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\ValuesController.cs" />
    <Compile Include="Filter\HZAuthorizeFilterAttribute.cs" />
    <Compile Include="Filter\APIExceptionFilterAttribute.cs" />
    <Compile Include="Filter\ApiLoggingFilterAttribute.cs" />
    <Compile Include="Filter\ManagementCacheFilterAttribute.cs" />
    <Compile Include="Filter\ValidateActionFilterAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SqlServerTypes\Loader.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Areas\HelpPage\HelpPage.css" />
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Global.asax" />
    <Content Include="Oracle.DataAccess.Common.Configuration.Section.xsd" />
    <Content Include="Oracle.ManagedDataAccess.Client.Configuration.Section.xsd" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Areas\HelpPage\Views\Web.config" />
    <Content Include="Areas\HelpPage\Views\Shared\_Layout.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\ResourceModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Index.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\TextSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\SimpleTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Samples.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\Parameters.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ModelDescriptionLink.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\KeyValuePairModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\InvalidSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ImageSample.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\HelpPageApiModel.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\EnumTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\DictionaryModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ComplexTypeModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\CollectionModelDescription.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\DisplayTemplates\ApiGroup.cshtml" />
    <Content Include="Areas\HelpPage\Views\Help\Api.cshtml" />
    <Content Include="Areas\Sys\Views\web.config" />
    <Content Include="Areas\RPT\Views\web.config" />
    <Content Include="Areas\PUB\Views\web.config" />
    <Content Include="Config\log4net.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="PDAUpdate\FaradyneWMS_PDA%28v1.0.2%29.apk" />
    <Content Include="libman.json" />
    <None Include="Properties\PublishProfiles\CustomProfile.pubxml" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <None Include="Scripts\jquery-3.3.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.3.1.js" />
    <Content Include="Scripts\jquery-3.3.1.min.js" />
    <Content Include="Scripts\jquery-3.3.1.slim.js" />
    <Content Include="Scripts\jquery-3.3.1.slim.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Areas\HelpPage\Views\_ViewStart.cshtml" />
    <Content Include="Content\Site.css" />
    <Content Include="Views\Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Areas\IX\Views\IX_NoticeReceipt\" />
    <Folder Include="Areas\PUB\Controllers\" />
    <Folder Include="Areas\PUB\Models\" />
    <Folder Include="Areas\PUB\Views\Shared\" />
    <Folder Include="Areas\RPT\Models\" />
    <Folder Include="Areas\RPT\Views\RPT_FTT_View\" />
    <Folder Include="Areas\RPT\Views\Shared\" />
    <Folder Include="Areas\SHM\Views\S_ResumeSupplyQualification\" />
    <Folder Include="Areas\Sys\Models\" />
    <Folder Include="Areas\Sys\Views\Shared\" />
    <Folder Include="LabelTemplate\" />
    <Folder Include="Models\" />
    <Folder Include="PrintTempPath\" />
    <Folder Include="UploadFiles\LabelTemplate\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap.min.css.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap.css.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap-theme.min.css.map" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Content\bootstrap-theme.css.map" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <Content Include="Scripts\jquery-3.3.1.slim.min.map" />
    <Content Include="Scripts\jquery-3.3.1.min.map" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AOS.Core\AOS.Core.csproj">
      <Project>{62d9f685-5537-494e-8d51-4daf877f8af1}</Project>
      <Name>AOS.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\AOS.SRM.Application\AOS.SRM.Application.csproj">
      <Project>{28dbe3a0-f19a-49e4-9cfe-6f8177cb17bf}</Project>
      <Name>AOS.SRM.Application</Name>
    </ProjectReference>
    <ProjectReference Include="..\AOS.SRM.Entity\AOS.SRM.Entity.csproj">
      <Project>{A56C0618-C820-42E7-89B5-E5DA01C72729}</Project>
      <Name>AOS.SRM.Entity</Name>
    </ProjectReference>
    <ProjectReference Include="..\AOS.SRM.WebJob\AOS.SRM.WebJob.csproj">
      <Project>{E5C0D604-9C02-4FA6-BEFB-6126AAE73651}</Project>
      <Name>AOS.SRM.WebJob</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>8085</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl></IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>http://192.168.3.68/63226</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>