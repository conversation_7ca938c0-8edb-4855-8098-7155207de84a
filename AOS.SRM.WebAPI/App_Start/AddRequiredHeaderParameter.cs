using Swashbuckle.Swagger;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http.Description;

namespace AOS.SRM.WebAPI
{
    /// <summary>
    /// 让swagger支持修改Http Header中的参数
    /// </summary>
    public class AddRequiredHeaderParameter : IOperationFilter
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="operation"></param>
        /// <param name="schemaRegistry"></param>
        /// <param name="apiDescription"></param>
        public void Apply(Operation operation, SchemaRegistry schemaRegistry, ApiDescription apiDescription)
        {
            if (operation.parameters == null)
                operation.parameters = new List<Parameter>();

            // 目前支持当前登录用户Id，有需要继续添加
            operation.parameters.Add(new Parameter
            {
                name = "UserId",
                @in = "header",
                type = "string",
                description = "Current User Id",
                required = true
            });
        }
    }
}