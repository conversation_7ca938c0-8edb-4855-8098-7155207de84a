using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web.Http;
using System.Web.Http.Cors;
using AOS.SRM.WebAPI.Filter;

namespace AOS.SRM.WebAPI
{
    /// <summary>
    /// 
    /// </summary>
    public static class WebApiConfig
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="config"></param>
        public static void Register(HttpConfiguration config)
        {
            // Web API 配置和服务

            // Web API 路由
            config.MapHttpAttributeRoutes();

            //注册全局Filter
            config.Filters.Add(new HZAuthorizeFilterAttribute());
            //操作日志filter
            config.Filters.Add(new ApiLoggingFilterAttribute());

            config.Filters.Add(new ManagementCacheFilterAttribute());

            config.Filters.Add(new APIExceptionFilterAttribute());

            //config.Services.Add(typeof(IExceptionLogger), new ErrorLogger());
            //config.Services.Replace(typeof(IExceptionHandler), new ErrorHandler());

            //WebAPI跨域访问支持
            //config.EnableCors(new EnableCorsAttribute("*", "*", "*"));
            var allowOrigins = ConfigurationManager.AppSettings["cors_allowOrigins"];
            var allowHeaders = ConfigurationManager.AppSettings["cors_allowHeaders"];
            var allowMethods = ConfigurationManager.AppSettings["cors_allowMethods"];
            var globalCors = new EnableCorsAttribute(allowOrigins, allowHeaders, allowMethods,"Content-Disposition");
            config.EnableCors(globalCors);

            //时区设置为当地时区
            var json = GlobalConfiguration.Configuration.Formatters.JsonFormatter;
            json.Indent = false;
            json.SerializerSettings.DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Local;

            

            // 修改默认业务路由规则
            config.Routes.MapHttpRoute(
                name: "DefaultApi",
                routeTemplate: "api/{area}/{controller}/{action}/{id}",        //追加Action寻找路由  张旭
                defaults: new { id = RouteParameter.Optional }
            );

            //添加Token路由
            config.Routes.MapHttpRoute(
                name: "TokenApi",
                routeTemplate: "api/{controller}/{action}/{id}",        //追加Action寻找路由  张旭
                defaults: new { id = RouteParameter.Optional }
            );

        }
    }
}
