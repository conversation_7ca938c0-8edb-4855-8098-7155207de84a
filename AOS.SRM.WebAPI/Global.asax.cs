using System.Web.Http;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using System.Threading.Tasks;
using System.Configuration;
using AOS.SRM.WebJob;

namespace AOS.SRM.WebAPI
{
    /// <summary>
    /// 
    /// </summary>
    public class WebApiApplication : System.Web.HttpApplication
    {
        /// <summary>
        ///
        /// </summary>
        protected void Application_Start()
        {
            AreaRegistration.RegisterAllAreas();
            GlobalConfiguration.Configure(WebApiConfig.Register);
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);
            //log4net
            log4net.Config.XmlConfigurator.Configure();

            // 启动定时任务调度器
            Task.Run(async () =>
            {
                try
                {
                    await JobScheduler.Start();
                }
                catch (System.Exception ex)
                {
                    // 记录启动失败的日志
                    var logger = log4net.LogManager.GetLogger(typeof(WebApiApplication));
                    logger.Error("启动定时任务调度器失败", ex);
                }
            });
        }

        /// <summary>
        /// 应用程序结束时停止调度器
        /// </summary>
        protected void Application_End()
        {
            Task.Run(async () =>
            {
                try
                {
                    await JobScheduler.Stop();
                }
                catch (System.Exception ex)
                {
                    // 记录停止失败的日志
                    var logger = log4net.LogManager.GetLogger(typeof(WebApiApplication));
                    logger.Error("停止定时任务调度器失败", ex);
                }
            });
        }
    }
}
