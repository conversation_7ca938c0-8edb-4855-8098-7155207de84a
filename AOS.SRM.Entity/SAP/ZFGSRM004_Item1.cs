using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    /// 预制发票过账_明细
    /// </summary>
    public class ZFGSRM004_Item1
    {
        /// <summary>
        /// 发票凭证中的凭证项目
        /// </summary>
        public int? RBLGP { get; set; }
        /// <summary>
        /// 总账科目  7001350100:处置单    6401080000：年价金额差异调整
        /// </summary>
        public string HKONT { get; set; }
        /// <summary>
        /// 销售/购买税代码  默认 J0
        /// </summary>
        public string MWSKZ { get; set; }
        /// <summary>
        /// 凭证货币金额   
        /// </summary>
        public decimal? WRBTR { get; set; }
        /// <summary>
        /// 借方/贷方标识 S:借方、奖励 H：贷方、扣款
        /// </summary>
        public string SHKZG { get; set; }
    }
}
