using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
   /// <summary>
   /// 生成采购收货单接口类_明细行
   /// </summary>
    public class ZFGSRM003_Item
    {
        /// <summary>
        /// 采购凭证的项目编号 
        /// </summary>
        public int? EBELP { get; set; }
        /// <summary>
        /// 科目分配类别
        /// </summary>
        public string KNTTP { get; set; }
        /// <summary>
        /// 采购凭证中的项目类别
        /// </summary>
        public string PSTYP { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        public string MATNR { get; set; }
        /// <summary>
        /// 短文本 
        /// </summary>
        public string TXZ01 { get; set; }
        /// <summary>
        /// 数量 
        /// </summary>
        public decimal? MENGE { get; set; }
        /// <summary>
        /// 基本计量单位 
        /// </summary>
        public string MEINS { get; set; }
        /// <summary>
        /// 交货日期 
        /// </summary>
        public string EEIND { get; set; }
        /// <summary>
        /// 总账科目编号 
        /// </summary>
        public string SAKTO { get; set; }
        /// <summary>
        /// 成本中心 
        /// </summary>
        public string KOSTL { get; set; }
        /// <summary>
        /// 订单号 
        /// </summary>
        public string AUFNR { get; set; }
        /// <summary>
        /// 主资产号 
        /// </summary>
        public string ANLN1 { get; set; }
        /// <summary>
        /// 销售/购买税代码  
        /// </summary>
        public string MWSKZ { get; set; }
        /// <summary>
        /// 净价
        /// </summary>
        public decimal? NETPR { get; set; }
        /// <summary>
        /// 价格单位
        /// </summary>
        public string PEINH { get; set; }
        /// <summary>
        /// 物料组
        /// </summary>
        public string MATKL { get; set; }
        /// <summary>
        /// 库存地点
        /// </summary>
        public string LGORT { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        public string WERKS { get; set; }
        /// <summary>
        /// 需求者/要求者名称 
        /// </summary>
        public string AFNAM { get; set; }
        /// <summary>
        /// 销售订单号
        /// </summary>
        public string VBELN { get; set; }
        /// <summary>
        /// 销售凭证项目
        /// </summary>
        public int? POSNR { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string ZTEXT { get; set; }
    }
}
