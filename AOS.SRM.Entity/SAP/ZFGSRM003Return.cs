using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    /// ZFGSRM003Return返回值
    /// </summary>
    public class ZFGSRM003Return
    {
        /// <summary>
        /// 过账行号
        /// </summary>
        public int? PostLine { get; set; }
        /// <summary>
        /// 采购凭证号
        /// </summary>
        public string SAPDocNum { get; set; }
        /// <summary>
        /// 采购凭证的项目编号
        /// </summary>
        public int? SAPLine { get; set; }
        /// <summary>
        /// 物料凭证编号
        /// </summary>
        public string SAPMaterialNum { get; set; }
        /// <summary>
        /// 物料凭证中的项目
        /// </summary>
        public int? SAPMaterialLine { get; set; }
    }
}
