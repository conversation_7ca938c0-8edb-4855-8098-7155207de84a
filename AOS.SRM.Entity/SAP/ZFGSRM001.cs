using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    /// 供应商信息同步实体类
    /// </summary>
    public class ZFGSRM001
    {
        /// <summary>
        /// 供应商或债权人的账号
        /// </summary>
        public string LIFNR { get; set; }
        /// <summary>
        /// 名称1
        /// </summary>
        public string NAME1 { get; set; }
        /// <summary>
        /// 供应商全称2
        /// </summary>
        public string NAME2 { get; set; }
        /// <summary>
        /// 供应商简称
        /// </summary>
        public string SORTL { get; set; }
        /// <summary>
        /// 供应商账户组
        /// </summary>
        public string KTOKK { get; set; }
        /// <summary>
        /// 标题
        /// </summary>
        public string ANRED { get; set; }
        /// <summary>
        /// 国家/地区代码
        /// </summary>
        public string LAND1 { get; set; }
        /// <summary>
        /// 城市
        /// </summary>
        public string ORT01 { get; set; }
        /// <summary>
        /// 街道和房屋号
        /// </summary>
        public string STRAS { get; set; }
        /// <summary>
        /// 地区
        /// </summary>
        public string ORT02 { get; set; }
        /// <summary>
        /// 供应商语言
        /// </summary>
        public string SPRAS { get; set; }
        /// <summary>
        /// 邮政编码
        /// </summary>
        public string PSTLZ { get; set; }
        /// <summary>
        /// 第一个电话号
        /// </summary>
        public string TELF1 { get; set; }
        /// <summary>
        /// 第二个电话号
        /// </summary>
        public string TELF2 { get; set; }
        /// <summary>
        /// 传真号
        /// </summary>
        public string TELFX { get; set; }
        /// <summary>
        /// 电子信箱号
        /// </summary>
        public string TELBX { get; set; }
        /// <summary>
        /// 税务登记号
        /// </summary>
        public string STENR { get; set; }
        /// <summary>
        /// 法人
        /// </summary>
        public string KOINH { get; set; }
        /// <summary>
        /// 银行名称
        /// </summary>
        public string BANKA { get; set; }
        /// <summary>
        /// 银行国家代码
        /// </summary>
        public string BANKS { get; set; }
        /// <summary>
        /// 银行账户号码
        /// </summary>
        public string BANKN { get; set; }
        /// <summary>
        /// 主记录的中心删除冻结
        /// </summary>
        public string NODEL { get; set; }
        /// <summary>
        /// 采购组织
        /// </summary>
        public string EKORG { get; set; }
        /// <summary>
        /// 货币码
        /// </summary>
        public string WAERS { get; set; }
        /// <summary>
        /// 付款条件代码
        /// </summary>
        public string ZTERM { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        public string BUKRS { get; set; }
        /// <summary>
        /// 统驭科目
        /// </summary>
        public string AKONT { get; set; }
        /// <summary>
        /// 标识：基于收货的发票验证
        /// </summary>
        public string WEBRE { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string REMARK { get; set; }
    }
}
