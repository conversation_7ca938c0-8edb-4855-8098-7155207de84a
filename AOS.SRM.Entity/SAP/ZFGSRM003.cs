using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    /// 生成采购收货单接口类
    /// </summary>
    public class ZFGSRM003
    {
        /// <summary>
        /// 凭证中的凭证日期
        /// </summary>
        public DateTime? BLDAT { get; set; }
        /// <summary>
        /// 凭证中的过账日期
        /// </summary>
        public DateTime? BUDAT { get; set; }
        /// <summary>
        /// 订单类型（采购）
        /// </summary>
        public string BSART { get; set; }
        /// <summary>
        /// 供应商或债权人的账号
        /// </summary>
        public string LIFNR { get; set; }
        /// <summary>
        /// 采购组织
        /// </summary>
        public string EKORG { get; set; }
        /// <summary>
        /// 采购组
        /// </summary>
        public string EKGRP { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        public string BUKRS { get; set; }
        /// <summary>
        /// 明细行
        /// </summary>
        public List<ZFGSRM003_Item> Items{get;set;}
    }
}
