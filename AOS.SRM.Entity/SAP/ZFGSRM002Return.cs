using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    /// 获取采购价格
    /// </summary>
    public class ZFGSRM002Return
    {
        ///// <summary>
        ///// 采购收货主键Id
        ///// </summary>
        //public string PurchaseReceiptID { get; set; }
        /// <summary>
        /// 结算单价
        /// </summary>
        public decimal? SettleUnitPrice { get; set; }
        ///// <summary>
        ///// 结算金额
        ///// </summary>
        //public decimal? SettlementPrice { get; set; }
    }
}
