using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    /// 采购申请
    /// </summary>
    public class ZFGSRM005Return
    {
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料描述
        /// </summary>
        public string ItemName { get; set; }
        /// <summary>
        /// 交货日期
        /// </summary>
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Qty { get; set; }
        /// <summary>
        /// 基本计量单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 刷字
        /// </summary>
        public string BrushingWords { get; set; }
    }
}
