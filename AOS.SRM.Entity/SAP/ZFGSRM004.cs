using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    /// 预制发票过账
    /// </summary>
    public class ZFGSRM004
    {
        /// <summary>
        /// SRM发票号
        /// </summary>
        public string ZID { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        public string BUKRS { get; set; }
        /// <summary>
        /// 供应商代码
        /// </summary>
        public string LIFNR { get; set; }
        /// <summary>
        /// 发票总额
        /// </summary>
        public decimal? RMWWR { get; set; }
        /// <summary>
        /// 文本
        /// </summary>
        public string SGTXT { get; set; }
        /// <summary>
        /// 发票日期
        /// </summary>
        public DateTime? BLDAT { get; set; }
        /// <summary>
        /// 发票基准日
        /// </summary>
        public DateTime? ZFBDT { get; set; }
        /// <summary>
        /// 发票过账日期
        /// </summary>
        public DateTime? BUDAT { get; set; }
        /// <summary>
        /// 贷项凭证 标识   空:预制发票   X:贷方凭证
        /// </summary>
        public string ZDXPZ { get; set; }
        /// <summary>
        /// 付款条件
        /// </summary>
        public string ZTERM { get; set; }
        /// <summary>
        /// 行信息
        /// </summary>
        public List<ZFGSRM004_Item> Items { get; set; }
        /// <summary>
        /// 折扣
        /// </summary>
        public List<ZFGSRM004_Item1> Items1 { get; set; }
    }
}
