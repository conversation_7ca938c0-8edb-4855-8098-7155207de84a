using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 
    /// </summary>
    public class ZFGSRM002
    {
        /// <summary>
        /// 采购信息记录号
        /// </summary>
        public string INFNR { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        public string MATNR { get; set; }
        /// <summary>
        /// 供应商帐户号
        /// </summary>
        public string LIFNR { get; set; }
        /// <summary>
        /// 采购组织
        /// </summary>
        public string EKORG { get; set; }
        /// <summary>
        /// 采购信息记录分类
        /// </summary>
        public string ESOKZ { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        public string WERKS { get; set; }
        /// <summary>
        /// 业务状态码
        /// </summary>
        public string ZZSTACODE { get; set; }
        /// <summary>
        /// 采购组
        /// </summary>
        public string EKGRP { get; set; }
        /// <summary>
        /// 不存在等级的条件金额或百分比
        /// </summary>
        public decimal KBETR { get; set; }
        /// <summary>
        /// 条件单位
        /// </summary>
        public string KMEIN { get; set; }
        /// <summary>
        /// 条件定价单位
        /// </summary>
        public string KPEIN { get; set; }
        /// <summary>
        /// 条件单位（货币或百分比）
        /// </summary>
        public string KONWA { get; set; }
        /// <summary>
        /// 销售/购买税代码 
        /// </summary>
        public string MWSKZ { get; set; }
        /// <summary>
        /// 条件记录有效起始日 
        /// </summary>
        public DateTime DATAB { get; set; }
        /// <summary>
        /// 条件记录有效截止日期 
        /// </summary>
        public DateTime DATBI { get; set; }
        /// <summary>
        /// 计划交货时间（天）  
        /// </summary>
        public string APLFZ { get; set; }
        /// <summary>
        /// 暂估价 
        /// </summary>
        public string ZZZGJ { get; set; }
        /// <summary>
        /// 标识：基于收货的发票验证 
        /// </summary>
        public string WEBRE { get; set; }
        /// <summary>
        /// 查询日期 
        /// </summary>
        public DateTime ZSRRQ { get; set; }
    }
}
