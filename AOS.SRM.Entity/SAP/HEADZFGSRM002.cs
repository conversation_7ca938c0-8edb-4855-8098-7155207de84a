using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    /// 获取结算价格
    /// </summary>
    public class HEADZFGSRM002
    {
        /// <summary>
        /// 物料编号
        /// </summary>
        public string MATNR { get; set; }
        /// <summary>
        /// 供应商帐户号
        /// </summary>
        public string LIFNR { get; set; }
        /// <summary>
        /// 采购组织
        /// </summary>
        public string EKORG { get; set; }
        /// <summary>
        /// 采购信息记录分类
        /// </summary>
        public string ESOKZ { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        public string WERKS { get; set; }
        /// <summary>
        /// 业务状态码
        /// </summary>
        public string ZZSTACODE { get; set; }
        /// <summary>
        /// 暂估价
        /// </summary>
        public string ZZZGJ { get; set; }
        /// <summary>
        /// 查询日期
        /// </summary>
        public DateTime ZSRRQ { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary>
        public string EBELN { get; set; }
        /// <summary>
        /// 采购单行号
        /// </summary>
        public int? EBELP { get; set; }
    }
}
