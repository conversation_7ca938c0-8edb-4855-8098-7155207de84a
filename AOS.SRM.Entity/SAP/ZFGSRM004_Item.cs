using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    /// 预制发票过账_明细
    /// </summary>
    public class ZFGSRM004_Item
    {
        /// <summary>
        /// 发票凭证中的凭证项目
        /// </summary>
        public int? RBLGP { get; set; }
        /// <summary>
        /// 采购凭证号
        /// </summary>
        public string EBELN { get; set; }
        /// <summary>
        /// 凭证项目号
        /// </summary>
        public int? EBELP { get; set; }
        /// <summary>
        /// 物料凭证号
        /// </summary>
        public string LFBNR { get; set; }
        /// <summary>
        /// 物料凭证项目
        /// </summary>
        public int? LFPOS { get; set; }
        /// <summary>
        /// 物料号
        /// </summary>
        public string MATNR { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal? MENGE { get; set; }
        /// <summary>
        /// 基本计量单位
        /// </summary>
        public string MEINS { get; set; }
        /// <summary>
        /// 发票金额
        /// </summary>
        public decimal? SMWWR { get; set; }
    }
}
