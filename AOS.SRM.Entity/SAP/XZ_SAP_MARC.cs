using System.ComponentModel;

namespace AOS.SRM.Entity.SAP
{
    /// <summary>
    ///SAP中间库-物料主数据
    /// </summary>
    public class XZ_SAP_MARC
    {
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string WERKS { get; set; }
        
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MATNR { get; set; }
        
        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MAKTX { get; set; } 
        
        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool Status { get; set; } 
        
        /// <summary>
        /// 跨工厂物料状态
        /// </summary>
        [Description("跨工厂物料状态")]
        public string MSTAE { get; set; } 
    }
}
