using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.Enum
{
    /// <summary>
    /// 业务模块 
    /// 增加的时候需要更新附件的方法
    /// </summary>
    public enum BusinessTypeEnum
    {
        /// <summary>
        /// 供应商注册
        /// </summary>
        [Description("供应商注册")]
        Register = 0,
        /// <summary>
        /// 供应商准入
        /// </summary>
        [Description("供应商准入")]
        Access = 1,
        /// <summary>
        /// 变更申请附件
        /// </summary>
        [Description("变更申请明细")]
        Change = 2,
        /// <summary>
        /// 公告附件
        /// </summary>
        [Description("公告附件")]
        Announce = 3,
        /// <summary>
        /// 公告回执附件
        /// </summary>
        [Description("公告回执附件")]
        Receipt = 4,
        /// <summary>
        /// 合同附件
        /// </summary>
        [Description("合同附件")]
        Contract = 5,
        /// <summary>
        /// 立项资料
        /// </summary>
        [Description("立项资料")]
        ProjectData = 6,
        /// <summary>
        /// 寻源资料
        /// </summary>
        [Description("寻源资料")]
        FindSource = 7,
        /// <summary>
        /// 技术答疑资料
        /// </summary>
        [Description("技术答疑资料")]
        AnswerQuestions = 8,
        /// <summary>
        /// 招标资料
        /// </summary>
        [Description("招标资料")]
        Bidding = 9,
        /// <summary>
        /// 技术通知单
        /// </summary>
        [Description("技术通知单")]
        TechnicalNotice = 10,
        /// <summary>
        /// 国行企标准
        /// </summary>
        [Description("国行企标准")]
        StandardsOfBank = 11,
    }
}
