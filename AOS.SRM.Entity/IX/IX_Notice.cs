using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.IX
{
    /// <summary>
    /// 信息往来-公告
    /// </summary>
    [SugarTable("IX_Notice")]
    public class IX_Notice : BaseEntity
    {
        ///<summary>
        ///主键
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键")]
        public string Id { get; set; }

        ///<summary>
        ///公告编号
        ///</summary>
        public string DocNum { get; set; }

        ///<summary>
        ///发起人
        ///</summary>
        public string Sponsor { get; set; }

        ///<summary>
        ///发起部门
        ///</summary>
        public string Dept { get; set; }

        ///<summary>
        ///公告内容
        ///</summary>
        public string StrContent { get; set; }

        ///<summary>
        ///状态 0：未发布 1：已发布
        ///</summary>
        public string Status { get; set; }

        ///<summary>
        ///发布时间
        ///</summary>
        public DateTime? ReleaseTime { get; set; }

        ///<summary>
        ///已读次数
        ///</summary>
        public int? ReadCount { get; set; }

        ///<summary>
        ///是否需要回执
        ///</summary>
        public string IsReceipt { get; set; }

        /// <summary>
        /// 附件Id
        /// </summary>
        public string AttachId { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; }
    }
}