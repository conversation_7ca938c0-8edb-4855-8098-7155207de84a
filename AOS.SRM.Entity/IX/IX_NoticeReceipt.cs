using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.IX
{
    /// <summary>
    /// 信息往来-公告回执
    /// </summary>
    [SugarTable("IX_NoticeReceipt")]
    public class IX_NoticeReceipt : BaseEntity
    {
        ///<summary>
        ///主键
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键")]
        public string Id { get; set; }
        ///<summary>
        ///公告编号
        ///</summary>
        public string DocNum { get; set; }
        ///<summary>
        ///供应商编码
        ///</summary>
        public string SupplierCode { get; set; }
        ///<summary>
        ///供应商名称
        ///</summary>
        public string SupplierName { get; set; }
        ///<summary>
        ///附件Id
        ///</summary>
        public string AttachId { get; set; }
    }
}
