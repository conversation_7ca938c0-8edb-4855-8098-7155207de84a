using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_UserMessage")]
    public class Sys_UserMessage : BaseEntity
    {
        /// <summary>
        /// 用户消息ID
        /// </summary>
        [Description("用户消息ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string UserMessageID { get; set; }

        /// <summary>
        /// 消息ID
        /// </summary>
        [Description("消息ID")]
        public string MessageID { get; set; }

        /// <summary>
        /// 消息分类ID
        /// </summary>
        [Description("消息分类ID")]
        public string MessageTypeID { get; set; }

        /// <summary>
        /// 消息分类描述
        /// </summary>
        [Description("消息分类描述")]
        public string MessageTypeDesc { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Description("用户ID")]
        public string UserID { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        [Description("用户姓名")]
        public string UserName { get; set; }

        /// <summary>
        /// 消息标题
        /// </summary>
        [Description("消息标题")]
        public string MessageTitle { get; set; }

        /// <summary>
        /// 消息正文
        /// </summary>
        [Description("消息正文")]
        public string MessageContent { get; set; }

        /// <summary>
        /// 通知方式
        /// </summary>
        [Description("通知方式")]
        public string NotifyType { get; set; }

        /// <summary>
        /// 消息发布者
        /// </summary>
        [Description("消息发布者")]
        public string Publisher { get; set; }

        /// <summary>
        /// 消息发布时间
        /// </summary>
        [Description("消息发布时间")]
        public DateTime? PublishTime { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public bool? IsReaded { get; set; }

        /// <summary>
        /// 用户阅读时间
        /// </summary>
        [Description("用户阅读时间")]
        public DateTime? ReadTime { get; set; }
    }
}