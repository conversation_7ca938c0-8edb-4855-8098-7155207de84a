using System.ComponentModel;
using SqlSugar;


namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_Organization")]
    public class Sys_Organization : BaseEntity
    {
        /// <summary>
        /// 机构ID
        /// </summary>
        [Description("机构ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string OrganizationID { get; set; }

        /// <summary>
        /// 上级机构ID
        /// </summary>
        [Description("上级机构ID")]
        public string FathOrganizationID { get; set; }

        /// <summary>
        /// 机构编号
        /// </summary>
        [Description("机构编号")]
        public string OrganizationCode { get; set; }

        /// <summary>
        /// 机构层级
        /// </summary>
        [Description("机构层级")]
        public int? OrganizationLevel { get; set; }

        /// <summary>
        /// 机构描述
        /// </summary>
        [Description("机构描述")]
        public string OrganizationDesc { get; set; }
    }
}