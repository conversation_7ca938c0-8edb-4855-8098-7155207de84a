using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 附件管理
    /// </summary>
    [SugarTable("MD_AttachmentManagement")]
    public class MD_AttachmentManagement : BaseEntity
    {
        /// <summary>
        /// 附件管理主键Id
        /// </summary>
        [Description("附件管理主键Id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        [Description("文件名称")]
        public string FileName { get; set; }

        /// <summary>
        /// 文件编码名称
        /// </summary>
        [Description("文件编码名称")]
        public string FileCodeName { get; set; }

        /// <summary>
        /// 附件上传地址
        /// </summary>
        [Description("附件上传地址")]
        public string Address { get; set; }

        /// <summary>
        /// 附件类型
        /// </summary>
        [Description("附件类型")]
        public string AttachType { get; set; }

        /// <summary>
        /// 附件类型代码
        /// </summary>
        [Description("附件类型代码")]
        public string AttachTypeCode { get; set; }

        /// <summary>
        /// 上传唯一标识
        /// </summary>
        [Description("上传唯一标识")]
        public string FileId { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }

        /// <summary>
        /// 菜单类型
        /// </summary>
        public string MenuType { get; set; }

        /// <summary>
        /// 菜单单号
        /// </summary>
        public string TypeOrder { get; set; }

        /// <summary>
        /// 单据Id
        /// </summary>
        public string ReferenceDocNumber { get; set; }
    }
}