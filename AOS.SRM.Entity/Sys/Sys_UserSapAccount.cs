using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_UserSapAccount")]
    public class Sys_UserSapAccount : BaseEntity
    {
        /// <summary> 
        /// 用户SAP账号ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string UserSapAccountID { get; set; }

        /// <summary> 
        /// 用户ID
        /// </summary> 
        public string UserID { get; set; }

        /// <summary> 
        /// 登录账号
        /// </summary> 
        public string SapAccount { get; set; }

        /// <summary> 
        /// 登录密码
        /// </summary> 
        public string SapPassword { get; set; }
    }
}