using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 数据字典主表
    /// </summary>
    [SugarTable("Sys_DictionaryBase")]
    public class Sys_DictionaryBase : BaseEntity
    {
        /// <summary>
        /// DictionaryID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("Id")]
        public string Id { get; set; }

        /// <summary>
        /// 类型编号
        /// </summary>
        [Description("类型编号")]
        public string TypeCode { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>
        [Description("类型名称")]
        public string TypeDisc { get; set; }
    }
}