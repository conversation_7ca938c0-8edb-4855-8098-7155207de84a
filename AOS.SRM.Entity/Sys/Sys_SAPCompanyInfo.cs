using AOS.SRM.Entity;
using SqlSugar;

namespace HZ.WMS.Entity.Sys
{
    /// <summary>
    /// sap链接配置
    /// </summary>
    [SugarTable("Sys_SAPCompanyInfo")]
    public class Sys_SAPCompanyInfo : BaseEntity
    {
        /// <summary>
        /// DictionaryID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string CompanyInfoId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CompanyCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SAPName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SAPAppServerHost { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SAPClient { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SAPUser { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SAPPassword { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string  SAPSystemNumber { get; set; }
    }
}