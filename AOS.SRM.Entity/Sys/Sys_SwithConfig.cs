using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_SwithConfig")]
    public class Sys_SwithConfig : BaseEntity
    {
        /// <summary>
        /// 开关ID
        /// </summary>
        [Description("开关ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ConfigID { get; set; }

        /// <summary>
        /// 开关设置编号
        /// </summary>
        [Description("开关设置编号")]
        public string ConfigCode { get; set; }

        /// <summary>
        /// 开关设置描述
        /// </summary>
        [Description("开关设置描述")]
        public string ConfigDesc { get; set; }

        /// <summary>
        /// 开关值
        /// </summary>
        [Description("开关值")]
        public bool? SwitchValue { get; set; }
    }
}