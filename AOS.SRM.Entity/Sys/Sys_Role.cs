using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_Role")]
    public class Sys_Role : BaseEntity
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        [Description("角色ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string RoleID { get; set; }

        /// <summary>
        /// 角色描述
        /// </summary>
        [Description("角色描述")]
        public string RoleDesc { get; set; }
    }
}