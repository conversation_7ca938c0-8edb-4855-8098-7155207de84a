using System.ComponentModel;
using SqlSugar;


namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_MailServerConfig")]
    public class Sys_MailServerConfig : BaseEntity
    {
        /// <summary>
        /// 邮件服务器ID
        /// </summary>
        [Description("邮件服务器ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string MailServerID { get; set; }

        /// <summary>
        /// 服务器地址
        /// </summary>
        [Description("服务器地址")]
        public string MailServerHost { get; set; }

        /// <summary>
        /// 端口
        /// </summary>
        [Description("端口")]
        public int MailServerPort { get; set; }

        /// <summary>
        /// 邮箱账号
        /// </summary>
        [Description("邮箱账号")]
        public string MailServerAccount { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        [Description("密码")]
        public string MailServerPassword { get; set; }

        /// <summary>
        /// 发件人显示名
        /// </summary>
        [Description("发件人显示名")]
        public string SenderDisplayName { get; set; }
    }
}