using System;
using System.ComponentModel;
using SqlSugar;


namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_Mail")]
    public class Sys_Mail : BaseEntity
    {
        /// <summary>
        /// 邮件ID
        /// </summary>
        [Description("邮件ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string MailID { get; set; }

        /// <summary>
        /// 消息ID
        /// </summary>
        [Description("消息ID")]
        public string MessageID { get; set; }

        /// <summary>
        /// 收件人用户ID
        /// </summary>
        [Description("收件人用户ID")]
        public string UserID { get; set; }

        /// <summary>
        /// 收件人姓名
        /// </summary>
        [Description("收件人姓名")]
        public string UserName { get; set; }

        /// <summary>
        /// 消息分类ID
        /// </summary>
        [Description("消息分类ID")]
        public string MessageTypeID { get; set; }

        /// <summary>
        /// 消息分类描述
        /// </summary>
        [Description("消息分类描述")]
        public string MessageTypeDesc { get; set; }

        /// <summary>
        /// 邮件标题
        /// </summary>
        [Description("邮件标题")]
        public string MailSubject { get; set; }

        /// <summary>
        /// 邮件正文
        /// </summary>
        [Description("邮件正文")]
        public string MailBody { get; set; }

        /// <summary>
        /// 发件人邮箱
        /// </summary>
        [Description("发件人邮箱")]
        public string SenderMail { get; set; }

        /// <summary>
        /// 发件人显示名
        /// </summary>
        public string SenderDisplayName { get; set; }

        /// <summary>
        /// 收件人邮箱
        /// </summary>
        [Description("收件人邮箱")]
        public string ReceiverMail { get; set; }

        /// <summary>
        /// 发件时间
        /// </summary>
        [Description("发件时间")]
        public DateTime? SendTime { get; set; }

        /// <summary>
        /// 是否已发送 true：是  false：否
        /// </summary>
        public bool? AutoSend { get; set; }

        /// <summary>
        /// 附件地址
        /// </summary>
        public string AttachmentAddr { get; set; }
    }
}