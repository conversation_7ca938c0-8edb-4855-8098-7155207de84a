using System.ComponentModel;
using SqlSugar;


namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_DbBackup")]
    public class Sys_DbBackup : BaseEntity
    {
        /// <summary>
        /// 数据库备份记录ID
        /// </summary>
        [Description("数据库备份记录ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string DbBackupID { get; set; }

        /// <summary>
        /// 数据库名称
        /// </summary>
        [Description("数据库名称")]
        public string DbName { get; set; }

        /// <summary>
        /// 备份名称
        /// </summary>
        [Description("备份名称")]
        public string BackupName { get; set; }

        /// <summary>
        /// 备份文件路径
        /// </summary>
        [Description("备份文件路径")]
        public string BackupFilePath { get; set; }

        /// <summary>
        /// 是否压缩备份
        /// </summary>
        [Description("是否压缩备份")]
        public bool? IsCompressed { get; set; }

        /// <summary>
        /// 连接字符串或者IP
        /// </summary>
        [Description("连接字符串或者IP")]
        public string DbServer { get; set; }
    }
}