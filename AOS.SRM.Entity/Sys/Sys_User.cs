using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 系统用户
    /// </summary>
    [SugarTable("Sys_User")]
    public class Sys_User : BaseEntity
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Description("用户ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string UserID { get; set; }

        /// <summary>
        /// 所属机构
        /// </summary>
        [Description("所属机构")]
        public string OrganizationID { get; set; }

        /// <summary>
        /// 机构描述
        /// </summary>
        [Description("机构描述")]
        public string OrganizationDesc { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        [Description("用户姓名")]
        public string UserName { get; set; }

        /// <summary>
        /// 英文名字
        /// </summary>
        [Description("英文名字")]
        public string FrgnName { get; set; }

        /// <summary>
        /// 登录账号
        /// </summary>
        [UniqueCode]
        [Description("登录账号")]
        public string LoginAccount { get; set; }

        /// <summary>
        /// 登录密码
        /// </summary>
        [Description("登录密码")]
        public string LoginPassword { get; set; }

        /// <summary>
        /// 头像
        /// </summary>
        [Description("头像")]
        public string Avatar { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        [Description("性别")]
        public int? Gender { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        [Description("出生日期")]
        public DateTime? Birthday { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [Description("电子邮箱")]
        public string Email { get; set; }

        /// <summary>
        /// 移动手机
        /// </summary>
        [Description("移动手机")]
        public string Mobile { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [Description("联系电话")]
        public string Telphone { get; set; }

        /// <summary>
        /// 冻结标识
        /// </summary>
        [Description("冻结标识")]
        public bool? IsEnable { get; set; }

        /// <summary>
        /// 
        /// </summary>
        [Description("用户角色")]
        public int? UserRole { get; set; }

        /// <summary>
        /// 是否供应商
        /// </summary>
        public bool? IsSupplier { get; set; } = false;

        /// <summary>
        /// 是否需要更新密码
        /// </summary>
        public bool? NeedUpdatePassword { get; set; } = false;

        /// <summary>
        /// 重置登录次数
        /// </summary>
        public int? ResetLoginCount { get; set; } = 0;

        /// <summary>
        /// 公司账套
        /// </summary>
        [Description("公司账套")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 公司账套名称
        /// </summary>
        [Description("公司账套名称")]
        public string CompanyName { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { get; set; }

        /// <summary>
        /// 密码更新标记 (0未更新1已更新)
        /// </summary>
        [Description("密码更新标记 (0未更新1已更新)")]
        public int? PasswordUpdateFlag { get; set; }

        /// <summary>
        /// 图纸类型
        /// </summary>
        [Description("图纸类型")]
        public string BlueprintType { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        [Description("物料类型")]
        public string MaterialType { get; set; }

        /// <summary>
        /// 图纸发送组
        /// </summary>
        [Description("图纸发送组")]
        public int? BlueprintSendGroup { get; set; }
    }

    /// <summary>
    /// 用户角色
    /// </summary>
    public enum UserRoleType
    {
        /// <summary>
        /// 系统管理员
        /// </summary>
        Administrator = 1,

        /// <summary>
        /// 业务人员
        /// </summary>
        BusinessUser = 2,

        /// <summary>
        /// 开发人员（内置）
        /// </summary>
        Developer = 99
    }
}