using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_RoleResource")]
    public class Sys_RoleResource : BaseEntity
    {
        /// <summary>
        /// 角色资源ID
        /// </summary>
        [Description("角色资源ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string RoleResourceID { get; set; }

        /// <summary>
        /// 资源ID
        /// </summary>
        [Description("资源ID")]
        public string ResourceID { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        [Description("角色ID")]
        public string RoleID { get; set; }
    }
}