using System.ComponentModel;
using SqlSugar;


namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 消息分类
    /// </summary>
    [SugarTable("Sys_MessageType")]
    public class Sys_MessageType:BaseEntity
    {
        /// <summary>
        /// 消息分类ID
        /// </summary>
        [Description("消息分类ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string MessageTypeID { get; set; }
        /// <summary>
        /// 消息分类描述
        /// </summary>
        [Description("消息分类描述")]
        public string MessageTypeDesc { get; set; }
        /// <summary>
        /// 是否邮件通知
        /// </summary>
        [Description("是否邮件通知")]
        public bool? IsNotifyByEmail { get; set; }
    }

    /// <summary>
    /// 消息通知方式
    /// </summary>
    public enum MessageNotifyType
    {
        /// <summary>
        /// 站内信
        /// </summary>
        InsideMessage = 1,
        /// <summary>
        /// 邮件
        /// </summary>
        Email = 2
    }
}

