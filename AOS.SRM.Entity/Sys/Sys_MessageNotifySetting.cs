using System.ComponentModel;
using SqlSugar;


namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 
    /// </summary>
    [SugarTable("Sys_MessageNotifySetting")]
    public class Sys_MessageNotifySetting : BaseEntity
    {
        /// <summary>
        /// 分类通知设置ID
        /// </summary>
        [Description("分类通知设置ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string MessageNotifySettingID { get; set; }

        /// <summary>
        /// 消息分类ID
        /// </summary>
        [Description("消息分类ID")]
        public string MessageTypeID { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        [Description("角色ID")]
        public string UserID { get; set; }
    }
}