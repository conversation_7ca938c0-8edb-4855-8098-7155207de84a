using System.ComponentModel;

namespace AOS.SRM.Entity.PLM
{
    /// <summary>
    /// 图纸自动发送条件
    /// </summary>
    public class BlueprintAutoSendCondition
    {
        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string WERKS { get; set; }

        /// <summary>
        /// 图纸类型（多个用逗号分隔）
        /// </summary>
        [Description("图纸类型")]
        public string BlueprintType { get; set; }

        /// <summary>
        /// 图纸类别（多个用逗号分隔）
        /// </summary>
        [Description("图纸类别")]
        public string BlueprintCategory { get; set; }

        /// <summary>
        /// 物料类型（多个用逗号分隔）
        /// </summary>
        [Description("物料类型")]
        public string MaterialType { get; set; }
    }

    /// <summary>
    /// 图纸自动发送发送组配置
    /// </summary>
    public class BlueprintAutoSendGroupConfig
    {
        /// <summary>
        /// 发送组列表（多个用逗号分隔）
        /// </summary>
        [Description("发送组列表")]
        public string SendGroups { get; set; }
    }
}
