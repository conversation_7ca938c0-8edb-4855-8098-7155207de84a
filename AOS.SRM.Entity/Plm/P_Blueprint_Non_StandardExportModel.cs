using System;
using System.ComponentModel;

namespace AOS.SRM.Entity.PLM
{
    /// <summary>
    /// 非标图纸导出模型
    /// </summary>
    public class P_Blueprint_Non_StandardExportModel
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料版本
        /// </summary>
        [Description("物料版本")]
        public string MaterialVersion { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public long Quantity { get; set; }

        /// <summary>
        /// 外部物料组
        /// </summary>
        [Description("外部物料组")]
        public string OutMaterialGroup { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// SAP订单号
        /// </summary>
        [Description("SAP订单号")]
        public string SapNo { get; set; }

        /// <summary>
        /// SAP订单行
        /// </summary>
        [Description("SAP订单行")]
        public int SapLine { get; set; }

        /// <summary>
        /// 文件状态文本
        /// </summary>
        [Description("文件状态文本")]
        public string FileStatusText { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        [Description("文件名称")]
        public string FileName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime CTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        public string CUser { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }
    }
}
