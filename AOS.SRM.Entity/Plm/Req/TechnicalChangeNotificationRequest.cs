using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace AOS.SRM.Entity.PLM.Req
{
    /// <summary>
    /// 技术更改通知请求参数
    /// </summary>
    public class TechnicalChangeNotificationRequest
    {
        /// <summary>
        /// 图纸ID列表
        /// </summary>
        [Description("图纸ID列表")]
        [Required(ErrorMessage = "图纸ID列表不能为空")]
        public List<string> BlueprintIds { get; set; }

        /// <summary>
        /// 更改内容
        /// </summary>
        [Description("更改内容")]
        [Required(ErrorMessage = "更改内容不能为空")]
        [StringLength(2000, ErrorMessage = "更改内容长度不能超过2000个字符")]
        public string ChangeContent { get; set; }

        /// <summary>
        /// 更改原因（可选）
        /// </summary>
        [Description("更改原因")]
        [StringLength(1000, ErrorMessage = "更改原因长度不能超过1000个字符")]
        public string ChangeReason { get; set; }

        /// <summary>
        /// 通知类型（可选，用于扩展）
        /// </summary>
        [Description("通知类型")]
        public string NotificationType { get; set; } = "技术更改通知";

        /// <summary>
        /// 是否发送邮件通知
        /// </summary>
        [Description("是否发送邮件通知")]
        public bool SendEmailNotification { get; set; } = true;

        /// <summary>
        /// 验证请求参数
        /// </summary>
        /// <returns>验证结果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (BlueprintIds == null || BlueprintIds.Count == 0)
            {
                result.IsValid = false;
                result.ErrorMessage = "图纸ID列表不能为空";
                return result;
            }

            if (string.IsNullOrWhiteSpace(ChangeContent))
            {
                result.IsValid = false;
                result.ErrorMessage = "更改内容不能为空";
                return result;
            }

            if (ChangeContent.Length > 2000)
            {
                result.IsValid = false;
                result.ErrorMessage = "更改内容长度不能超过2000个字符";
                return result;
            }

            if (!string.IsNullOrEmpty(ChangeReason) && ChangeReason.Length > 1000)
            {
                result.IsValid = false;
                result.ErrorMessage = "更改原因长度不能超过1000个字符";
                return result;
            }

            result.IsValid = true;
            return result;
        }
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 是否验证通过
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
    }
}
