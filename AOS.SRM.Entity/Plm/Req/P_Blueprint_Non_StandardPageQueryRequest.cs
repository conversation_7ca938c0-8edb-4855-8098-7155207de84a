using System;
using System.ComponentModel;
using AOS.Core.Http;

namespace AOS.SRM.Entity.PLM.Req
{
    /// <summary>
    /// 非标图纸分页查询请求参数
    /// </summary>
    public class P_Blueprint_Non_StandardPageQueryRequest
    {
        /// <summary>
        /// 分页参数
        /// </summary>
        [Description("分页参数")]
        public Pagination Page { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料版本
        /// </summary>
        [Description("物料版本")]
        public string MaterialVersion { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// SAP订单号
        /// </summary>
        [Description("SAP订单号")]
        public string SapNo { get; set; }

        /// <summary>
        /// 文件状态 0未上传 1已上传
        /// </summary>
        [Description("文件状态")]
        public int? FileStatus { get; set; }

        /// <summary>
        /// 创建时间开始
        /// </summary>
        [Description("创建时间开始")]
        public DateTime? CreateTimeStart { get; set; }

        /// <summary>
        /// 创建时间结束
        /// </summary>
        [Description("创建时间结束")]
        public DateTime? CreateTimeEnd { get; set; }

        /// <summary>
        /// 关键字搜索（物料编码、物料描述、SAP订单号）
        /// </summary>
        [Description("关键字搜索")]
        public string Keyword { get; set; }

        /// <summary>
        /// 转换为查询请求参数
        /// </summary>
        /// <returns>查询请求参数</returns>
        public P_Blueprint_Non_StandardQueryRequest ToQueryRequest()
        {
            return new P_Blueprint_Non_StandardQueryRequest
            {
                MaterialCode = this.MaterialCode,
                MaterialVersion = this.MaterialVersion,
                MaterialDesc = this.MaterialDesc,
                FactoryCode = this.FactoryCode,
                SapNo = this.SapNo,
                FileStatus = this.FileStatus,
                CreateTimeStart = this.CreateTimeStart,
                CreateTimeEnd = this.CreateTimeEnd,
                Keyword = this.Keyword
            };
        }
    }
}
