using System;
using System.ComponentModel;

namespace AOS.SRM.Entity.PLM
{
    public class BlueprintReq
    {

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        public string Name { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Description("版本")]
        public string Version { get; set; }

        /// <summary>
        /// 下载地址
        /// </summary>
        [Description("下载地址")]
        public string DownUrl { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        [Description("生效日期")]
        public DateTime? EffectiveDate { get; set; }

        /// <summary>
        /// Token (Optional - now using Authorization header instead)
        /// </summary>
        [Description("Token")]
        public string Token { get; set; }
        
        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string WERKS { get; set; }

        /// <summary>
        /// CA号
        /// </summary>
        [Description("CA号")]
        public string AENNR { get; set; }
        
        /// <summary>
        /// 图纸编号
        /// </summary>
        [Description("图纸编号")]
        public string BlueprintCode { get; set; }

        /// <summary>
        /// CR号
        /// </summary>
        [Description("CR号")]
        public string CrCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 创建用户名称
        /// </summary>
        [Description("创建用户名称")]
        public string CreateUserName { get; set; }
        
        /// <summary>
        /// 创建用户邮箱
        /// </summary>
        [Description("创建用户邮箱")]
        public string CreateEmail { get; set; }

        /// <summary>
        /// 图纸类别
        /// </summary>
        [Description("图纸类别")]
        public string BlueprintCategory { get; set; }


    }
}