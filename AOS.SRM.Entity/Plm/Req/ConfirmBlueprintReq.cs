using System.ComponentModel;

namespace AOS.SRM.Entity.PLM
{
    /// <summary>
    /// 确认图纸请求参数
    /// </summary>
    public class ConfirmBlueprintReq
    {
        /// <summary>
        /// 图纸ID
        /// </summary>
        [Description("图纸ID")]
        public string Id { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        [Description("采购订单号")]
        public string PurchaseOrder { get; set; }

        /// <summary>
        /// 采购订单行号
        /// </summary>
        [Description("采购订单行号")]
        public decimal? PurchaseOrderLine { get; set; }
    }
}