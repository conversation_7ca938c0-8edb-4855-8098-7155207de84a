using System.Collections.Generic;
using System.ComponentModel;

namespace AOS.SRM.Entity.PLM.Req
{
    /// <summary>
    /// 撤回未释放图纸请求参数
    /// </summary>
    public class RevokeUnreleasedRequest
    {
        /// <summary>
        /// 图纸ID列表
        /// </summary>
        [Description("图纸ID列表")]
        public List<string> BlueprintIds { get; set; }
        
        /// <summary>
        /// 撤回原因
        /// </summary>
        [Description("撤回原因")]
        public string Reason { get; set; }
    }
}
