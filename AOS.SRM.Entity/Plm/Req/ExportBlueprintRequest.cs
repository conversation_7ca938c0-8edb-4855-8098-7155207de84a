using System;
using System.ComponentModel;

namespace AOS.SRM.Entity.PLM.Req
{
    /// <summary>
    /// 导出图纸请求参数
    /// </summary>
    public class ExportBlueprintRequest
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 图纸名称
        /// </summary>
        [Description("图纸名称")]
        public string Name { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Description("版本")]
        public string Version { get; set; }

        /// <summary>
        /// 物料类型
        /// </summary>
        [Description("物料类型")]
        public string MaterialType { get; set; }

        /// <summary>
        /// 释放状态 0未释放 1已释放
        /// </summary>
        [Description("释放状态")]
        public int? ReleaseStatus { get; set; }

        /// <summary>
        /// 创建时间开始
        /// </summary>
        [Description("创建时间开始")]
        public DateTime? CreateTimeStart { get; set; }

        /// <summary>
        /// 创建时间结束
        /// </summary>
        [Description("创建时间结束")]
        public DateTime? CreateTimeEnd { get; set; }

        /// <summary>
        /// 关键字搜索（物料编码、图纸名称、物料描述）
        /// </summary>
        [Description("关键字")]
        public string Keyword { get; set; }
    }
}
