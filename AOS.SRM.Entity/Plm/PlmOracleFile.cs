using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PLM
{
    /// <summary>
    /// Oracle数据库中的文件信息
    /// </summary>
    [SugarTable("WIND.APPLICATIONDATA")]
    public class PlmOracleFile
    {
        /// <summary>
        /// 文件ID
        /// </summary>
        [SugarColumn(ColumnName = "IDA2A2", IsPrimaryKey = true)]
        public string FileId { get; set; }
        
        /// <summary>
        /// 文件名称
        /// </summary>
        [SugarColumn(ColumnName = "FILENAME")]
        public string FileName { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(ColumnName = "CREATESTAMPA2")]
        public DateTime? CreateTime { get; set; }
        
        /// <summary>
        /// 上传路径
        /// </summary>
        [SugarColumn(ColumnName = "UPLOADEDFROMPATH")]
        public string UploadPath { get; set; }
    }
} 