using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;

namespace AOS.SRM.Entity.PLM
{
    /// <summary>
    /// 图纸信息记录表
    /// </summary>
    [SugarTable("Plm_Blueprint")]
    public class Plm_Blueprint : BaseEntity
    {
        
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        public string Name { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Description("版本")]
        public string Version { get; set; }

        /// <summary>
        /// 下载地址
        /// </summary>
        [Description("下载地址")]
        public string DownUrl { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        [Description("文件名称")]
        [SugarColumn(IsIgnore = true)]
        public string FileName { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        [Description("生效日期")]
        public DateTime? EffectiveDate { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string WERKS { get; set; }

        /// <summary>
        /// CA号
        /// </summary>
        [Description("CA号")]
        public string AENNR { get; set; }

        /// <summary>
        /// 采购类型 自制，虚拟，采购，委外
        /// </summary>
        [Description("物料类型")]
        public string MaterialType { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 发送状态 0未发送 1已发送
        /// </summary>
        [Description("发送状态")]
        public int? SendStatus { get; set; }

        /// <summary>
        /// 释放状态 0未释放 1已释放
        /// </summary>
        [Description("释放状态")]
        public int? ReleaseStatus { get; set; }

        /// <summary>
        /// 释放时间
        /// </summary>
        [Description("释放时间")]
        public DateTime? ReleaseTime { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        [Description("文件名称")]
        public string Filename { get; set; }

        /// <summary>
        /// 图纸类型
        /// </summary>
        [Description("图纸类型")]
        public string BlueprintType { get; set; }

        /// <summary>
        /// 图纸编号
        /// </summary>
        [Description("图纸编号")]
        public string BlueprintCode { get; set; }

        /// <summary>
        /// CR号
        /// </summary>
        [Description("CR号")]
        public string CrCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 创建用户名称
        /// </summary>
        [Description("创建用户名称")]
        public string CreateUserName { get; set; }

        /// <summary>
        /// 创建用户邮箱
        /// </summary>
        [Description("创建用户邮箱")]
        public string CreateEmail { get; set; }
        
        /// <summary>
        /// 图纸类别
        /// </summary>
        [Description("图纸类别")]
        public string BlueprintCategory { get; set; }

        /// <summary>
        /// 手动发放类型 0批量处理 1只能单条处理
        /// </summary>
        [Description("手动发放类型")]
        public int? HandMovementSendType { get; set; } = 0;

    }
}