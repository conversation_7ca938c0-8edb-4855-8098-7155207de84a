using System.ComponentModel;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 邮件通知设置
    /// </summary>
    //[SugarTable("V_EmailUserInfo")]
    public  class V_EmailUserInfo
    {
        /// <summary>
        /// 消息类别id
        /// </summary>
        [Description("消息类别ID")]
        public string MessageTypeID { get; set; }
        /// <summary>
        /// 消息类别描述
        /// </summary>
        [Description("消息类别描述")]
        public string MessageTypeDesc { get; set; }
        /// <summary>
        /// 用户ID
        /// </summary>
        [Description("用户ID")]
        public string UserID { get; set; }
        /// <summary>
        /// 用户名称
        /// </summary>
        [Description("用户名称")]
        public string UserName { get; set; }
        /// <summary>
        /// 邮箱
        /// </summary>
        [Description("邮箱")]
        public string Email { get; set; }
    }
}
