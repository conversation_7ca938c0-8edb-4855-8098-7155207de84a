using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.WMS
{
    /// <summary>
    /// 
    /// </summary>
    public class MD_Province_City_District
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键")]
        public int? Id { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        [Description("父级ID")]
        public int? ParentId { get; set; }

        /// <summary>
        /// 地区名称
        /// </summary>
        [Description("地区名称")]
        public string Name { get; set; }
    }
}