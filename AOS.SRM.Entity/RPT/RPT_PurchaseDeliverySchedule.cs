using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_PurchaseDeliverySchedule
    {
        /// <summary>
        /// 
        /// </summary>
        public Int64 RowID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string PlanNum { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string DeliveryTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SupplierCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SupplierName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string BaseNum { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Int32 BaseLine { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ItemCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ItemName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ItmsGrpCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ItmsGrpName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? PurchaseOrderQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? PlanQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? DeliveryedQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? OkQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? NoQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? NoInspectionQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? StockInQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string DeliveryPlanStatus { get; set; }
    }
}
