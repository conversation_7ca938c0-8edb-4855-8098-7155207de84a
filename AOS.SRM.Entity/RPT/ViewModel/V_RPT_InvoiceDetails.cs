using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT.ViewModel
{
    /// <summary>
    /// 开票申请明细
    /// </summary>
    public class V_RPT_InvoiceDetails
    {
        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        [Description("单据编号")]
        public string BillingNo { get; set; }
        /// <summary>
        /// 发票单号
        /// </summary>
        [Description("发票号")]
        public string InvoiceNo { get; set; }
        /// <summary>
        /// 开票时间
        /// </summary>
        [Description("发票日期")]
        public DateTime? InvoiceTime { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        [Description("申请人")]
        public string Invoicer { get; set; }
        /// <summary>
        /// 报检单号
        /// </summary>
        [Description("报检单号")]
        public string InspectionNo { get; set; }
        /// <summary>
        /// 报检单行号
        /// </summary>
        [Description("报检单行号")]
        public int? InspectionLine { get; set; }
        ///// <summary>
        ///// 收货单号
        ///// </summary>
        //[Description("收货单号")]
        //public string ReceiptNo { get; set; }
        /// <summary>
        /// 采购订单号
        /// </summary>
        [Description("采购单号")]
        public string OrderNo { get; set; }
        /// <summary>
        /// 采购订单行号
        /// </summary>
        [Description("采购行号")]
        public int? OrderLine { get; set; }
        /// <summary>
        /// 旧物料编码
        /// </summary>
        [Description("旧物料编码")]
        public string BISMT { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary>
        /// 采购收货数量
        /// </summary>
        [Description("收货数量")]
        public decimal? PurchaseReceiptQty { get; set; }
        /// <summary>
        /// 开票单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }
        /// <summary>
        /// 采购价格
        /// </summary>
        [Description("采购单价")]
        public decimal? PurchasePrice { get; set; }
        /// <summary>
        /// 结算单价
        /// </summary>
        [Description("结算单价")]
        public decimal? SettleUnitPrice { get; set; }
        /// <summary>
        /// 结算金额
        /// </summary>
        [Description("结算金额")]
        public decimal? SettlementPrice { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        [Description("税额")]
        public string TaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        [Description("税率")]
        public string TaxRate { get; set; }
        /// <summary>
        /// 含税金额
        /// </summary>
        [Description("含税金额")]
        public decimal? AmountWithTax { get; set; }
        /// <summary>
        /// 下单时间
        /// </summary>
        [Description("下单时间")]
        public DateTime? OrderCTime { get; set; }
        /// <summary>
        /// 订单类型  1：标准 2：委外
        /// </summary>
        [Description("类型")]
        public string OrderType { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string ZTEXT { get; set; }
        ///// <summary>
        ///// 预制发票
        ///// </summary>
        //[Description("预制发票号")]
        //public string SAPInvoiceNo { get; set; }
        ///// <summary>
        ///// 过账日期
        ///// </summary>
        //[Description("过账日期")]
        //public DateTime? PostTime { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }
    }
}
