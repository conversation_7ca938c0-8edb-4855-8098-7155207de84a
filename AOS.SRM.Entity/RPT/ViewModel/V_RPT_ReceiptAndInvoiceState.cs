using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT.ViewModel
{
    /// <summary>
    /// 收获记录开票状态
    /// </summary>
    public class V_RPT_ReceiptAndInvoiceState
    {
        /// <summary>
        /// 报检单号
        /// </summary>
        [Description("报检单号")]
        public string InspectionNum { get; set; }
        /// <summary>
        /// 报检单行号
        /// </summary>
        [Description("报检单行号")]
        public int InspectionLine { get; set; }
        /// <summary>
        /// 收获单号
        /// </summary>
        [Description("收获单号")]
        public string DocNum { get; set; }
        /// <summary>
        /// 采购单号
        /// </summary>
        [Description("采购单号")]
        public string BaseNum { get; set; }
        /// <summary>
        /// 采购单行号
        /// </summary>
        [Description("采购单行号")]
        public int BaseLine { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        public string SupplierCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplierName { get; set; }
        /// <summary>
        /// 旧物料编码
        /// </summary>
        [Description("旧物料编码")]
        public string BISMT { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary>
        /// 收获数量
        /// </summary>
        [Description("收获数量")]
        public decimal PurchaseReceiptQty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }
        /// <summary>
        /// 行备注
        /// </summary>
        [Description("行备注")]
        public string ZTEXT { get; set; }
        /// <summary>
        /// 状态 ：未开票、已开票
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }
        /// <summary>
        /// 收货日期
        /// </summary>
        [Description("收货日期")]
        public DateTime? CTime { get; set; }
    }
}
