using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_PLineOutPut
    {
        /// <summary>
        /// 生产线
        /// </summary>
        [Description("生产线")]
        public string PLine { get; set; }
        /// <summary>
        /// 产品编号
        /// </summary>
        [Description("产品编号")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }
        /// <summary>
        /// 总数量
        /// </summary>
        [Description("总数量")]
        public decimal? SumQty { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        [Description("日期")]
        public DateTime? CTime { get; set; }
        /// <summary>
        /// 成品类别
        /// </summary>
        [Description("成品类别")]
        public bool DType { get; set; }
    }
}
