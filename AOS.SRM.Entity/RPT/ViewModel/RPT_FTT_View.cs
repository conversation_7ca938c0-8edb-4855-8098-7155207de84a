using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_FTT_View
    {
        /// <summary>
        /// Production Line ID
        /// </summary>
        [Description("Production Line ID")]
        public string ProductionLineID { get; set; }
        /// <summary>
        /// FTT ID
        /// </summary>
        [Description("FTT ID")]
        public string FTTID { get; set; }
        /// <summary>
        /// Product ID
        /// </summary>
        [Description("Product ID")]
        public string ProductID { get; set; }
        /// <summary>
        /// Station ID
        /// </summary>
        [Description("Station ID")]
        public string StationID { get; set; }
        /// <summary>
        /// NCCondition Description
        /// </summary>
        [Description("NCCondition Description")]
        public string NCConditionDescription { get; set; }
        /// <summary>
        /// Handling Recommendations
        /// </summary>
        [Description("Handling Recommendations")]
        public string HandlingRecommendations { get; set; }
        /// <summary>
        /// RejectQuantity
        /// </summary>
        [Description("RejectQuantity")]
        public decimal? RejectQuantity { get; set; }
        /// <summary>
        /// InputQuantity
        /// </summary>
        [Description("InputQuantity")]
        public decimal? InputQuantity { get; set; }
        /// <summary>
        /// OutputQuantity
        /// </summary>
        [Description("OutputQuantity")]
        public decimal? OutputQuantity { get; set; }
        /// <summary>
        /// FType
        /// </summary>
        [Description("FType")]
        public string FType { get; set; }
        /// <summary>
        /// CreateDate
        /// </summary>
        [Description("CreateDate")]
        public DateTime? CreateDate { get; set; }
    }
}
