using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_PO_Inspection_View
    {
        /// <summary>
        /// 批次号
        /// </summary>
        [Description("批次号")]
        public string BatchNum { get; set; }
        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplierCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplierName { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary>
        /// 送检批数
        /// </summary>
        [Description("送检批数")]
        public decimal? Qty { get; set; }
        /// <summary>
        /// 合格批数
        /// </summary>
        [Description("合格批数")]
        public decimal? OkQty { get; set; }
        /// <summary>
        /// 不合格批数
        /// </summary>
        [Description("不合格批数")]
        public decimal? NoQty { get; set; }
        /// <summary>
        /// 状态(是否质检)
        /// </summary>
        [Description("状态(是否质检)")]
        public bool Status { get; set; }
        /// <summary>
        /// 质检人
        /// </summary>
        [Description("质检人")]
        public string IUser { get; set; }
        /// <summary>
        /// 质检时间
        /// </summary>
        [Description("质检时间")]
        public DateTime? ITime { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }
        /// <summary>
        /// 合格率
        /// </summary>
        [Description("合格率")]
        public decimal? RM_FPY { get; set; }
    }
}
