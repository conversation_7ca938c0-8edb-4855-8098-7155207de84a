using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_SD_Delivery_View
    {
        /// <summary>
        /// 交货单号
        /// </summary>
        [Description("交货单号")]
        public string ShipmentNo { get; set; }
        /// <summary>
        /// 集装箱号
        /// </summary>
        [Description("集装箱号")]
        public string Container { get; set; }
        /// <summary>
        /// 报关单号
        /// </summary>
        [Description("报关单号")]
        public string CustomsNum { get; set; }
        /// <summary>
        /// 实际出货时间
        /// </summary>
        [Description("实际出货时间")]
        public DateTime? ActualDate { get; set; }
        /// <summary>
        /// 销售订单号
        /// </summary>
        [Description("销售订单号")]
        public string BaseNum { get; set; }
        /// <summary>
        /// 出货数量
        /// </summary>
        [Description("出货数量")]
        public decimal? Qty { get; set; }
        /// <summary>
        /// 行号
        /// </summary>
        [Description("行号")]
        public Int32 BaseLine { get; set; }
        /// <summary>
        /// 客户编码
        /// </summary>
        [Description("客户编码")]
        public string CustomerCode { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary>
        /// 出货时间
        /// </summary>
        [Description("出货时间")]
        public DateTime? CTime { get; set; }
        /// <summary>
        /// 客户需求时间
        /// </summary>
        [Description("客户需求时间")]
        public DateTime? EndDeliveryTime { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("与承诺日期的差异")]
        public Int32 DiffDate { get; set; }
    }
}
