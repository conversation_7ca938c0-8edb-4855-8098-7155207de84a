using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_PO_InOut_View
    {
        /// <summary>
        /// 供应商代码
        /// </summary>
        [Description("供应商编号")]
        public string SupplierCode { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 日期
        /// </summary>
        [Description("日期")]
        public DateTime? CTime { get; set; }
        /// <summary>
        /// 收货数量
        /// </summary>
        [Description("收货数量")]
        public decimal? InQty { get; set; }
        /// <summary>
        /// 上架数量
        /// </summary>
        [Description("上架数量")]
        public decimal? SQty { get; set; }
        /// <summary>
        /// 退货数量
        /// </summary>
        [Description("退货数量")]
        public decimal? RQty { get; set; }
        /// <summary>
        /// 汇总数量
        /// </summary>
        [Description("汇总数量")]
        public decimal? sumQty { get; set; }
    }
}
