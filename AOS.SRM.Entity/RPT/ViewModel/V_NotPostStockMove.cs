using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class V_NotPostStockMove
    {
        /// <summary>
        /// 扫描单号
        /// </summary>
        [Description("扫描单号")]
        public string DocNum { get; set; }
        /// <summary>
        /// 条码号
        /// </summary>
        [Description("条码号")]
        public string BarCode { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        [Description("批次号")]
        public string BatchNum { get; set; }
        /// <summary>
        /// 订单号
        /// </summary>
        [Description("订单号")]
        public string BaseNum { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary>
        /// 物料组编号
        /// </summary>
        [Description("物料组编号")]
        public string ItmsGrpCode { get; set; }
        /// <summary>
        /// 物料组名称
        /// </summary>
        [Description("物料组名称")]
        public string ItmsGrpName { get; set; }
        /// <summary>
        /// 仓库编号
        /// </summary>
        [Description("仓库编号")]
        public string WhsCode { get; set; }
        /// <summary>
        /// 仓库名称
        /// </summary>
        [Description("仓库名称")]
        public string WhsName { get; set; }
        /// <summary>
        /// 区域编号
        /// </summary>
        [Description("区域编号")]
        public string RegionCode { get; set; }
        /// <summary>
        /// 区域名称
        /// </summary>
        [Description("区域名称")]
        public string RegionName { get; set; }
        /// <summary>
        /// 库位编号
        /// </summary>
        [Description("库位编号")]
        public string BinLocationCode { get; set; }
        /// <summary>
        /// 库位名称
        /// </summary>
        [Description("库位名称")]
        public string BinLocationName { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? Qty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }
        /// <summary>
        /// 操作类别
        /// </summary>
        [Description("操作类别")]
        public string OperationType { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }
    }
}
