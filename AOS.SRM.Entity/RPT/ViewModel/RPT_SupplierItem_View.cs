using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class RPT_SupplierItem_View
    {
        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplierCode { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 使用时间
        /// </summary>
        [Description("使用时间")]
        public DateTime? CTime { get; set; }
        /// <summary>
        /// 合格数量
        /// </summary>
        [Description("合格数量")]
        public decimal? OKQTY { get; set; }
        /// <summary>
        /// 不合格数量
        /// </summary>
        [Description("不合格数量")]
        public decimal? NoQty { get; set; }
        /// <summary>
        /// 使用数量
        /// </summary>
        [Description("使用数量")]
        public decimal? Qty { get; set; }
        /// <summary>
        /// 库存数量
        /// </summary>
        [Description("库存数量")]
        public decimal? StockQTY { get; set; }
    }
}
