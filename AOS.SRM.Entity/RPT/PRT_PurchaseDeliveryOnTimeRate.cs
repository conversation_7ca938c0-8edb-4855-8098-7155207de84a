using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.RPT
{
    /// <summary>
    /// 
    /// </summary>
    public class PRT_PurchaseDeliveryOnTimeRate
    {
        /// <summary>
        /// 
        /// </summary>
        public Int64 RowID { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SupplierCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SupplierName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ItemCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ItemName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ItmsGrpCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ItmsGrpName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Int32 TotalDeliveryItem { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Int32 OnTimeDeliveryItem { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public Int32 UnOnTimeDeliveryItem { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? TotalQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? TotalDeliveryedQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public decimal? TotalUnOnTimeDeliveryQty { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string OTDRate { get; set; }
    }
}
