using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 综合绩效考核基础信息
    /// </summary>
    public class S_PerformanceAppraisalBase : BaseEntity
    {
        ///<summary>
        ///主键
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        ///<summary>
        ///供应商编码
        ///</summary>
        [Description("供应商编码")]
        public string SupplierCode { get; set; }

        ///<summary>
        ///供应商名称
        ///</summary>
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 考核年度
        /// </summary>
        [Description("考核年度")]
        public string AssessYear { get; set; }

        ///<summary>
        ///主要供应产品
        ///</summary>
        [Description("主要供应产品")]
        public string SupplyProducts { get; set; }

        ///<summary>
        ///类别
        ///</summary>
        [Description("类别")]
        public string Category { get; set; }

        ///<summary>
        ///考核方式
        ///</summary>
        [Description("考核方式")]
        public string AssessMode { get; set; }

        /// <summary>
        /// 下一个考核日期
        /// </summary>
        [Description("下一个考核日期")]
        public DateTime? NextDate { get; set; }
    }
}