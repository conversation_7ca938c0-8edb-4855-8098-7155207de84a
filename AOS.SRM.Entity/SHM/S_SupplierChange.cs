using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    ///西子富沃德 供应商变更申请
    /// </summary>
    [SugarTable("S_SupplierChange")]
    public class S_SupplierChange : BaseEntity
    {
        /// <summary>
        /// 供应商主键id
        /// </summary>
        [Description("供应商主键id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        /// <summary>
        /// 公司编号
        /// </summary>
        [Description("公司编号")]
        public string CompanyCode { get; set; }
        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplyerCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyerName1 { get; set; }
        /// <summary>
        /// 供应商简称
        /// </summary>
        [Description("供应商简称")]
        public string SupplyerName { get; set; }
        /// <summary>
        /// 供应商账户组代码
        /// </summary>
        [Description("供应商账户组代码")]
        public string AccountGroupCode { get; set; }
        /// <summary>
        /// 供应商账户组描述
        /// </summary>
        [Description("供应商账户组描述")]
        public string AccountGroupDisc { get; set; }
        /// <summary>
        /// 供应商状态 0：已申请  1：审核中 2：已通过 3:已驳回
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }
        /// <summary>
        /// 供货状态 
        /// </summary>
        [Description("供货状态")]
        public string SupplyStatus { get; set; }
        /// <summary>
        /// 准入状态： 0：已提报 1：未准入 2：潜在供方 3：已准入
        /// </summary>
        public string AccessStatus { get; set; }
        /// <summary>
        /// 供应商性质
        /// </summary>
        [Description("供应商性质")]
        public string Nature { get; set; }
        /// <summary>
        /// 是否与西子其它公司业务往来 1：有 0：无
        /// </summary>
        [Description("是否与西子其它公司业务往来 1：有 0：无")]
        public string BWO { get; set; }
        /// <summary>
        /// 重要程度
        /// </summary>
        [Description("重要程度")]
        public string Importance { get; set; }
        /// <summary>
        /// 单位规模
        /// </summary>
        [Description("单位规模")]
        public string UnitSize { get; set; }
        /// <summary>
        /// 所属行业
        /// </summary>
        [Description("所属行业")]
        public string Industry { get; set; }
        /// <summary>
        /// 是否上市1：是 0：否
        /// </summary>
        [Description("是否上市1：是 0：否")]
        public bool? IsMarket { get; set; }
        /// <summary>
        /// 国家代码
        /// </summary>
        [Description("国家代码")]
        public string CountryCode { get; set; }
        /// <summary>
        /// 国家名称
        /// </summary>
        [Description("国家名称")]
        public string CountryName { get; set; }
        /// <summary>
        /// 省份
        /// </summary>
        [Description("省份")]
        public string Province { get; set; }
        /// <summary>
        /// 城市
        /// </summary>
        [Description("城市")]
        public string City { get; set; }
        /// <summary>
        /// 地址(SAP街道)
        /// </summary>
        [Description("地址(SAP街道)")]
        public string Address { get; set; }
        /// <summary>
        /// 邮政编码
        /// </summary>
        [Description("邮政编码")]
        public string PostalCode { get; set; }
        /// <summary>
        /// 固定电话
        /// </summary>
        [Description("固定电话")]
        public string Tel { get; set; }
        /// <summary>
        /// 移动电话
        /// </summary>
        [Description("移动电话")]
        public string Phone { get; set; }
        /// <summary>
        /// 传真
        /// </summary>
        [Description("传真")]
        public string Fax { get; set; }
        /// <summary>
        /// EMail
        /// </summary>
        [Description("EMail")]
        public string EMail { get; set; }
        /// <summary>
        /// 供方主页
        /// </summary>
        [Description("供方主页")]
        public string Homepage { get; set; }
        /// <summary>
        /// 法人代表
        /// </summary>
        [Description("法人代表")]
        public string LegalUser { get; set; }
        /// <summary>
        /// 法人身份证编号
        /// </summary>
        [Description("法人身份证编号")]
        public string LegalUserCardId { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        [Description("银行账号")]
        public string BankAccount { get; set; }
        /// <summary>
        /// 银行开户许可证号
        /// </summary>
        [Description("银行开户许可证号")]
        public string BankLicenseNo { get; set; }
        /// <summary>
        /// 开户银行
        /// </summary>
        [Description("开户银行")]
        public string BankDeposit { get; set; }
        /// <summary>
        /// 营业执照号
        /// </summary>
        [Description("营业执照号")]
        public string BusinessLicenseNo { get; set; }
        /// <summary>
        /// 税务登记号
        /// </summary>
        [Description("税务登记号")]
        public string TaxNumber { get; set; }
        /// <summary>
        /// 成立日期
        /// </summary>
        [Description("成立日期")]
        public DateTime? DateEstablishment { get; set; }
        /// <summary>
        /// 经营范围
        /// </summary>
        [Description("经营范围")]
        public string BusinessScope { get; set; }
        /// <summary>
        /// 是否有增值税发票1：有 0：无
        /// </summary>
        [Description("是否有增值税发票1：有 0：无")]
        public bool? IsVATInvoice { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        [Description("税率")]
        public string TaxRate { get; set; }
        /// <summary>
        /// 业务年数/年销售额  
        /// </summary>
        [Description("业务年数/年销售额")]
        public string AnnualSales { get; set; }
        /// <summary>
        /// 工厂面积
        /// </summary>
        [Description("工厂面积")]
        public string FactoryArea { get; set; }
        /// <summary>
        /// 供应商产品类别
        /// </summary>
        [Description("供应商产品类别")]
        public string ProcductType { get; set; }
        /// <summary>
        /// 有无质量体系认证1：有 0：无
        /// </summary>
        [Description("有无质量体系认证1：有 0：无")]
        public bool? IsCertificate { get; set; }
        /// <summary>
        /// 质量/技术人数
        /// </summary>
        [Description("质量/技术人数")]
        public string PeopleNumber { get; set; }
        /// <summary>
        ///供应商类别
        /// </summary>
        [Description("供应商类别")]
        public string SupplierCategory { get; set; }
        /// <summary>
        ///采购组织代码
        /// </summary>
        [Description("采购组织代码")]
        public string POrganizationCode { get; set; }
        /// <summary>
        ///订货货币代码
        /// </summary>
        [Description("订货货币代码")]
        public string CurrencyCode { get; set; }
        /// <summary>
        ///订货货币描述
        /// </summary>
        [Description("订货货币描述")]
        public string CurrencyDisc { get; set; }
        /// <summary>
        ///采购付款条件代码
        /// </summary>
        [Description("采购付款条件代码")]
        public string PaymentTermCode { get; set; }
        /// <summary>
        ///采购入款条件描述
        /// </summary>
        [Description("采购入款条件描述")]
        public string PaymentTermDisc { get; set; }
        /// <summary>
        ///发票校验
        /// </summary>
        [Description("发票校验")]
        public string InvoiceVerification { get; set; }
        /// <summary>
        ///送货方式货运、自备车、物流、快递
        /// </summary>
        [Description("送货方式货运、自备车、物流、快递")]
        public string DeliveryMethod { get; set; }
        /// <summary>
        ///车牌号
        /// </summary>
        [Description("车牌号")]
        public string CarNumber { get; set; }
        /// <summary>
        /// 供应商语言
        /// </summary>
        [Description("供应商语言")]
        public string SupplierLanguage { get; set; }
        /// <summary>
        /// 银行国家代码
        /// </summary>
        [Description("银行国家代码")]
        public string BankCountryCode { get; set; }
        /// <summary>
        /// 统驭科目
        /// </summary>
        [Description("统驭科目")]
        public string ControlSubject { get; set; }
        /// <summary>
        /// 付款方式
        /// </summary>
        [Description("付款方式")]
        public string PayMode { get; set; }
        /// <summary>
        ///内外部
        /// </summary>
        [Description("内外部")]
        public string InOrOut { get; set; }
        /// <summary>
        /// 所属采购员
        /// </summary>
        [Description("所属采购员")]
        public string BuyerCode { get; set; }
        /// <summary>
        /// 采购员名称
        /// </summary>
        [Description("采购员名称")]
        public string BuyerName { get; set; }
        /// <summary>
        /// 下一个审批人
        /// </summary>
        public string NextCheckUserCode { get; set; }
        /// <summary>
        /// 下一个审批人名称
        /// </summary>
        public string NextCheckUserName { get; set; }
        /// <summary>
        /// 审批序列
        /// </summary>
        public int? AuditSequence { get; set; }
        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactPerson { get; set; }
    }
}
