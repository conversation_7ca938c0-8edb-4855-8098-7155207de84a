using System;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 设备采购跟踪
    /// </summary>
    public class S_EquipPurchase : BaseEntity
    {
        ///<summary>
        ///主键ID
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        ///<summary>
        ///立项编号
        ///</summary>
        public string EPNO { get; set; }

        ///<summary>
        ///申请人
        ///</summary>
        public string Sponsor { get; set; }

        ///<summary>
        ///申请部门
        ///</summary>
        public string Dept { get; set; }

        ///<summary>
        ///合同编号
        ///</summary>
        public string ContractCode { get; set; }

        ///<summary>
        ///
        ///</summary>
        public string ContractName { get; set; }

        ///<summary>
        ///合同内容
        ///</summary>
        public string Description { get; set; }

        ///<summary>
        ///设备名称
        ///</summary>
        public string EquipName { get; set; }

        ///<summary>
        ///规格
        ///</summary>
        public string Spec { get; set; }

        ///<summary>
        ///数量
        ///</summary>
        public decimal? Qty { get; set; }

        ///<summary>
        ///立项金额
        ///</summary>
        public decimal? EPAmount { get; set; }

        ///<summary>
        ///寻源方式  ：比价、招标
        ///</summary>
        public string SourceMode { get; set; }

        ///<summary>
        ///进度说明
        ///</summary>
        public string ProgressDesc { get; set; }

        ///<summary>
        ///预计时间
        ///</summary>
        public DateTime? EstimatedTime { get; set; }

        ///<summary>
        ///招标进度  内部申请、招标邀请、招标结果
        ///</summary>
        public string BidProgress { get; set; }

        ///<summary>
        ///中标供方
        ///</summary>
        public string WinBidSupplier { get; set; }

        ///<summary>
        ///中标金额
        ///</summary>
        public decimal? WinBidAmount { get; set; }

        ///<summary>
        ///交期
        ///</summary>
        public DateTime? DeliveryDate { get; set; }

        ///<summary>
        ///付款条件
        ///</summary>
        public string Payment { get; set; }

        ///<summary>
        ///请款状态 预付款、预验收、终验收、质保金、履约保证金、投标保证金
        ///</summary>
        public string ClaimStatus { get; set; }

        /// <summary>
        /// 指派用户编码
        /// </summary>
        public string AssignUserCode { get; set; }

        /// <summary>
        /// 指派用户名称
        /// </summary>
        public string AssignUserName { get; set; }
    }
}