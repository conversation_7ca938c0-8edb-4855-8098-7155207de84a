using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 奖励/处置返还申请单
    /// </summary>
    public class S_RRDF : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { set; get; }

        /// <summary>
        /// 奖励/扣款返还申请单号
        /// </summary>
        [Description("申请单号")]
        public string RNo { set; get; }

        /// <summary>
        /// 原处置单号
        /// </summary>
        [Description("原处置单号")]
        public string OriginalNo { set; get; }

        /// <summary>
        /// 单据类型 3：奖励返还 4：扣款返还
        /// </summary>
        [Description("返还类型")]
        public string ApplyType { set; get; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string CompanyCode { set; get; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        public string FactoryCode { set; get; }

        /// <summary>
        /// 供应商代码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { set; get; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { set; get; }

        /// <summary>
        /// 状态 1-已申请 2：供应商管理已审核 3-质量中心（品质部）已审核  4-财务副总已审核   5-总经理已审核 6：财务已签收 10：已完成
        /// </summary>
        [Description("状态")]
        public string Status { set; get; }

        /// <summary>
        /// 申请返还金额
        /// </summary>
        [Description("返还金额")]
        public decimal? DisposalMoney { set; get; }

        ///// <summary>
        ///// 发起部门
        ///// </summary>
        //public string InitiatingDept
        //{
        //    set;
        //    get;
        //}
        /// <summary>
        /// 指定SQE用户名称
        /// </summary>
        [Description("SQE")]
        public string Initiatinger { set; get; }

        /// <summary>
        /// 申请日期
        /// </summary>
        [Description("申请日期")]
        public DateTime? DisposalDate { set; get; }

        /// <summary>
        /// 问题出处 0-进货检验 1-使用过程  2-其他
        /// </summary>
        [Description("问题出处")]
        public string ProblemSource { set; get; }

        /// <summary>
        /// 申请事项
        /// </summary>
        [Description("申请事项")]
        public string ProblemDescription { set; get; }

        /// <summary>
        /// 申请理由阐述
        /// </summary>
        [Description("申请理由阐述")]
        public string HandlingOpinions { set; get; }

        /// <summary>
        /// 供应商管理意见
        /// </summary>
        public string SupplyOptions { set; get; }

        /// <summary>
        /// 供应商管理签字确认人
        /// </summary>
        public string SupplyUserName { set; get; }

        /// <summary>
        /// 供应商管理确认日期
        /// </summary>
        public string SupplyDate { set; get; }

        /// <summary>
        /// 品质部意见
        /// </summary>
        public string SQEOptions { set; get; }

        /// <summary>
        /// 品质部签字
        /// </summary>
        public string SQEUserName { set; get; }

        /// <summary>
        /// 品质部确认日期
        /// </summary>
        public string SQEDate { set; get; }

        /// <summary>
        /// 财务副总意见
        /// </summary>
        public string FinanceOptions { set; get; }

        /// <summary>
        /// 财务副总确认人
        /// </summary>
        public string FinanceUserName { set; get; }

        /// <summary>
        /// 财务副总确认日期
        /// </summary>
        public string FinanceDate { set; get; }

        /// <summary>
        /// 总经理处理意见
        /// </summary>
        public string GMOptions { set; get; }

        /// <summary>
        /// 总经理处理确认签字
        /// </summary>
        public string GMOUserName { set; get; }

        /// <summary>
        /// 总经理处理签字确认时间
        /// </summary>
        public string GMODate { set; get; }

        /// <summary>
        /// 财务部门签收意见
        /// </summary>
        public string FinanceSignOptions { set; get; }

        /// <summary>
        /// 财务部门签收确认人
        /// </summary>
        public string FinanceSignUserName { set; get; }

        /// <summary>
        /// 财务部门签收日期
        /// </summary>
        public string FinanceSignDate { set; get; }

        /// <summary>
        /// 指定SQE
        /// </summary>
        public string AssignSQE { get; set; }
    }
}