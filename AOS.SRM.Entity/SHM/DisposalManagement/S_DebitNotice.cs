using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 扣款通知单
    /// </summary>
    public class S_DebitNotice : BaseEntity
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Description("主键id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// 处置单号
        /// </summary>
        [Description("处置单号")]
        public string DisposalOrder { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }

        /// <summary>
        /// 供应商代码
        /// </summary>
        [Description("供应商代码")]
        public string SupplyCode { get; set; }

        /// <summary>
        /// 发起部门
        /// </summary>
        [Description("发起部门")]
        public string InitiatingDept { get; set; }

        /// <summary>
        /// 发起人
        /// </summary>
        [Description("发起人")]
        public string Initiatinger { get; set; }

        /// <summary>
        /// 问题来源
        /// </summary>
        [Description("问题报告处")]
        public string ProblemSource { get; set; }

        /// <summary>
        /// 处置日期
        /// </summary>
        [Description("处置日期")]
        public DateTime? DisposalDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 状态  1：已下达 2：供应已接收 3：部门已审核 4：经理已审核 5：已签收 10:已完成 
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }

        /// <summary>
        /// 问题描述
        /// </summary>
        [Description("问题描述")]
        public string ProblemDescription { get; set; }

        /// <summary>
        /// 处理意见
        /// </summary>
        [Description("处理意见")]
        public string HandlingOpinions { get; set; }

        /// <summary>
        /// 处理用户签字
        /// </summary>
        [Description("处理用户签字")]
        public string HandlingUserName { get; set; }

        /// <summary>
        /// 处理用户签字确认日期
        /// </summary>
        [Description("处理用户签字确认日期")]
        public string HandlingDate { get; set; }

        /// <summary>
        /// 发出部门意见
        /// </summary>
        [Description("发出部门意见")]
        public string RelDeptOptions { get; set; }

        /// <summary>
        /// 发出部门签字确认人
        /// </summary>
        [Description("发出部门签字确认人")]
        public string RelDeptUserName { get; set; }

        /// <summary>
        /// 发出部门签字日期
        /// </summary>
        [Description("发出部门签字日期")]
        public string RelDeptDate { get; set; }

        /// <summary>
        /// 扣款金额
        /// </summary>
        [Description("扣款金额")]
        public decimal DisposalMoney { get; set; }

        /// <summary>
        /// 总经理处理意见
        /// </summary>
        [Description("总经理处理意见")]
        public string GMOptions { get; set; }

        /// <summary>
        /// 总经理处理确认签字
        /// </summary>
        [Description("总经理处理确认签字")]
        public string GMOUserName { get; set; }

        /// <summary>
        /// 总经理处理确认签字时间
        /// </summary>
        [Description("总经理处理确认签字时间")]
        public string GMODate { get; set; }

        /// <summary>
        /// 供应商意见
        /// </summary>
        [Description("供应商意见")]
        public string SupplyOptions { get; set; }

        /// <summary>
        /// 供应商回复确认人
        /// </summary>
        [Description("供应商回复确认人")]
        public string SupplyUserName { get; set; }

        /// <summary>
        /// 供应商回复日期
        /// </summary>
        [Description("供应商回复日期")]
        public string SupplyDate { get; set; }

        /// <summary>
        /// 财务签收意见
        /// </summary>
        [Description("财务签收意见")]
        public string FinanceOptions { get; set; }

        /// <summary>
        /// 财务签收人
        /// </summary>
        [Description("财务签收人")]
        public string FinanceUserName { get; set; }

        /// <summary>
        /// 财务签收日期
        /// </summary>
        [Description("财务签收日期")]
        public string FinanceDate { get; set; }
    }
}