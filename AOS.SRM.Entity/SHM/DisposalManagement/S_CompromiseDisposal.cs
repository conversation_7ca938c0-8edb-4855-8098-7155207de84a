using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 让步接收处置单主表
    /// </summary>
    public class S_CompromiseDisposal : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { set; get; }

        /// <summary>
        /// 处置单号
        /// </summary>
        [Description("处置单号")]
        public string DisposalNo { set; get; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string CompanyCode { set; get; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        public string FactoryCode { set; get; }

        /// <summary>
        /// 供应商代码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { set; get; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { set; get; }

        /// <summary>
        /// 状态  0-未下达 1-已下发 2-供方已接收 3-部门已审核 4：总经理已审核 5：财务接收 6：财务定价 10：已完成
        /// </summary>
        [Description("状态")]
        public string Status { set; get; }

        /// <summary>
        /// 发起部门
        /// </summary>
        [Description("发起部门")]
        public string InitiatingDept { set; get; }

        /// <summary>
        /// 发起人
        /// </summary>
        [Description("发起人")]
        public string Initiatinger { set; get; }

        /// <summary>
        /// 问题来源 0-进货让步  1-使用过程让步 2-偏差替代让步 3-其它
        /// </summary>
        [Description("问题来源")]
        public string ProblemSource { set; get; }

        /// <summary>
        /// 处置日期
        /// </summary>
        [Description("处置日期")]
        public DateTime? DisposalDate { set; get; }

        /// <summary>
        /// 处置金额
        /// </summary>
        [Description("处置金额")]
        public decimal? DisposalMoney { set; get; }

        /// <summary>
        /// 问题描述
        /// </summary>
        [Description("问题描述")]
        public string ProblemDescription { set; get; }

        /// <summary>
        /// 处理意见
        /// </summary>
        [Description("处理意见")]
        public string HandlingOpinions { set; get; }

        /// <summary>
        /// 处理用户签字
        /// </summary>
        public string HandlingUserName { set; get; }

        /// <summary>
        /// 处理用户签字确认日期
        /// </summary>
        public string HandlingDate { set; get; }

        /// <summary>
        /// 发出部门意见
        /// </summary>
        public string RelDeptOptions { set; get; }

        /// <summary>
        /// 发出部门签字确认人
        /// </summary>
        public string RelDeptUserName { set; get; }

        /// <summary>
        /// 发布部门签字日期
        /// </summary>
        public string RelDeptDate { set; get; }

        /// <summary>
        /// 总经理处理意见
        /// </summary>
        public string GMOptions { set; get; }

        /// <summary>
        /// 总经理处理确认签字
        /// </summary>
        public string GMOUserName { set; get; }

        /// <summary>
        /// 总经理处理签字确认时间
        /// </summary>
        public string GMODate { set; get; }

        /// <summary>
        /// 供应商回复意见
        /// </summary>
        public string SupplyOptions { set; get; }

        /// <summary>
        /// 供应商回复确认人
        /// </summary>
        public string SupplyUserName { set; get; }

        /// <summary>
        /// 供应商回复日期
        /// </summary>
        public string SupplyDate { set; get; }

        /// <summary>
        /// 财务签收意见
        /// </summary>
        public string FinanceOptions { set; get; }

        /// <summary>
        /// 财务签收人
        /// </summary>
        public string FinanceUserName { set; get; }

        /// <summary>
        /// 财务签收日期
        /// </summary>
        public string FinanceDate { set; get; }

        /// <summary>
        /// 财务定价人
        /// </summary>
        public string MakePriceUser { get; set; }

        /// <summary>
        /// 财务定价时间
        /// </summary>
        public DateTime? MakePriceDate { get; set; }
    }
}