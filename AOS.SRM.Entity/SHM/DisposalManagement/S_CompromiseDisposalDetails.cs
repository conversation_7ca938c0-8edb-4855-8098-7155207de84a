using SqlSugar;

namespace AOS.SRM.Entity.SHM.DisposalManagement
{
    /// <summary>
    /// 让步接收处置订单明细表
    /// </summary>
    public partial class S_CompromiseDisposalDetails : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { set; get; }

        /// <summary>
        /// 不合格处置单号
        /// </summary>
        public string DisposalNo { set; get; }

        /// <summary>
        /// 序号
        /// </summary>
        public int? LineNum { set; get; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string CompanyCode { set; get; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        public string FactoryCode { set; get; }

        /// <summary>
        /// 供应商代码
        /// </summary>
        public string SupplyCode { set; get; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string ItemCode { set; get; }

        /// <summary>
        /// 物料描述
        /// </summary>
        public string ItemName { set; get; }

        /// <summary>
        /// 处置数量
        /// </summary>
        public int? Quantity { set; get; }

        /// <summary>
        /// 让步比例
        /// </summary>
        public string Proportion { set; get; }

        /// <summary>
        /// 处置金额
        /// </summary>
        public decimal? DisposalMoney { set; get; }

        /// <summary>
        /// 价格
        /// </summary>
        public decimal? UnitPrice { get; set; }
    }
}