using System;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 综合绩效考核
    /// </summary>
    public class S_PerformanceAppraisal : BaseEntity
    {
        ///<summary>
        ///主键
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 单据编号
        /// </summary>
        public string DocNum { get; set; }

        /// <summary>
        /// 行号
        /// </summary>
        public int? Line { get; set; }

        ///<summary>
        ///供应商编码
        ///</summary>
        public string SupplierCode { get; set; }

        ///<summary>
        ///供应商名称
        ///</summary>
        public string SupplierName { get; set; }

        /// <summary>
        /// 考核年度
        /// </summary>
        public string AssessYear { get; set; }

        ///<summary>
        ///主要供应产品
        ///</summary>
        public string SupplyProducts { get; set; }

        ///<summary>
        ///类别
        ///</summary>
        public string Category { get; set; }

        ///<summary>
        ///考核方式
        ///</summary>
        public string AssessMode { get; set; }

        ///<summary>
        ///PPM/FPY
        ///</summary>
        public decimal? Q_PPMFPY { get; set; }

        ///<summary>
        ///过程影响数
        ///</summary>
        public decimal? Q_Process { get; set; }

        ///<summary>
        ///改进验证关闭率
        ///</summary>
        public decimal? Q_Improved { get; set; }

        ///<summary>
        ///逃逸数
        ///</summary>
        public decimal? Q_Escape { get; set; }

        ///<summary>
        ///AMT
        ///</summary>
        public decimal? Q_AMT { get; set; }

        ///<summary>
        ///加分项
        ///</summary>
        public decimal? Q_AddPoints { get; set; }

        ///<summary>
        ///扣分项
        ///</summary>
        public decimal? Q_DeducPoints { get; set; }

        ///<summary>
        ///质量绩效总分
        ///</summary>
        public decimal? Q_Score { get; set; }

        ///<summary>
        ///及时交货率
        ///</summary>
        public decimal? D_Timely { get; set; }

        ///<summary>
        ///缺件关闭率
        ///</summary>
        public decimal? D_MissPart { get; set; }

        ///<summary>
        ///诚信服务
        ///</summary>
        public decimal? D_Integrity { get; set; }

        ///<summary>
        ///交货绩效得分
        ///</summary>
        public decimal? D_Score { get; set; }

        ///<summary>
        ///服务质量合作遵守
        ///</summary>
        public decimal? C_SQCC { get; set; }

        ///<summary>
        ///合作绩效得分
        ///</summary>
        public decimal? C_Score { get; set; }

        ///<summary>
        ///新产品交期和技术配合
        ///</summary>
        public decimal? T_Technical { get; set; }

        ///<summary>
        ///配合绩效得分
        ///</summary>
        public decimal? T_Score { get; set; }

        ///<summary>
        ///综合评分
        ///</summary>
        public decimal? TotalScore { get; set; }

        ///<summary>
        ///评定等级
        ///</summary>
        public string Rating { get; set; }

        /// <summary>
        /// 质量绩效操作人
        /// </summary>
        public string Q_User { get; set; }

        /// <summary>
        /// 质量绩效操作时间
        /// </summary>
        public DateTime? Q_Time { get; set; }

        /// <summary>
        /// 交货绩效操作人
        /// </summary>
        public string D_User { get; set; }

        /// <summary>
        /// 交货绩效操作时间
        /// </summary>
        public DateTime? D_Time { get; set; }

        /// <summary>
        /// 合作绩效操作人
        /// </summary>
        public string C_User { get; set; }

        /// <summary>
        /// 合作绩效操作时间
        /// </summary>
        public DateTime? C_Time { get; set; }

        /// <summary>
        /// 配合绩效操作人
        /// </summary>
        public string T_User { get; set; }

        /// <summary>
        /// 配合绩效操作时间
        /// </summary>
        public DateTime? T_Time { get; set; }

        /// <summary>
        /// 操作序列
        /// </summary>
        public int? Sequence { get; set; }

        /// <summary>
        /// 下一个操作人编码
        /// </summary>
        public string NextChkUserCode { get; set; }

        /// <summary>
        /// 下一个操作人名称
        /// </summary>
        public string NextChkUserName { get; set; }
    }
}