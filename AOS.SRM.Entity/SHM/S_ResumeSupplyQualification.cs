using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 
    /// </summary>
    public class S_ResumeSupplyQualification : BaseEntity
    {
        ///<summary>
        ///主键
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        ///<summary>
        ///单据编号
        ///</summary>
        public string DocNum { get; set; }

        ///<summary>
        ///供应商编码
        ///</summary>
        public string SupplierCode { get; set; }

        ///<summary>
        ///供应商名称
        ///</summary>
        public string SupplierName { get; set; }

        ///<summary>
        ///问题描述
        ///</summary>
        public string Description { get; set; }

        ///<summary>
        ///改进方案
        ///</summary>
        public string ImproveMethod { get; set; }

        /// <summary>
        /// 改进确认
        /// </summary>
        public string ImproveConfirm { get; set; }

        ///<summary>
        ///状态  已提交、审批中、已通过、
        ///</summary>
        public string Status { get; set; }

        ///<summary>
        ///
        ///</summary>
        public string NextCheckUserCode { get; set; }

        ///<summary>
        ///
        ///</summary>
        public string NextCheckUserName { get; set; }

        ///<summary>
        ///审批序列
        ///</summary>
        public int? AuditSequence { get; set; }
    }
}