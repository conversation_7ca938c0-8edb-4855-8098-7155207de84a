using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 取消供货资格
    /// </summary>
    public class S_DisqualifySupply : BaseEntity
    {
        ///<summary>
        ///主键
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        ///<summary>
        ///单据编号
        ///</summary>
        public string DocNum { get; set; }

        ///<summary>
        ///发起人
        ///</summary>
        public string Sponsor { get; set; }

        ///<summary>
        ///发起部门
        ///</summary>
        public string Dept { get; set; }

        ///<summary>
        ///取消原因
        ///</summary>
        public string CancelReason { get; set; }

        ///<summary>
        ///供应商编码
        ///</summary>
        public string SupplierCode { get; set; }

        ///<summary>
        ///供应商名称
        ///</summary>
        public string SupplierName { get; set; }

        ///<summary>
        ///详细描述
        ///</summary>
        public string Description { get; set; }

        ///<summary>
        ///状态
        ///</summary>
        public string Status { get; set; }

        /// <summary>
        /// 下一个审批人
        /// </summary>
        public string NextCheckUserCode { get; set; }

        /// <summary>
        /// 下一个审批人名称
        /// </summary>
        public string NextCheckUserName { get; set; }

        /// <summary>
        /// 审批序列
        /// </summary>
        public int? AuditSequence { get; set; }
    }
}