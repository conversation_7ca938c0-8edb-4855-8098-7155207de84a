using AOS.SRM.Entity.SHM.DisposalManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SHM.ViewModel
{
    /// <summary>
    /// 让步接收单
    /// </summary>
    public class ConcessionAcceptDto
    {
        /// <summary>
        /// 让步接收主表信息
        /// </summary>
        public S_CompromiseDisposal main { get; set; }
        /// <summary>
        /// 让步接收单明细
        /// </summary>
        public List<S_CompromiseDisposalDetails> detail { get; set; }
    }
}
