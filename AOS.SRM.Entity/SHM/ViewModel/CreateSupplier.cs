using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SHM.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class CreateSupplier
    {
        /// <summary>
        /// 
        /// </summary>
        public S_SupplierInfo entity { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string[] fileIds { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class ChangeSupplier
    {
        /// <summary>
        /// 
        /// </summary>
        public S_SupplierChange entity { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string[] fileIds { get; set; }
    }
}
