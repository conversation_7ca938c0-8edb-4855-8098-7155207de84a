using AOS.SRM.Entity.SHM.DisposalManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.SHM.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class I_CompromiseDisposal_Views
    {
        /// <summary>
        /// 是否启用自动过账
        /// </summary>
        // public bool IsPostAccount { get; set; }

        //处置单号
        public string DisposalNo { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        public string CompanyCode { get; set; }
        /// <summary>
        /// 序号
        /// </summary>
        public int? LineNum { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }
        /// <summary>
        /// 供应商代码
        /// </summary>
        public string SupplyCode { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 发起部门
        /// </summary>
        public string InitiatingDept { get; set; }
        /// <summary>
        /// 发起人
        /// </summary>
        public string Initiatinger { get; set; }
        /// <summary>
        /// 处置日期
        /// </summary>
        public DateTime DisposalDate { get; set; }
        /// <summary>
        /// 问题来源
        /// </summary>
        public string ProblemSource { get; set; }
        /// <summary>
        /// 问题描述
        /// </summary>
        public string ProblemDescription { get; set; }
        /// <summary>
        /// 处理意见
        /// </summary>
        public string HandlingOpinions { get; set; }
        /// <summary>
        /// 处置总金额
        /// </summary>
        public decimal DisposalMoney { get; set; }
        /// <summary>
        /// 让步接收明细
        /// </summary>
        public List<S_CompromiseDisposalDetails> CompromiseDisposalDetails { get; set; }
    }
}
