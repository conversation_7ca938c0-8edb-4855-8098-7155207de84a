using System;
using System.Collections.Generic;
using AOS.SRM.Entity.SHM.DisposalManagement;
using SqlSugar;

namespace AOS.SRM.Application.SHM
{
    /// <summary>
    /// 让步接收处置单  列表
    /// </summary>
    public class SHM_CompromiseDisposal_View
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 处置单号
        /// </summary>
        public string DisposalNo { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        public string CompanyCode { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int? Quantity { get; set; }

        /// <summary>
        /// 让步比例
        /// </summary>
        public string Proportion { get; set; }

        /// <summary>
        /// 问题描述
        /// </summary>
        public string ProblemDescription { get; set; }

        /// <summary>
        /// 问题来源
        /// </summary>
        public string ProblemSource { get; set; }

        /// <summary>
        /// 处置金额
        /// </summary>
        public decimal? DisposalMoney { get; set; }

        /// <summary>
        /// 处置金额
        /// </summary>
        public DateTime? DisposalDate { get; set; }

        /// <summary>
        /// 发起部门
        /// </summary>
        public string InitiatingDept { get; set; }

        /// <summary>
        /// 发起人
        /// </summary>
        public string Initiatinger { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 删除标识
        /// </summary>
        public bool? IsDelete { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<S_CompromiseDisposalDetails> Details { get; set; }
    }
}