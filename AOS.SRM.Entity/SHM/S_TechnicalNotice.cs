using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 技术通知单
    /// </summary>
    public class S_TechnicalNotice : BaseEntity
    {
        ///<summary>
        ///主键Id
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        ///<summary>
        ///单据编号
        ///</summary>
        public string DocNum { get; set; }

        ///<summary>
        ///发起人
        ///</summary>
        public string Sponsor { get; set; }

        ///<summary>
        ///发起部门
        ///</summary>
        public string Dept { get; set; }

        ///<summary>
        ///供应商编码
        ///</summary>
        public string SupplierCode { get; set; }

        ///<summary>
        ///供应商名称
        ///</summary>
        public string SupplierName { get; set; }

        ///<summary>
        ///状态  已提交、供方已经收
        ///</summary>
        public string Status { get; set; }

        ///<summary>
        ///附件Id
        ///</summary>
        public string AttachId { get; set; }
    }
}