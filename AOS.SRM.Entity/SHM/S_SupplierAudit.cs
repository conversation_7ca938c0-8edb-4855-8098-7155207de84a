using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 供应商注册审批
    /// </summary>
    public class S_SupplierAudit : BaseEntity
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Description("Id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        /// <summary>
        /// 审批类型 1：供应商注册 2：供应商变更
        /// </summary>
        public string CheckType { get; set; }
        /// <summary>
        /// 单据Id
        /// </summary>
        public string DocId { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string DocNum { get; set; }
        /// <summary>
        /// 供应商Id
        /// </summary>
        [Description("供应商Id")]
        public string SupplierId { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplierCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; }
        /// <summary>
        /// 审批顺序
        /// </summary>
        public int? AuditSequence { get; set; }
        /// <summary>
        /// 审批状态  1：同意 2：驳回
        /// </summary>
        public string AuditStatus { get; set; }
        /// <summary>
        /// 审批人
        /// </summary>
        public string AuditUser { get; set; }
        /// <summary>
        /// 审批时间
        /// </summary>
        public DateTime AuditTime { get; set; }
        /// <summary>
        /// 审批意见
        /// </summary>
        public string AuditOpinions { get; set; }
    }
}
