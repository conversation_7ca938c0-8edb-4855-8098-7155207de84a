using System;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 供应商警告函实体类
    /// </summary>
    public class S_SupplierWarningLetter : BaseEntity
    {
        ///<summary>
        ///主键
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        ///<summary>
        ///月度
        ///</summary>
        public string DocNum { get; set; }

        ///<summary>
        ///供应商编码
        ///</summary>
        public string SupplierCode { get; set; }

        ///<summary>
        ///供应商名称
        ///</summary>
        public string SupplierName { get; set; }

        ///<summary>
        ///质量绩效分
        ///</summary>
        public decimal? Q_Score { get; set; }

        ///<summary>
        ///交货绩效分
        ///</summary>
        public decimal? D_Score { get; set; }

        ///<summary>
        ///合作绩效分
        ///</summary>
        public decimal? C_Score { get; set; }

        ///<summary>
        ///配合绩效分
        ///</summary>
        public decimal? T_Score { get; set; }

        ///<summary>
        ///综合评分
        ///</summary>
        public decimal? TotalScore { get; set; }

        ///<summary>
        ///质量绩效评定结果
        ///</summary>
        public string Q_Rating { get; set; }

        ///<summary>
        ///交货绩效评定结果
        ///</summary>
        public string D_Rating { get; set; }

        ///<summary>
        ///综合评定结果
        ///</summary>
        public string Rating { get; set; }

        ///<summary>
        ///书面警告次数
        ///</summary>
        public int? WarnNumber { get; set; }

        ///<summary>
        ///改进措施提交截至日期
        ///</summary>
        public DateTime? DueDate { get; set; }

        ///<summary>
        ///处置结果
        ///</summary>
        public string DisposalResult { get; set; }

        /// <summary>
        /// 状态 0：未接收 1：已接收
        /// </summary>
        public string Status { get; set; }
    }
}