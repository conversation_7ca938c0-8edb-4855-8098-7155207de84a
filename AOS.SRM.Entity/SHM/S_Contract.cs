using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 合同
    /// </summary>
    public class S_Contract : BaseEntity
    {
        ///<summary>
        ///主键
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        ///<summary>
        ///合同编号
        ///</summary>
        public string ContractCode { get; set; }

        ///<summary>
        ///合同名称
        ///</summary>
        public string ContractName { get; set; }

        ///<summary>
        ///发起人
        ///</summary>
        public string Sponsor { get; set; }

        ///<summary>
        ///发起人部门
        ///</summary>
        public string Dept { get; set; }

        ///<summary>
        ///供应商编码
        ///</summary>
        public string SupplierCode { get; set; }

        ///<summary>
        ///供应商名称
        ///</summary>
        public string SupplierName { get; set; }

        ///<summary>
        ///合同类型：新供方、日常、原材料
        ///</summary>
        public string ContractType { get; set; }

        ///<summary>
        ///描述
        ///</summary>
        public string Description { get; set; }

        ///<summary>
        ///状态：已提交、供方已回传、采购已回传
        ///</summary>
        public string Status { get; set; }

        /// <summary>
        /// 附件Id
        /// </summary>
        public string AttachId { get; set; }
    }
}