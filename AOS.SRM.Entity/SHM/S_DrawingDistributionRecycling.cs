using System;
using SqlSugar;

namespace AOS.SRM.Entity.SHM
{
    /// <summary>
    /// 
    /// </summary>
    public class S_DrawingDistributionRecycling : BaseEntity
    {
        ///<summary>
        ///主键
        ///</summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        ///<summary>
        ///产品管理单号
        ///</summary>
        public string DocNum { get; set; }

        ///<summary>
        ///物料编号
        ///</summary>
        public string ItemCode { get; set; }

        ///<summary>
        ///旧件号
        ///</summary>
        public string OldItemCode { get; set; }

        ///<summary>
        ///物料名称
        ///</summary>
        public string ItemName { get; set; }

        ///<summary>
        ///图纸区分  新增、变更
        ///</summary>
        public string DrawingDiff { get; set; }

        ///<summary>
        ///接收日期
        ///</summary>
        public DateTime? ReceiveDate { get; set; }

        ///<summary>
        ///实施日期
        ///</summary>
        public DateTime? ImplementDate { get; set; }

        ///<summary>
        ///图纸类型 试制图纸、常规图纸
        ///</summary>
        public string PaperType { get; set; }

        ///<summary>
        ///采购员编号
        ///</summary>
        public string BuyerCode { get; set; }

        ///<summary>
        ///采购员名称
        ///</summary>
        public string BuyerName { get; set; }

        ///<summary>
        ///供应商编号，可多个
        ///</summary>
        public string SupplierCodes { get; set; }

        ///<summary>
        ///供应商名称，可多个
        ///</summary>
        public string SupplierNames { get; set; }

        ///<summary>
        ///发放日期
        ///</summary>
        public DateTime? DistributeDate { get; set; }

        ///<summary>
        ///
        ///</summary>
        public string Status { get; set; }
    }
}