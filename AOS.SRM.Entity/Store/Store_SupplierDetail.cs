using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 供应商库存
    /// </summary>
    [SugarTable("Store_SupplierDetail")]
    public class Store_SupplierDetail : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [Description("ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }

        /// <summary>
        /// PID
        /// </summary>
        [Description("PID")]
        public string PID { get; set; }

        /// <summary>
        /// 供应商代码
        /// </summary>
        [Description("供应商代码")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 件号
        /// </summary>
        [Description("件号")]
        public string PartNo { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 操作数量
        /// </summary>
        [Description("操作数量")]
        public int OperateNo { get; set; }

        /// <summary>
        /// 操作人
        /// </summary> 
        [Description("操作人")]
        public string Operator { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        [Description("操作类型")]
        public int OperateType { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [Description("操作时间")]
        public DateTime OperateTime { get; set; }
    }
}