using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Sys
{
    /// <summary>
    /// 供应商库存
    /// </summary>
    [SugarTable("Store_Supplier")]
    public class Store_Supplier : BaseEntity
    {
        /// <summary>
        /// ID
        /// </summary>
        [Description("ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ID { get; set; }

        /// <summary>
        /// 供应商代码
        /// </summary>
        [Description("供应商代码")]
        public string SupplierCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplierName { get; set; }

        /// <summary>
        /// 件号
        /// </summary>
        [Description("件号")]
        public string PartNo { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 在库数量
        /// </summary>
        [Description("在库数量")]
        public int StoreNo { get; set; }
    }
}