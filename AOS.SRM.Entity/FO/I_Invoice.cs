using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 开票单主表
    /// </summary>
    [SugarTable("I_Invoice")]
    public class I_Invoice : BaseEntity
    {
        /// <summary>
        /// 开票单主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("开票单主键ID")]
        public string Id { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }
        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }
        /// <summary>
        /// 开票单号
        /// </summary>
        [Description("开票单号")]
        public string InvoiceNo { get; set; }
        /// <summary>
        /// 开票时间
        /// </summary>
        public DateTime? InvoiceTime { get; set; }
        /// <summary>
        /// 开票人
        /// </summary>
        [Description("开票人")]
        public string Invoicer { get; set; }
        /// <summary>
        /// 发票单号
        /// </summary>
        [Description("发票单号")]
        public string BillingNo { get; set; }
        /// <summary>
        /// 开户银行
        /// </summary>
        [Description("开户银行")]
        public string BankDeposit { get; set; }
        /// <summary>
        /// 开户行账号
        /// </summary>
        [Description("开户行账号")]
        public string BankAccount { get; set; }
        /// <summary>
        /// 价税合计
        /// </summary>
        [Description("价税合计")]
        public decimal TotalPriceSum { get; set; }
        /// <summary>
        /// 纳税人识别号
        /// </summary>
        [Description("纳税人识别号")]
        public string TaxpayerNo { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        [Description("电话")]
        public string Tel { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        [Description("地址")]
        public string Address { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public string TaxRate { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public string TaxAmount { get; set; }
        /// <summary>
        /// 处置总金额
        /// </summary>
        public decimal? DisposalTotalMoney { get; set; }
        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceMoney { get; set; }
        /// <summary>
        /// 状态 1：已提报 2：已审核 3：已驳回 4：已完成 5:冲销已完成 6：收货已冲销 7：退货已冲销
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// SAP预制发票号
        /// </summary>
        public string SAPInvoiceNo { get; set; }
        /// <summary>
        /// SAP贷方凭证
        /// </summary>
        public string SAPInvoiceNo2 { get; set; }
        /// <summary>
        /// 是否已过账
        /// </summary>
        public bool? IsPosted { get; set; }
        /// <summary>
        /// 过账人
        /// </summary>
        public string PostUser { get; set; }
        /// <summary>
        /// 过账时间
        /// </summary>
        public DateTime? PostTime { get; set; }
        /// <summary>
        /// 单据类型 1 普通开票  2：物流开票
        /// </summary>
        public string DocType { get; set; }
        /// <summary>
        /// 是否过账
        /// </summary>
        public bool? IsPostedCredit { get; set; }
        /// <summary>
        /// 过账人
        /// </summary>
        public string PostUserCredit { get; set; }
        /// <summary>
        /// 过账时间
        /// </summary>
        public DateTime? PostTimeCredit { get; set; }
        /// <summary>
        /// 冲销凭证
        /// </summary>
        public string SAPNoCredit { get; set; }
        /// <summary>
        /// 冲销凭证2
        /// </summary>
        public string SAPNoCredit2{ get; set; }
        /// <summary>
        /// 会计年度
        /// </summary>
        public int? FISC_YEAR { get; set; }
        /// <summary>
        /// 调整金额
        /// </summary>
        public decimal? AdjustMoney { get; set; }
        /// <summary>
        /// 退货金额
        /// </summary>
        public decimal? ReturnMoney { get; set; }
        /// <summary>
        /// 发票扫描文件路径
        /// </summary>
        public string BillScanPath { get; set; }

        /// <summary>
        /// 发票扫描文件名称
        /// </summary>
        public string BillScanName { get; set; }
    }
}
