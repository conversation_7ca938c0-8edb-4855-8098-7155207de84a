using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using SqlSugar;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 开票单主表
    /// </summary>
    [SugarTable("I_InvoiceDetails")]
    public class I_InvoiceDetails : BaseEntity
    {
        /// <summary>
        /// 开票单主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("开票单主键ID")]
        public string Id { get; set; }
        /// <summary>
        /// 主表主键
        /// </summary>
        public string ParentId { get; set; }
        /// <summary>
        /// 报检单号
        /// </summary>
        [Description("报检单号")]
        public string InspectionNo { get; set; }
        /// <summary>
        /// 报检单行号
        /// </summary>
        [Description("报检单行号")]
        public int? InspectionLine { get; set; }
        /// <summary>
        /// 收货单号
        /// </summary>
        [Description("收货单号")]
        public string ReceiptNo { get; set; }
        /// <summary>
        /// 采购订单号
        /// </summary>
        [Description("采购订单号")]
        public string OrderNo { get; set; }
        /// <summary>
        /// 采购订单行号
        /// </summary>
        [Description("采购订单行号")]
        public int? OrderLine { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }
        /// <summary>
        /// 旧物料编码
        /// </summary>
        [Description("旧物料编码")]
        public string BISMT { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary>
        /// 采购收货数量
        /// </summary>
        [Description("收货数量")]
        public decimal? PurchaseReceiptQty { get; set; }
        /// <summary>
        /// 库存单位
        /// </summary>
        [Description("库存单位")]
        public string Unit { get; set; }
        /// <summary>
        /// 采购价格
        /// </summary>
        [Description("采购单价")]
        public decimal? PurchasePrice { get; set; }
        /// <summary>
        /// 结算单价
        /// </summary>
        [Description("结算单价")]
        public decimal? SettleUnitPrice { get; set; }
        /// <summary>
        /// 结算金额
        /// </summary>
        [Description("结算金额")]
        public decimal? SettlementPrice { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        [Description("税额")]
        public string TaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        [Description("税率")]
        public string TaxRate { get; set; }
        /// <summary>
        /// 含税金额
        /// </summary>
        [Description("含税金额")]
        public decimal? AmountWithTax { get; set; }
        /// <summary>
        /// 采购订单行备注
        /// </summary>
        [Description("备注")]
        public string ZTEXT { get; set; }
        /// <summary>
        /// WMS采购守护Id
        /// </summary>
        public string PurchaseReceiptID { get; set; }
        /// <summary>
        /// 物料凭证号
        /// </summary>
        [Description("物料凭证号")]
        public string SapDocNum { get; set; }
        /// <summary>
        /// 物料凭证项目
        /// </summary>
        public int? SapLine { get; set; }
        /// <summary>
        /// 订单类型  1：标准 2：委外
        /// </summary>
        [Description("订单类型")]
        public string OrderType { get; set; }
        /// <summary>
        /// 订单创建日期
        /// </summary>
        [Description("下单日期")]
        public DateTime? OrderCTime { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        [Description("单据编号")]
        public string BillingNo { get; set; }
        /// <summary>
        /// 业务状态码
        /// </summary>
        [Description("业务状态码")]
        public string StatusCode { get; set; }
        /// <summary>
        /// 公司
        /// </summary>
        [Description("公司")]
        public string CompanyCode { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }
    }
}
