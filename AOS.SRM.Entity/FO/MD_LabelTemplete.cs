using SqlSugar;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 标签模板
    /// </summary>
    public class MD_LabelTemplete : BaseEntity
    {
        /// <summary> 
        /// 模板ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        public string TempleteID { get; set; }
        /// <summary>
        /// 模板描述
        /// </summary>
        public string TempleteDesc { get; set; }
        /// <summary>
        /// 模板类型
        /// </summary>
        public int TempleteType { get; set; }
        /// <summary>
        /// 模板文件
        /// </summary>
        public string TempleteFile { get; set; }
        /// <summary>
        /// 是否有效
        /// </summary>
        public bool? IsEnable { get; set; }
    }
}
