using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 采购订单创建、采购订单收货
    /// </summary>
    [SugarTable("P_ConsignmentNote")]
    public class P_ConsignmentNote : BaseEntity
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键ID")]
        public string Id { get; set; }
        /// <summary>
        /// 父Id
        /// </summary>
        public string ParentId { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }
        /// <summary>
        /// 物流单号
        /// </summary>
        public string LogisticsNo { get; set; }
        /// <summary>
        /// 物流单行号
        /// </summary>
        public int? Line { get; set; }
        /// <summary>
        /// 销售订单号
        /// </summary>
        public string SaleNo { get; set; }
        /// <summary>
        /// 销售订单行号
        /// </summary>
        public int? SaleLine { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string CostCenter { get; set; }
        /// <summary>
        /// 物料编号
        /// </summary>
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        public string ItemName { get; set; }
        /// <summary>
        /// 物料组编码
        /// </summary>
        public string ItmsGrpCode { get; set; }
        /// <summary>
        /// 物料组名称
        /// </summary>
        public string ItmsGrpName { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Qty { get; set; }
        /// <summary>
        /// 库存计量单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 发货日期
        /// </summary>
        public DateTime? DeliverDate { get; set; }
        /// <summary>
        /// 行理论金额
        /// </summary>
        public decimal? RowTheoryAmount { get; set; }
        /// <summary>
        /// 行实际金额
        /// </summary>
        public decimal? RowActualAmount { get; set; }
        /// <summary>
        /// SAP采购订单
        /// </summary>
        public string SAPDocNum { get; set; }
        /// <summary>
        /// SAP采购订单行号
        /// </summary>
        public int? SAPLine { get; set; }
        /// <summary>
        /// SAP物料凭证号（收货单）
        /// </summary>
        public string SAPMaterialNum { get; set; }
        /// <summary>
        /// SAP物料凭证项目（收货单行号）
        /// </summary>
        public int? SAPMaterialLine { get; set; }
        /// <summary>
        /// 开票申请Id
        /// </summary>
        public string InvoiceId { get; set; }
        /// <summary>
        /// 是否已过帐
        /// </summary>
        public bool? IsPosted { get; set; }
        /// <summary>
        /// 过账人
        /// </summary>
        public string PostUser { get; set; }
        /// <summary>
        /// 过账时间
        /// </summary>
        public DateTime? PostTime { get; set; }
        /// <summary>
        /// 过账行号
        /// </summary>
        public int? PostLine { get; set; }
    }
}
