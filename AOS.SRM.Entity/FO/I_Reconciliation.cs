using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 对账单
    /// </summary>
    [SugarTable("I_Reconciliation")]
    public class I_Reconciliation : BaseEntity
    {
        /// <summary>
        /// 开票单主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("开票单主键ID")]
        public string Id { get; set; }

        /// <summary>
        /// 对账单号
        /// </summary>
        [Description("对账单号")]
        public string ReconciliationNo { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }

        /// <summary>
        /// 对账日期
        /// </summary>
        [Description("对账日期")]
        public DateTime ReconciliationDate { get; set; }

        /// <summary>
        /// 申请人
        /// </summary>
        [Description("申请人")]
        public string Applyer { get; set; }

        /// <summary>
        /// 接收人
        /// </summary>
        [Description("接收人")]
        public string Receiver { get; set; }

        /// <summary>
        /// 接收日期
        /// </summary>
        [Description("接收日期")]
        public DateTime? ReceiveDate { get; set; }

        /// <summary>
        /// 1：已提交、2：已接收、3：已取消、4：已完成-在应付会计确认返还单时更新)；
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 路径
        /// </summary>
        [Description("路径")]
        public string Path { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        [Description("电话")]
        public string Phone { get; set; }

        /// <summary>
        /// 传真
        /// </summary>
        public string Fax { get; set; }

        /// <summary>
        /// 信箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 单据类型 1：普通对账 2：物流对账
        /// </summary>
        public string DocType { get; set; }

        /// <summary>
        /// 附件上传主键id
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// 上传附件名称
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 账面总金额
        /// </summary>
        [Description("账面总金额")]
        public decimal? BookTotalAmount { get; set; }

        /// <summary>
        /// 结算总金额
        /// </summary>
        [Description("结算总金额")]
        public decimal? SettleTotalAmount { get; set; }

        /// <summary>
        /// SAP采购订单
        /// </summary>
        [Description("SAP采购订单")]
        public string SAPDocNum { get; set; }

        /// <summary>
        /// SAP收货单
        /// </summary>
        [Description("SAP收货单")]
        public string SAPMaterialNum { get; set; }

        /// <summary>
        /// 驳回信息
        /// </summary>
        [Description("驳回信息")]
        public string ErrMsg { get; set; }
    }
}