using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 对账单明细
    /// </summary>
    [SugarTable("I_ReconciliationDetail")]
    public class I_ReconciliationDetail: BaseEntity
    {
        /// <summary>
        /// 对账单明细主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("开票单主键ID")]
        public string Id { get; set; }
        /// <summary>
        /// 父Id
        /// </summary>
        [Description("父Id")]
        public string ParentId { get; set; }
        /// <summary>
        /// SRM物流订单Id
        /// </summary>
        public string LogisticsId { get; set; }
        /// <summary>
        /// 物流单号
        /// </summary>
        [Description("物流单号")]
        public string LogisticsNo { get; set; }

        /// <summary>
        /// 客户编码
        /// </summary>
        public string CustomerCode { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }
        /// <summary>
        /// 物流供应商编码
        /// </summary>
        public string LogisticsSupplierCode { get; set; }
        /// <summary>
        /// 物流供应商名称
        /// </summary>
        public string LogisticsSupplierName { get; set; }
        /// <summary>
        /// 账面金额
        /// </summary>
        [Description("账面金额")]
        public decimal? BookAmount { get; set;}
        /// <summary>
        /// 结算金额
        /// </summary>
        [Description("结算金额")]
        public decimal? SettlementAmout { get; set; }
    }
}
