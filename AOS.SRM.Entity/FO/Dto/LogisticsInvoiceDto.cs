using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.FO.Dto
{
    /// <summary>
    /// 物流开票申请保存类
    /// </summary>
    public class LogisticsInvoiceDto
    {
        /// <summary>
        /// 主信息
        /// </summary>
        public I_Invoice main { get; set; }
        /// <summary>
        /// 开票明细
        /// </summary>
        public List<P_ConsignmentNote> LogisticsDetail { get; set; }
        /// <summary>
        /// 处置单据
        /// </summary>
        public List<I_InvoiceDisposal> disposal { get; set; }
    }
}
