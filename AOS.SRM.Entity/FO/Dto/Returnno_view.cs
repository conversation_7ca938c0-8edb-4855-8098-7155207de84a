using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.FO.Dto
{
    /// <summary>
    /// 返还单提交
    /// </summary>
    public class Returnno_view
    {
        /// <summary>
        /// 返还单号
        /// </summary>
        public string ReturnNo { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
        /// <summary>
        /// 西子富沃德对账金额 
        /// </summary>
        public decimal ReconciliationMoney { get; set; }
        /// <summary>
        /// 供应商对账金额
        /// </summary>
        public decimal SupplyReconciliationMoney { get; set; }
        /// <summary>
        /// 西子富沃德账面余额
        /// </summary>
        public decimal BalanceMoney { get; set; }
        /// <summary>
        /// 供应商账面余额
        /// </summary>
        public decimal SuppBalanceMoney { get; set; }

        /// <summary>
        /// 返还日期
        /// </summary>
        public DateTime ReturnDate { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 电子邮箱
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 传真
        /// </summary>
        public string Fax { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }
        /// <summary>
        /// 附件路径
        /// </summary>
        public string Path { get; set; }
        /// <summary>
        /// 附件表Id
        /// </summary>
        public string ParentId { get; set; }
        /// <summary>
        /// 附件名称
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<I_ReturnNote> Invoice { get; set; }
    }
}
