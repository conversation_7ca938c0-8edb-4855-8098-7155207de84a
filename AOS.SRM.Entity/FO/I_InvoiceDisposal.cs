using System;
using SqlSugar;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 开票申请-处置单据
    /// </summary>
    [SugarTable("I_InvoiceDisposal")]
    public class I_InvoiceDisposal : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        /// <summary>
        /// 开票申请单Id（父Id）
        /// </summary>
        public string InvoiceId { get; set; }
        /// <summary>
        /// 单据类型 1：不合格处置单 2：让步接收单 3：奖励处置单 4：返还处置单
        /// </summary>
        public string DocType { get; set; }
        /// <summary>
        /// 单据名称
        /// </summary>
        public string DocName { get; set; }
        /// <summary>
        /// 处置单据Id
        /// </summary>
        public string DisposalId { get; set; }
        /// <summary>
        /// 处置单号
        /// </summary>

        public string DisposalNo { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get;set; }
        /// <summary>
        /// 发起部门
        /// </summary>
        public string InitiatingDept { get; set; }
        /// <summary>
        /// 发起人
        /// </summary>
        public string Initiatinger { get; set; }
        /// <summary>
        /// 发起日期
        /// </summary>
        public DateTime? DisposalDate { get; set; }
        /// <summary>
        /// 问题来源
        /// </summary>
        public string ProblemSource { get; set; }
        /// <summary>
        /// 处置金额
        /// </summary>
        public decimal? DisposalMoney { get; set; }
        /// <summary>
        /// 问题描述
        /// </summary>
        public string ProblemDescription { get; set; }
    }
}
