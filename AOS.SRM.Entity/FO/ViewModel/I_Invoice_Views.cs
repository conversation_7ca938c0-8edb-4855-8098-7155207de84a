using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.FO.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class I_Invoice_Views
    {
        /// <summary>
        /// 是否启用自动过账
        /// </summary>
        // public bool IsPostAccount { get; set; }
        public string Id { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }
        /// <summary>
        /// 开票日期
        /// </summary>
        public DateTime? InvoiceTime { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 开票人
        /// </summary>
        public string Invoicer { get; set; }
        /// <summary>
        /// 发票单号
        /// </summary>
        public string InvoiceNo { get; set; }
        /// <summary>
        /// 开票单号
        /// </summary>
        public string BillingNo { get; set; }
        /// <summary>
        /// 开户行
        /// </summary>
        public string BankDeposit { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        public string BankAccount { get; set; }
        /// <summary>
        /// 价税合计
        /// </summary>
        public decimal TotalPriceSum { get; set; }
        /// <summary>
        /// 纳税人识别号
        /// </summary>
        public string TaxpayerNo { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string Tel { get; set; }
        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 税额
        /// </summary>
        public string TaxAmount { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        public string TaxRate { get; set; }
        /// <summary>
        /// 处置总金额
        /// </summary>
        public decimal? DisposalTotalMoney { get; set; }
        /// <summary>
        /// 开票金额
        /// </summary>
        public decimal? InvoiceMoney { get; set; }
        /// <summary>
        /// 年度调整金额
        /// </summary>
        public decimal? AdjustMoney { get; set; }
        /// <summary>
        /// 退货总金额
        /// </summary>
        public decimal? ReturnMoney { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        public string CompanyCode { get; set; }

        /// <summary>
        /// 开票申请明细
        /// </summary>
        public List<I_InvoiceDetails> Invoice { get; set; }
        /// <summary>
        /// 供应商处置单据明细
        /// </summary>
        public List<I_InvoiceDisposal> InvoiceDisposalDetail { get; set; }
        /// <summary>
        /// 发票扫描文件路径
        /// </summary>
        public string BillScanPath { get; set; }
        /// <summary>
        /// 发票扫描文件名称
        /// </summary>
        public string BillScanName { get; set; }
    }
}
