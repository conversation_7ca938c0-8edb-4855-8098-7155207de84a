using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.FO.ViewModel
{
    /// <summary>
    /// 收货记录
    /// </summary>
    public class FO_ReceivingDetails_View
    {
        /// <summary> 
        /// 采购收货主键ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("采购收货主键ID")]
        public string PurchaseReceiptID { get; set; }
        /// <summary>
        /// 报检单号
        /// </summary>
        [Description("报检单号")]
        public string InspectionNo { get; set; }
        /// <summary>
        /// 报检单行号
        /// </summary>
        [Description("报检单行号")]
        public int? InspectionLine { get; set; }
        /// <summary>
        /// 收货单号   采购、委外、退货单
        /// </summary>
        [Description("收货单号")]
        public string ReceiptNo { get; set; }
        /// <summary> 
        /// 采购单号
        /// </summary> 
        [Description("采购单号")]
        public string OrderNo { get; set; }
        /// <summary> 
        /// 采购行号
        /// </summary> 
        [Description("采购行号")]
        public int? OrderLine { get; set; }
        /// <summary>
        /// 采购订单类型
        /// </summary>
        public string BaseType { get; set; }
        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplyCode { get; set; }
        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplyName { get; set; }
        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary> 
        /// 采购收货数量
        /// </summary> 
        [Description("采购收货数量")]
        public decimal? PurchaseReceiptQty { get; set; }
        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit { get; set; }
        /// <summary>
        /// sap生成单号
        /// </summary>
        [Description("sap生成单号")]
        public string SapDocNum { get; set; }
        /// <summary>
        /// SAP生成行号
        /// </summary>
        [Description("SAP生成行号")]
        public int? SapLine { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CTime { get; set; }
        /// <summary>
        /// 税率
        /// </summary>
        [Description("税率")]
        public decimal? TaxRate { get; set; }
        /// <summary>
        /// 税额（总金额*税率） 获取结算价时计算
        /// </summary>
        [Description("税额")]
        public decimal? TaxAmount { get; set; }
        /// <summary>
        /// 采购组织
        /// </summary>
        [Description("采购组织")]
        public string Organization { get; set; }
        /// <summary>
        /// 项目类别
        /// </summary>
        [Description("项目类别")]
        public string ProjectType { get; set; }
        /// <summary>
        /// 业务状态码
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 采购订单创建日期
        /// </summary>
        [Description("采购订单创建日期")]
        public DateTime? OrderCTime { get; set; }
        /// <summary>
        /// 不含税单价
        /// </summary>
        [Description("不含税单价")]
        public decimal? PurchasePrice { get; set; }
        /// <summary>
        /// 结算单价
        /// </summary>
        [Description("结算单价")]
        public decimal? SettleUnitPrice { get; set; }
        /// <summary>
        /// 结算金额
        /// </summary>
        [Description("结算金额")]
        public decimal? SettlementPrice { get; set; }
        /// <summary>
        /// 含税总金额
        /// </summary>
        [Description("含税总金额")]
        public decimal? AmountWithTax { get; set; }
        /// <summary>
        /// 单据类型 1：标准 2：委外
        /// </summary>
        [Description("单据类型")]
        public string OrderType { get; set; }
        /// <summary>
        /// 行备注
        /// </summary>
        [Description("行备注")]
        public string ZTEXT { get; set; }
        /// <summary>
        /// 旧物料编码
        /// </summary>
        [Description("旧物料编码")]
        public string BISMT { get; set; }
        /// <summary>
        /// 工厂-cc-220713添加
        /// </summary>
        [Description("工厂")]
        public string WERKS { get; set; }
        /// <summary>
        /// 开票方
        /// </summary>
        [Description("开票方")]
        public string LIFRE { get; set; }
        /// <summary>
        /// 配送方
        /// </summary>
        [Description("配送方")]
        public string LLIEF { get; set; }
        /// <summary>
        /// 检查标记
        /// </summary>
        [Description("检查标记")]
        public bool? CheckFlag { get; set; }
    }
}
