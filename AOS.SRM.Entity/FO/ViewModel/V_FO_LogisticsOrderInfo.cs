using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.FO.ViewModel
{
    /// <summary>
    /// 物流对账-获取物流订单信息
    /// </summary>
    public class V_FO_LogisticsOrderInfo
    {
        /// <summary>
        /// 物流订单Id
        /// </summary>
        public string LogisticsId { get; set; }
        /// <summary>
        /// 物流订单号
        /// </summary>
        public string LogisticsNo { get; set; }
        /// <summary>
        /// 客户编码
        /// </summary>
        public string CustomerCode { get; set; }
        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { get; set; }
        /// <summary>
        /// 物流供应商编码
        /// </summary>
        public string LogisticsSupplierCode { get; set; }
        /// <summary>
        /// 物流供应商名称
        /// </summary>
        public string LogisticsSupplierName { get; set; }
        /// <summary>
        /// 账面金额
        /// </summary>
        public decimal? BookAmount { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? CTime { get; set; }
    }
}
