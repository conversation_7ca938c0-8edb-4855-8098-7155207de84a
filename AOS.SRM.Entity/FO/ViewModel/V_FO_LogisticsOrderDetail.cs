using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.FO.ViewModel
{
    /// <summary>
    /// 
    /// </summary>
    public class V_FO_LogisticsOrderDetail
    {
        /// <summary>
        /// 物流单号
        /// </summary>
        public string LogisticsNo { get; set; }
        /// <summary>
        /// 销售订单号
        /// </summary>
        public string SaleNo { get; set; }
        /// <summary>
        /// 销售订单行号
        /// </summary>
        public int? SaleLine { get; set; }
        /// <summary>
        /// 行号
        /// </summary>
        public int? Line { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string CostCenter { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        public string ItemName { get; set; }
        /// <summary>
        /// 物料组编码
        /// </summary>
        public string ItmsGrpCode { get; set; }
        /// <summary>
        /// 物料组名称
        /// </summary>
        public string ItmsGrpName { get; set; }
        /// <summary>
        /// 数量
        /// </summary>
        public decimal? Qty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 发货日期
        /// </summary>
        public DateTime? DeliverDate { get; set; }
        /// <summary>
        /// 重量
        /// </summary>
        public decimal? Weight { get; set; }
        /// <summary>
        /// 重量单位
        /// </summary>
        public decimal? WeightUnit { get; set; }
        /// <summary>
        /// 行理论金额
        /// </summary>
        public decimal? RowTheoreticalAmount { get; set; }
    }
}
