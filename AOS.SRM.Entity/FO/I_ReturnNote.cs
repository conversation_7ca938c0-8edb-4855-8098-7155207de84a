using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 返还单
    /// </summary>
    [SugarTable("I_ReturnNote")]
    public class I_ReturnNote : BaseEntity
    {
        /// <summary>
        /// 开票单主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("开票单主键ID")]
        public string Id { get; set; }
        /// <summary>
        /// 对账单号
        /// </summary>
        [Description("对账单号")]
        public string ReconciliationNo { get; set; }
        /// <summary>
        /// 返还单号
        /// </summary>
        [Description("返还单号")]
        public string ReturnNo { get; set; }
        /// <summary>
        /// 返还日期
        /// </summary>
        [Description("返还日期")]
        public DateTime? ReturnDate { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }
        /// <summary>
        /// 制作人
        /// </summary>
        [Description("制作人")]
        public string Producer { get; set; }
        /// <summary>
        /// 提交人
        /// </summary>
        [Description("提交人")]
        public string Submitter { get; set; }
        /// <summary>
        /// 西子对账金额
        /// </summary>
        [Description("西子对账金额")]
        public decimal? ReconciliationMoney { get; set; }
        /// <summary>
        /// 供应商对账金额
        /// </summary>
        [Description("供应商对账金额")]
        public decimal? SupplyReconciliationMoney { get; set; }
        /// <summary>
        /// 西子账面余额
        /// </summary>
        [Description("西子账面余额")]
        public decimal? BalanceMoney { get; set; }
        /// <summary>
        /// 供应商账面余额
        /// </summary>
        [Description("供应商账面余额")]
        public decimal? SuppBalanceMoney { get; set; }
        /// <summary>
        /// 确认人
        /// </summary>
        [Description("确认人")]
        public string Confirmer { get; set; }
        /// <summary>
        /// 0：已提交 1:已下发 2:已接收 3：已上传 4:已完成
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }
        /// <summary>
        /// 附件路径
        /// </summary>
        [Description("附件路径")]
        public string Path { get; set; }
        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }
        /// <summary>
        /// 传真
        /// </summary>
        public string Fax { get; set; }
        /// <summary>
        /// 信箱
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 附件管理id
        /// </summary>
        public string ParentId { get; set; }
        /// <summary>
        /// 附件名称
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// 签章返还单地址
        /// </summary>
        public string SignPath { get; set; }
        /// <summary>
        /// 签章返还单附件Id
        /// </summary>
        public string SignAttachId { get; set; }
        /// <summary>
        /// 签章返还单文件名
        /// </summary>
        public string SignFileName { get; set; }
    }
}
