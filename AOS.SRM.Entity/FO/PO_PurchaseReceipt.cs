using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.FO
{
    /// <summary>
    /// 采购收货扫描
    /// </summary>
    [SugarTable("PO_PurchaseReceipt")]
    public class PO_PurchaseReceipt : BaseEntity
    {
        /// <summary> 
        /// 采购收货主键ID
        /// </summary> 
        [SugarColumn(IsPrimaryKey = true)]
        [Description("采购收货主键ID")]
        public string PurchaseReceiptID { get; set; }
        /// <summary> 
        /// WMS单号
        /// </summary> 
        [Description("WMS单号")]
        public string DocNum { get; set; }
        /// <summary> 
        /// WMS行号
        /// </summary> 
        [Description("WMS行号")]
        public int? Line { get; set; }
        /// <summary> 
        /// 公司代码
        /// </summary> 
        [Description("公司代码")]
        public string CompanyCode { get; set; }
        /// <summary> 
        /// 工厂代码
        /// </summary> 
        [Description("工厂代码")]
        public string FactoryCode { get; set; }
        /// <summary> 
        /// 移动类型
        /// </summary> 
        [Description("移动类型")]
        public string MovementType { get; set; }
        /// <summary> 
        /// 特殊库存
        /// </summary> 
        [Description("特殊库存")]
        public string SpecialInventory { get; set; }
        /// <summary> 
        /// 销售订单号
        /// </summary> 
        [Description("销售订单号")]
        public string SalesOrderNum { get; set; }
        /// <summary> 
        /// 销售订单行号
        /// </summary> 
        [Description("销售订单行号")]
        public int? SalesOrderLine { get; set; }
        /// <summary> 
        /// 评估类型
        /// </summary> 
        [Description("评估类型")]
        public string EvaluationType { get; set; }
        /// <summary> 
        /// 采购退货申请单编号
        /// </summary> 
        [Description("采购收货申请单编号")]
        public string BaseEntry { get; set; }
        /// <summary> 
        /// 采购单号
        /// </summary> 
        [Description("采购单号")]
        public string BaseNum { get; set; }
        /// <summary> 
        /// 采购订单类型
        /// </summary> 
        [Description("采购订单类型")]
        public string BaseType { get; set; }
        /// <summary> 
        /// 采购行号
        /// </summary> 
        [Description("采购行号")]
        public int? BaseLine { get; set; }
        /// <summary> 
        /// 条码
        /// </summary> 
        [Description("条码")]
        public string BarCode { get; set; }
        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }
        /// <summary> 
        /// 供应商编号
        /// </summary> 
        [Description("供应商编号")]
        public string SupplierCode { get; set; }
        /// <summary> 
        /// 供应商名称
        /// </summary> 
        [Description("供应商名称")]
        public string SupplierName { get; set; }
        /// <summary> 
        /// 供应商批次
        /// </summary> 
        [Description("供应商批次")]
        public string SupplierBatch { get; set; }
        /// <summary>
        /// 生产日期
        /// </summary>
        [Description("生产日期")]
        public DateTime? PTime { get; set; }
        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }
        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary> 
        /// 物料组编号
        /// </summary> 
        [Description("物料组编号")]
        public string ItmsGrpCode { get; set; }
        /// <summary> 
        /// 物料组名称
        /// </summary> 
        [Description("物料组名称")]
        public string ItmsGrpName { get; set; }
        /// <summary> 
        /// 采购收货数量
        /// </summary> 
        [Description("采购收货数量")]
        public decimal PurchaseReceiptQty { get; set; }
        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit { get; set; }
        /// <summary> 
        /// 仓库编号
        /// </summary> 
        [Description("仓库编号")]
        public string WhsCode { get; set; }
        /// <summary> 
        /// 仓库名称
        /// </summary> 
        [Description("仓库名称")]
        public string WhsName { get; set; }
        /// <summary> 
        /// 区域编号
        /// </summary> 
        [Description("区域编号")]
        public string RegionCode { get; set; }
        /// <summary> 
        /// 区域名称
        /// </summary> 
        [Description("区域名称")]
        public string RegionName { get; set; }
        /// <summary> 
        /// 库位编号
        /// </summary> 
        [Description("库位编号")]
        public string BinLocationCode { get; set; }
        /// <summary> 
        /// 库位名称
        /// </summary> 
        [Description("库位名称")]
        public string BinLocationName { get; set; }
        /// <summary> 
        /// 状态
        /// </summary> 
        [Description("状态")]
        public int? Status { get; set; }
        /// <summary> 
        /// 是否已过账
        /// </summary> 
        [Description("是否已过账")]
        public bool? IsPosted { get; set; }
        /// <summary> 
        /// 过账人
        /// </summary> 
        [Description("过账人")]
        public string PostUser { get; set; }
        /// <summary> 
        /// 过账时间
        /// </summary> 
        [Description("过账时间")]
        public DateTime? PostTime { get; set; }
        /// <summary>
        /// sap生成单号
        /// </summary>
        [Description("sap生成单号")]
        public string SapDocNum { get; set; }
        /// <summary>
        /// SAP生成行号
        /// </summary>
        [Description("SAP生成行号")]
        public int? SapLine { get; set; }
        /// <summary>
        /// 项目类别
        /// </summary>
        public string  ProjectType { get; set; }
        /// <summary>
        /// 采购组织
        /// </summary>
        public string Organization { get; set; }
        /// <summary>
        /// 是否已开票
        /// </summary>
        public bool? ApplySRM { get; set; }
    }
}
