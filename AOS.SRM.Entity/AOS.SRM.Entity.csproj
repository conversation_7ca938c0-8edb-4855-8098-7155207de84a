<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A56C0618-C820-42E7-89B5-E5DA01C72729}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AOS.SRM.Entity</RootNamespace>
    <AssemblyName>AOS.SRM.Entity</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\AOS.SRM.Entity.xml</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Quartz">
      <HintPath>..\packages\Quartz.3.0.7\lib\net452\Quartz.dll</HintPath>
    </Reference>
    <Reference Include="SqlSugar, Version=5.1.4.185, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SqlSugar.5.1.4.185\lib\SqlSugar.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Base\BaseEntity.cs" />
    <Compile Include="Base\BinLocationEntity.cs" />
    <Compile Include="Base\EntityComparer.cs" />
    <Compile Include="Enum\BusinessTypeEnum.cs" />
    <Compile Include="IX\IX_Notice.cs" />
    <Compile Include="FO\Dto\InvoiceDto.cs" />
    <Compile Include="FO\Dto\LogisticsInvoiceDto.cs" />
    <Compile Include="FO\Dto\LogisticsReconciliationDto.cs" />
    <Compile Include="FO\Dto\Returnno_view.cs" />
    <Compile Include="FO\I_Invoice.cs" />
    <Compile Include="FO\I_InvoiceDetails.cs" />
    <Compile Include="FO\I_InvoiceDisposal.cs" />
    <Compile Include="FO\I_Reconciliation.cs" />
    <Compile Include="FO\I_ReconciliationDetail.cs" />
    <Compile Include="FO\I_ReturnNote.cs" />
    <Compile Include="FO\MD_LabelTemplete.cs" />
    <Compile Include="FO\MM_Warehousing.cs" />
    <Compile Include="FO\PO_PurchaseReceipt.cs" />
    <Compile Include="FO\PO_ReturnScanDetailed.cs" />
    <Compile Include="FO\ViewModel\FO_ReceivingDetails_View.cs" />
    <Compile Include="FO\ViewModel\I_Invoice_Views.cs" />
    <Compile Include="FO\ViewModel\V_FO_InvoiceDetailExport.cs" />
    <Compile Include="FO\ViewModel\V_FO_LogisticsOrderDetail.cs" />
    <Compile Include="FO\ViewModel\V_FO_LogisticsOrderInfo.cs" />
    <Compile Include="IX\IX_NoticeReceipt.cs" />
    <Compile Include="Plm\BlueprintAutoSendCondition.cs" />
    <Compile Include="Plm\Plm_Blueprint.cs" />
    <Compile Include="Plm\P_Blueprint_Non_StandardExportModel.cs" />
    <Compile Include="Plm\Req\BlueprintReq.cs" />
    <Compile Include="Plm\Req\ConfirmBlueprintReq.cs" />
    <Compile Include="Plm\Req\ExportBlueprintRequest.cs" />
    <Compile Include="Plm\Req\P_Blueprint_Non_StandardPageQueryRequest.cs" />
    <Compile Include="Plm\Req\P_Blueprint_Non_StandardQueryRequest.cs" />
    <Compile Include="Plm\Req\RefreshMaterialTypeRequest.cs" />
    <Compile Include="Plm\Req\RevokeUnreleasedRequest.cs" />
    <Compile Include="Plm\Req\SyncReleaseStatusRequest.cs" />
    <Compile Include="Plm\Req\TechnicalChangeNotificationRequest.cs" />
    <Compile Include="Plm\Res\BlueprintRes.cs" />
    <Compile Include="PXC\Dto\DeliveryBatchDto.cs" />
    <Compile Include="PXC\Dto\P_InspectionDetail_Views.cs" />
    <Compile Include="PXC\P_Blueprint_Accept.cs" />
    <Compile Include="PXC\P_Blueprint_Non_Standard.cs" />
    <Compile Include="PXC\P_Blueprint_Send_Record.cs" />
    <Compile Include="PXC\P_Blueprint_Send_User.cs" />
    <Compile Include="PXC\P_LogisticsDispatchCar.cs" />
    <Compile Include="PXC\Req\BatchDownloadRequest.cs" />
    <Compile Include="PXC\Req\BatchSendRequest.cs" />
    <Compile Include="PXC\Req\BatchSendToAcceptsRequest.cs" />
    <Compile Include="PXC\Req\BlueprintAcceptQueryRequest.cs" />
    <Compile Include="PXC\Req\BlueprintQueryRequest.cs" />
    <Compile Include="PXC\Req\ConfirmAcceptRequest.cs" />
    <Compile Include="PXC\Req\GetBlueprintSendUsersRequest.cs" />
    <Compile Include="PXC\Req\GetByBlueprintIdRequest.cs" />
    <Compile Include="PXC\Req\GetByIdRequest.cs" />
    <Compile Include="PXC\Req\GetByAcceptIdRequest.cs" />
    <Compile Include="PXC\Req\GetByUserIdRequest.cs" />
    <Compile Include="PXC\Req\ManualSendBlueprintRequest.cs" />
    <Compile Include="PXC\Req\BlueprintSendUserRequest.cs" />
    <Compile Include="PXC\Req\P_Blueprint_Send_RecordQueryRequest.cs" />
    <Compile Include="PXC\Req\P_Blueprint_Send_RecordExportRequest.cs" />
    <Compile Include="PXC\SD_ConsignmentNote.cs" />
    <Compile Include="PXC\SD_ConsignmentNoteDetail.cs" />
    <Compile Include="PXC\ViewModel\BlueprintSendUserInfo.cs" />
    <Compile Include="PXC\ViewModel\P_Blueprint_Send_Record_View.cs" />
    <Compile Include="PXC\ViewModel\LogisticsIOrder\PXC_ConsignmentNoteDetail_View.cs" />
    <Compile Include="PXC\ViewModel\LogisticsIOrder\PXC_ConsignmentNote_Export.cs" />
    <Compile Include="PXC\ViewModel\PurchaseOrder\PXC_PurchaseApplyQty_View.cs" />
    <Compile Include="RPT\ViewModel\V_RPT_InvoiceDetails.cs" />
    <Compile Include="RPT\ViewModel\V_RPT_ReceiptAndInvoiceState.cs" />
    <Compile Include="SAP\XZ_SAP_MARC.cs" />
    <Compile Include="SAP\XZ_SAP_T007S.cs" />
    <Compile Include="SAP\XZ_SAP_TCURT.cs" />
    <Compile Include="SAP\ZFGSRM002Return.cs" />
    <Compile Include="SAP\HEADZFGSRM002.cs" />
    <Compile Include="SAP\HEADZFGSRM003.cs" />
    <Compile Include="FO\P_ConsignmentNote.cs" />
    <Compile Include="SAP\XZ_SAP_T005T.cs" />
    <Compile Include="SAP\XZ_SAP_T052U.cs" />
    <Compile Include="SAP\XZ_SAP_T077Y.cs" />
    <Compile Include="SAP\ZFGSRM001.cs" />
    <Compile Include="SAP\ZFGSRM002.cs" />
    <Compile Include="PXC\P_Inspection.cs" />
    <Compile Include="PXC\P_PurchaseServerOrderStatus.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SAP\ZFGSRM003.cs" />
    <Compile Include="SAP\ZFGSRM003Return.cs" />
    <Compile Include="SAP\ZFGSRM004.cs" />
    <Compile Include="SAP\ZFGSRM004_Item1.cs" />
    <Compile Include="SAP\ZFGSRM004_Item.cs" />
    <Compile Include="SAP\ZFGSRM005.cs" />
    <Compile Include="SAP\ZFGSRM005Return.cs" />
    <Compile Include="SAP\ZFGSRM006.cs" />
    <Compile Include="SAP\ZFGSRM006Return.cs" />
    <Compile Include="SAP\ZFGSRM007.cs" />
    <Compile Include="SAP\ZFGWMS019.cs" />
    <Compile Include="SAP\ZFGWMS019Return.cs" />
    <Compile Include="SHM\DisposalManagement\S_UnqualifiedDisposal.cs" />
    <Compile Include="SHM\S_DisqualifySupply.cs" />
    <Compile Include="SHM\S_DrawingDistributionRecycling.cs" />
    <Compile Include="SHM\S_EquipPurchase.cs" />
    <Compile Include="SHM\S_PerformanceAppraisal.cs" />
    <Compile Include="SHM\S_PerformanceAppraisalBase.cs" />
    <Compile Include="SHM\S_ResumeSupplyQualification.cs" />
    <Compile Include="SHM\S_TechnicalNotice.cs" />
    <Compile Include="SHM\S_StandardsOfBank.cs" />
    <Compile Include="SHM\S_SupplierAudit.cs" />
    <Compile Include="SHM\S_SupplierChange.cs" />
    <Compile Include="SHM\S_SupplierInfo.cs" />
    <Compile Include="PXC\ViewModel\LogisticsIOrder\PXC_ConsignmentNote_View.cs" />
    <Compile Include="PXC\ViewModel\PurchaseOrder\PXC_MakeInspection_View.cs" />
    <Compile Include="PXC\ViewModel\PurchaseOrder\PXC_PurchaseDeliveryBatch_View.cs" />
    <Compile Include="PXC\ViewModel\PurchaseOrder\PXC_PurchaseOrder_View.cs" />
    <Compile Include="PXC\P_DeliveryBatch.cs" />
    <Compile Include="PXC\P_InspectionDetail.cs" />
    <Compile Include="PXC\P_PurchaseOrderStatus.cs" />
    <Compile Include="RPT\PRT_PurchaseDeliveryOnTimeRate.cs" />
    <Compile Include="RPT\ViewModel\RPT_FTT_View.cs" />
    <Compile Include="RPT\ViewModel\RPT_PLineOutPut.cs" />
    <Compile Include="RPT\ViewModel\RPT_PO_InOut_View.cs" />
    <Compile Include="RPT\RPT_PurchaseDeliverySchedule.cs" />
    <Compile Include="RPT\ViewModel\RPT_PO_Inspection_View.cs" />
    <Compile Include="RPT\ViewModel\RPT_SD_Delivery_View.cs" />
    <Compile Include="RPT\ViewModel\RPT_StockMove_View.cs" />
    <Compile Include="RPT\ViewModel\RPT_SupplierItem_View.cs" />
    <Compile Include="RPT\ViewModel\V_NotPostStockMove.cs" />
    <Compile Include="SAP\SAPRETURN.cs" />
    <Compile Include="SAP\ZFGSRM003_Item.cs" />
    <Compile Include="SHM\DisposalManagement\S_CompromiseDisposal.cs" />
    <Compile Include="SHM\DisposalManagement\S_CompromiseDisposalDetails.cs" />
    <Compile Include="SHM\DisposalManagement\S_RRDF.cs" />
    <Compile Include="SHM\DisposalManagement\S_DebitNotice.cs" />
    <Compile Include="SHM\S_SupplierWarningLetter.cs" />
    <Compile Include="SHM\ViewModel\ConcessionAcceptDto.cs" />
    <Compile Include="SHM\ViewModel\CreateSupplier.cs" />
    <Compile Include="SHM\ViewModel\I_CompromiseDisposal_Views.cs" />
    <Compile Include="SHM\ViewModel\SHM_CompromiseDisposal_View.cs" />
    <Compile Include="SHM\ViewModel\XZ_SAP_MARC.cs" />
    <Compile Include="Store\Store_Supplier.cs" />
    <Compile Include="Store\Store_SupplierDetail.cs" />
    <Compile Include="Sys\MD_AttachmentManagement.cs" />
    <Compile Include="Sys\Sys_ApiLogConfig.cs" />
    <Compile Include="Sys\Sys_AppVersion.cs" />
    <Compile Include="Sys\Sys_DbBackup.cs" />
    <Compile Include="Sys\Sys_DbBackupConfig.cs" />
    <Compile Include="Sys\Sys_DictionaryBase.cs" />
    <Compile Include="Sys\Sys_Log.cs" />
    <Compile Include="Sys\Sys_Mail.cs" />
    <Compile Include="Sys\Sys_MailServerConfig.cs" />
    <Compile Include="Sys\Sys_Message.cs" />
    <Compile Include="Sys\Sys_MessageNotifySetting.cs" />
    <Compile Include="Sys\Sys_MessageType.cs" />
    <Compile Include="Sys\Sys_Organization.cs" />
    <Compile Include="Sys\Sys_Dictionary.cs" />
    <Compile Include="Sys\Sys_Resource.cs" />
    <Compile Include="Sys\Sys_Role.cs" />
    <Compile Include="Sys\Sys_RoleResource.cs" />
    <Compile Include="Sys\Sys_SAPCompanyInfo.cs" />
    <Compile Include="Sys\Sys_SwithConfig.cs" />
    <Compile Include="Sys\Sys_User.cs" />
    <Compile Include="Sys\Sys_UserMessage.cs" />
    <Compile Include="Sys\Sys_UserRole.cs" />
    <Compile Include="Sys\Sys_UserSapAccount.cs" />
    <Compile Include="Sys\ViewModel\BatchSettingRequestParmsBase.cs" />
    <Compile Include="Sys\ViewModel\RequestBatchProcess.cs" />
    <Compile Include="Sys\ViewModel\Sys_MessageNotifySettingInput.cs" />
    <Compile Include="SHM\S_Contract.cs" />
    <Compile Include="UniqueCodeAttribute.cs" />
    <Compile Include="VW\V_EmailUserInfo.cs" />
    <Compile Include="WMS\MM_Warehousing.cs" />
    <Compile Include="WMS\PO_PurchaseReceipt.cs" />
    <Compile Include="WMS\PO_ReturnScanDetailed.cs" />
    <Compile Include="WMS\Province_City_District.cs" />
    <Compile Include="WMS\req\ExclusionVerificationReceivingReq.cs" />
    <Compile Include="Plm\PlmOracleFile.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Basic\ViewModel\" />
    <Folder Include="PXC\ViewModel\OutsourcingProcurement\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AOS.Core\AOS.Core.csproj">
      <Project>{62d9f685-5537-494e-8d51-4daf877f8af1}</Project>
      <Name>AOS.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>