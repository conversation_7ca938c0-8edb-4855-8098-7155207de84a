using AOS.SRM.Entity.PLM;
using AOS.SRM.Entity.Sys;
using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 图纸发放用户关联
    /// </summary>
    public class P_Blueprint_Send_User : BaseEntity
    {
        /// <summary>
        /// Id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 图纸ID
        /// </summary>
        public string BlueprintId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 图纸发送组
        /// </summary>
        public int? BlueprintSendGroup { get; set; }
        
        /// <summary>
        /// 用户信息（一对一导航属性）
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(UserId), nameof(Sys_User.UserID))]
        public Sys_User UserInfo { get; set; }
        
        /// <summary>
        /// 图纸信息（一对一导航属性）
        /// </summary>
        [Navigate(NavigateType.OneToOne, nameof(BlueprintId), nameof(Plm_Blueprint.Id))]
        public Plm_Blueprint BlueprintInfo { get; set; }
    }
} 