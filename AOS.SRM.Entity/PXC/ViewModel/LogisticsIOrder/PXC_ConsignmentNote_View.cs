using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 物流订单查询（查询数据源是wms数据库）
    /// </summary>
    public class PXC_ConsignmentNote_View
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Description("主键id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ConsignmentNoteID { set; get; }

        /// <summary>
        /// wms单号
        /// </summary>
        public string DocNum { set; get; }

        /// <summary>
        /// 
        /// </summary>
        public string ShippingPlanNum { set; get; }

        /// <summary>
        /// 发货日期
        /// </summary>
        public DateTime? DeliveryDate { set; get; }

        /// <summary>
        /// 客户名称
        /// </summary>
        public string CustomerName { set; get; }

        /// <summary>
        /// 客户地址
        /// </summary>
        public string CustomerAdd { set; get; }

        /// <summary>
        /// 客户结算地址
        /// </summary>
        public string CustomerRegion { set; get; }

        /// <summary>
        /// 物流供应商编号
        /// </summary>
        public string LogisticsSupplierCode { set; get; }

        /// <summary>
        /// 物流供应商名称
        /// </summary>
        public string LogisticsSupplierName { set; get; }

        /// <summary>
        /// 托运部门
        /// </summary>
        public string ShippingDepar { set; get; }

        /// <summary>
        /// 运输方式
        /// </summary>
        public string ShippingType { set; get; }

        /// <summary>
        /// 托运人
        /// </summary>
        public string Shipper { set; get; }

        /// <summary>
        /// 车牌号
        /// </summary>
        public string CarNum { set; get; }

        /// <summary>
        /// 其他费用
        /// </summary>
        public decimal? SpecialExpenses { set; get; }

        /// <summary>
        /// 总理论费用
        /// </summary>
        public decimal? TotalTheoreticalAmount { set; get; }

        /// <summary>
        /// 总实际费用
        /// </summary>
        public decimal? TotalActualAmount { set; get; }

        /// <summary>
        /// 总重量
        /// </summary>
        public decimal? TotalWeight { set; get; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime? CTime { set; get; }

        /// <summary>
        /// 
        /// </summary>
        public string Status { set; get; }

        /// <summary>
        /// 总金额
        /// </summary>
        public decimal? SumAmount { get; set; }

        /// <summary>
        /// Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 成本中心
        /// </summary>
        public string CostCenter { get; set; }

        /// <summary>
        /// 附件路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 附件名称
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 接收人
        /// </summary>
        public string CUser { get; set; }
    }
}