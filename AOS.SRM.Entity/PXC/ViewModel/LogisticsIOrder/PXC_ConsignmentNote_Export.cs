using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 物流明细导出
    /// </summary>
    public class PXC_ConsignmentNote_Export
    {
        /// <summary>
        /// 物流单号
        /// </summary>
        public string LogisticsNo { get; set; }
        /// <summary>
        /// 物流供应商
        /// </summary>
        public string LogisticsSupplierCode { get; set; }
        /// <summary>
        /// 客户订单号
        /// </summary>
        [Description("订单号")]
        public string CustomerOrderNum { get; set; }
        /// <summary>
        /// 合同号
        /// </summary>
        [Description("合同号")]
        public string ContractNo { get; set; }
        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("发货单位")]
        public string CustomerName { get; set; }
        /// <summary> 
        /// 发货日期
        /// </summary> 
        [Description("发货日期")]
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 序列号
        /// </summary>
        [Description("出场编号")]
        public string BarCode { get; set; }
        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("部件号图号")]
        public string ItemName { get; set; }
        /// <summary> 
        /// 托运单数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }
        /// <summary> 
        /// 结算地址
        /// </summary> 
        [Description("地址")]
        public string CustomerRegion { get; set; }
        /// <summary> 
        /// 联系人
        /// </summary> 
        [Description("联系人")]
        public string Contact { get; set; }
        /// <summary> 
        /// 联系方式
        /// </summary> 
        [Description("联系电话")]
        public string Telephone { get; set; }
        /// <summary> 
        /// 客户地址
        /// </summary> 
        [Description("发货地址")]
        public string CustomerAdd { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }
    }
}
