using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 采购订单查询
    /// </summary>
    public class PXC_PurchaseOrder_View
    {
        /// <summary>
        /// 暂估价标识
        /// </summary>
        [Description("暂估标识")]
        public string ZZZGJ { get; set; }

        /// <summary>
        /// 采购订单类型
        /// </summary>
        [Description("采购订单类型")]
        public string BSART { get; set; }

        /// <summary>
        /// 采购订单类型描述
        /// </summary>
        [Description("采购订单类型描述")]
        public string BATXT { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string BUKRS { get; set; }

        /// <summary>
        /// 项目类别
        /// </summary>
        [Description("项目类别")]
        public string PSTYP { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string WERKS { get; set; }

        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string EBELN { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public decimal? EBELP { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string LIFNR { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string NAME1 { get; set; }

        /// <summary>
        /// 旧物料编码
        /// </summary>
        [Description("旧物料编码")]
        public string BISMT { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MATNR { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string TXZ01 { get; set; }

        /// <summary>
        /// 采购数量
        /// </summary>
        [Description("采购数量")]
        public decimal? MENGE { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 库存地点代码
        /// </summary>
        [Description("库存地点代码")]
        public string LGORT { get; set; }

        /// <summary>
        /// 库存地点描述
        /// </summary>
        [Description("库存地点描述")]
        public string LGOBE { get; set; }

        /// <summary>
        /// 交期
        /// </summary>
        [Description("交货日期")]
        public DateTime? EINDT { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [Description("创建日期")]
        public DateTime? AEDAT { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }

        /// <summary>
        /// 主键id（用于修改状态）
        /// </summary>
        [Description("主键id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDelete { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        public string SaleNo { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        public decimal? SaleLineNo { get; set; }

        /// <summary>
        /// 采购组织
        /// </summary>
        public string PurchaseORG { get; set; }

        /// <summary>
        /// 采购组
        /// </summary>
        public string PurchaseGroup { get; set; }

        /// <summary>
        /// 生产批次
        /// </summary>
        public string BatchNum { get; set; }

        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string BrushingWords { get; set; }

        ///// <summary>
        ///// 生产订单号
        ///// </summary>
        //public string AUFNR { get; set; }
        /// <summary>
        /// 报检单数量
        /// </summary>
        [Description("报检单数量")]
        public decimal? InspectionQty { get; set; }

        /// <summary>
        /// 已入库数量
        /// </summary>
        [Description("已入库数量")]
        public decimal? StorageQty { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string ZTEXT { get; set; }

        /// <summary>
        /// 交货标识  X:已完成
        /// </summary>
        [Description("交货标识")]
        public string ELIKZ { get; set; }

        /// <summary>
        /// 接收
        /// </summary>
        [Description("接收人")]
        public string CUser { get; set; }

        /// <summary>
        /// 接收时间
        /// </summary>
        [Description("接收时间")]
        public DateTime? CTime { get; set; }

        /// <summary>
        /// 开票方
        /// </summary>
        public string LIFRE { get; set; }

        /// <summary>
        /// 配送方
        /// </summary>
        public string LLIEF { get; set; }

        /// <summary>
        /// 接收状态  0无需处理  1待下载  2待确认  3已确认
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public int? AcceptStatus { get; set; }

        /// <summary>
        /// 接收状态  0无需处理  1待下载  2待确认  3已确认
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string AcceptId { get; set; }

        /// <summary>
        /// 下载地址
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string DownUrl { get; set; }

        /// <summary>
        /// 文件名
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string FileName { get; set; }
    }
}