using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 报检单
    /// </summary>
    public class PXC_MakeInspection_View
    {
        /// <summary>
        /// 暂估价标识
        /// </summary>
        [Description("暂估标识")]
        public string ZZZGJ { get; set; }

        /// <summary>
        /// 主键id
        /// </summary>
        [Description("主键id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [Description("是否删除")]
        public bool IsDelete { get; set; }

        /// <summary>
        /// 报检单号
        /// </summary>
        [Description("报检单号")]
        public string InspectionNo { get; set; }

        /// <summary>
        /// 报检单行号
        /// </summary>
        [Description("报检单行号")]
        public int? InspectionLine { get; set; }

        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 采购订单行号
        /// </summary>
        [Description("采购订单行号")]
        public decimal? OrderLine { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplyerCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyerName1 { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        [Description("批次号")]
        public string BatchNum { get; set; }

        /// <summary>
        /// 仓库地点
        /// </summary>
        [Description("仓库地点")]
        public string WhsAddress { get; set; }

        /// <summary>
        /// 仓库编码
        /// </summary>
        [Description("仓库编码")]
        public string WhsCode { get; set; }

        /// <summary>
        /// 报检数量
        /// </summary>
        [Description("报检数量")]
        public decimal? InspectionQty { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 采购订单交期
        /// </summary>
        [Description("采购订单交期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 送货日期
        /// </summary>
        [Description("送货日期")]
        public DateTime? DeliveryToDate { get; set; }

        /// <summary>
        /// 1:已发货 2:检验完成 3:完成入库
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }

        /// <summary>
        /// 检验合格数量
        /// </summary>
        [Description("检验合格数量")]
        public decimal? QualifiedQty { get; set; }

        /// <summary>
        /// 检验不合格数量
        /// </summary>
        [Description("检验不合格数量")]
        public decimal? UnQualifiedQty { get; set; }

        /// <summary>
        /// 采购订单类型
        /// </summary>
        [Description("采购订单类型")]
        public string BSART { get; set; }

        /// <summary>
        /// 旧物料编码
        /// </summary>
        [Description("旧物料编码")]
        public string BISMT { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MATNR { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string TXZ01 { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        public string CUser { get; set; }

        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string BrushingWords { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string ZTEXT { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        [Description("入库数量")]
        public decimal? StorageQty { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string WERKS { get; set; }

        /// <summary>
        /// 开票方
        /// </summary>
        public string LIFRE { get; set; }

        /// <summary>
        /// 配送方
        /// </summary>
        public string LLIEF { get; set; }
    }
}