using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 送货计划视图
    /// </summary>
    public class PXC_PurchaseDeliveryBatch_View
    {
        /// <summary>
        /// 暂估价标识
        /// </summary>
        [Description("暂估标识")]
        public string ZZZGJ { get; set; }
        /// <summary>
        /// 送货日期
        /// </summary>
        [Description("送货日期")]
        public DateTime? DeliveryToDate { get; set; }
        /// <summary>
        /// 采购订单类型
        /// </summary>
        [Description("采购订单类型")]
        public string BSART { get; set; }
        /// <summary>
        /// 采购订单类型描述
        /// </summary>
        [Description("采购订单类型描述")]
        public string BATXT { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string BUKRS { get; set; }
        /// <summary>
        /// 创建日期
        /// </summary>
        [Description("创建日期")]
        public DateTime? AEDAT { get; set; }
        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string OrderNo { get; set; }
        /// <summary>
        /// 采购订单行号
        /// </summary>
        [Description("采购订单行号")]
        public decimal? EBELP { get; set; }
        /// <summary>
        /// 供应商代码
        /// </summary>
        [Description("供应商代码")]
        public string LIFNR { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string NAME1 { get; set; }
        /// <summary>
        /// 旧物料编码
        /// </summary>
        [Description("旧物料编码")]
        public string BISMT { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string MaterialName { get; set; }
        /// <summary>
        /// 仓库代码
        /// </summary>
        [Description("仓库代码")]
        public string WhsCode { get; set; }
        /// <summary>
        /// 仓库描述
        /// </summary>
        [Description("仓库描述")]
        public string WhsAddress { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        [Description("批次号")]
        public string BatchNum { get; set; }
        /// <summary>
        /// 批次送货数量
        /// </summary>
        [Description("批次送货数量")]
        public decimal? BatchQty { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Description("单位")]
        public string Unit { get; set; }
        /// <summary>
        /// 批次送货数量总和
        /// </summary>
        [Description("批次送货数量总和")]
        public decimal? CumulativeBatchQuantity { get; set; }
        /// <summary>
        /// 交货日期
        /// </summary>
        [Description("交货日期")]
        public DateTime? EINDT { get; set; }
        /// <summary>
        /// 采购数量
        /// </summary>
        [Description("采购数量")]
        public decimal? MENGE { get; set; }
        /// <summary>
        /// 是否删除
        /// </summary>
        [Description("是否删除")]
        public bool IsDelete { get; set; }
        /// <summary>
        /// 主键id（点击编辑获取修改字段）
        /// </summary>
        [Description("主键id")]
        public string Id { get; set; }
        /// <summary>
        /// 项目类别
        /// </summary>
        [Description("项目类别")]
        public string PSTYP { get; set; }
        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string WERKS { get; set; }
        /// <summary>
        /// 采购组织
        /// </summary>
        [Description("采购组织")]
        public string PurchaseORG { get; set; }
        /// <summary>
        /// 采购组
        /// </summary>
        [Description("采购组")]
        public string PurchaseGroup { get; set; }
        /// <summary>
        /// 销售订单号
        /// </summary>
        [Description("销售订单号")]
        public string SaleNo { get; set; }
        /// <summary>
        /// 销售订单行号
        /// </summary>
        [Description("销售订单行号")]
        public decimal? SaleLineNo { get; set; }
        /// <summary>
        /// 已交数量
        /// </summary>
        public decimal? DeliveredQuantity { get; set; }
        /// <summary>
        /// 未交数量
        /// </summary>
        public decimal? OutstandingQuantity { get; set; }
        ///// <summary>
        ///// 报检次数
        ///// </summary>
        //public int? CheckCount { get; set; }
        /// <summary>
        /// 报检单数量
        /// </summary>
        public decimal? HasInspectionQty { get; set; }
        /// <summary>
        /// 未报检数量
        /// </summary>
        public decimal? NoInspectionQty { get; set; }
        /// <summary>
        /// 刷字
        /// </summary>
        [Description("刷字")]
        public string BrushingWords { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string ZTEXT { get; set; }
        /// <summary>
        /// 交货标识  X:已完成
        /// </summary>
        [Description("交货标识")]
        public string ELIKZ { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        [Description("创建人")]
        public string CUser { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }
        /// <summary>
        /// 开票方
        /// </summary>
        public string LIFRE { get; set; }
        /// <summary>
        /// 配送方
        /// </summary>
        public string LLIEF { get; set; }
    }
}
