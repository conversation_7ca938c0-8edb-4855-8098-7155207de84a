using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PXC.ViewModel.PurchaseOrder
{
    /// <summary>
    /// 
    /// </summary>
    public class PXC_PurchaseApplyQty_View
    {
        /// <summary>
        /// 采购订单类型
        /// </summary>
        [Description("采购订单类型")]
        public string BSART { get; set; }

        /// <summary>
        /// 采购订单类型描述
        /// </summary>
        [Description("采购订单类型描述")]
        public string BATXT { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string BUKRS { get; set; }

        /// <summary>
        /// 项目类别
        /// </summary>
        [Description("项目类别")]
        public string PSTYP { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string WERKS { get; set; }

        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string EBELN { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string LIFNR { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string NAME1 { get; set; }

        /// <summary>
        /// 创建日期
        /// </summary>
        [Description("创建日期")]
        public DateTime? AEDAT { get; set; }

        /// <summary>
        /// 旧物料编码
        /// </summary>
        [Description("旧物料编码")]
        public string BISMT { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MATNR { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string TXZ01 { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public decimal? MENGE { get; set; }

        /// <summary>
        /// 库存地点代码
        /// </summary>
        [Description("库存地点代码")]
        public string LGORT { get; set; }

        /// <summary>
        /// 库存地点描述
        /// </summary>
        [Description("库存地点描述")]
        public string LGOBE { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public decimal? EBELP { get; set; }

        /// <summary>
        /// 交期
        /// </summary>
        [Description("交期")]
        public DateTime? EINDT { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }

        /// <summary>
        /// 主键id（用于修改状态）
        /// </summary>
        [Description("主键id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDelete { get; set; }

        /// <summary>
        /// 已交数量
        /// </summary>
        [Description("已交数量")]
        public decimal? DeliveredQuantity { get; set; }

        /// <summary>
        /// 未交数量
        /// </summary>
        [Description("未交数量")]
        public decimal? OutstandingQuantity { get; set; }

        /// <summary>
        /// 采购订单批次总数量
        /// </summary>
        [Description("采购订单批次总数量")]
        public decimal? CumulativeBatchQuantity { get; set; }

        ///// <summary>
        ///// 本次批次数量
        ///// </summary>
        //[Description("本次批次数量")]
        //public decimal? BatchQty { get; set; }
        /// <summary>
        /// 销售订单号
        /// </summary>
        public string SaleNo { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        public decimal? SaleLineNo { get; set; }

        /// <summary>
        /// 采购组织
        /// </summary>
        public string PurchaseORG { get; set; }

        /// <summary>
        /// 采购组
        /// </summary>
        public string PurchaseGroup { get; set; }

        ///// <summary>
        ///// 生产订单号
        ///// </summary>
        //public string AUFNR { get; set; }
        /// <summary>
        /// 批次
        /// </summary>
        public string BatchNum { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 刷字
        /// </summary>
        public string BrushingWords { get; set; }

        /// <summary>
        /// 可制作送货计划数量
        /// </summary>
        public decimal? NoUseQty { get; set; }
    }
}