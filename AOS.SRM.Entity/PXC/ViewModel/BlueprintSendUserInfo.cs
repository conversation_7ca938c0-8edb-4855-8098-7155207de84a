using System;
using System.ComponentModel;

namespace AOS.SRM.Entity.PXC.ViewModel
{
    /// <summary>
    /// 图纸发放用户信息视图模型
    /// </summary>
    public class BlueprintSendUserInfo
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Description("用户ID")]
        public string UserID { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        [Description("用户姓名")]
        public string UserName { get; set; }

        /// <summary>
        /// 登录账号
        /// </summary>
        [Description("登录账号")]
        public string LoginAccount { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [Description("电子邮箱")]
        public string Email { get; set; }

        /// <summary>
        /// 移动手机
        /// </summary>
        [Description("移动手机")]
        public string Mobile { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }

        /// <summary>
        /// 接收记录ID
        /// </summary>
        [Description("接收记录ID")]
        public string AcceptId { get; set; }

        /// <summary>
        /// 接收状态（1-待确认，2-已下载，3-已确认）
        /// </summary>
        [Description("接收状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 接收状态描述
        /// </summary>
        [Description("接收状态描述")]
        public string StatusDesc { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        [Description("采购订单号")]
        public string PurchaseOrder { get; set; }

        /// <summary>
        /// 采购订单行
        /// </summary>
        [Description("采购订单行")]
        public decimal? PurchaseOrderLine { get; set; }

        /// <summary>
        /// 采购订单号和行号
        /// </summary>
        [Description("采购订单号和行号")]
        public string PurchaseOrderAndLine { get; set; }

        /// <summary>
        /// 发放类型（1-自动，2-手动）
        /// </summary>
        [Description("发放类型")]
        public int Type { get; set; }

        /// <summary>
        /// 发放类型描述
        /// </summary>
        [Description("发放类型描述")]
        public string TypeDesc { get; set; }

        /// <summary>
        /// 发放记录ID
        /// </summary>
        [Description("发放记录ID")]
        public string SendRecordId { get; set; }

        /// <summary>
        /// 发放时间
        /// </summary>
        [Description("发放时间")]
        public DateTime? SendTime { get; set; }

        /// <summary>
        /// 发放人
        /// </summary>
        [Description("发放人")]
        public string SendUser { get; set; }

        /// <summary>
        /// 接收时间
        /// </summary>
        [Description("接收时间")]
        public DateTime? AcceptTime { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Description("最后修改时间")]
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 最后修改人
        /// </summary>
        [Description("最后修改人")]
        public string ModifyUser { get; set; }

        /// <summary>
        /// 发送状态（1-已发送，0-未发送）
        /// </summary>
        [Description("发送状态")]
        public int SendStatus { get; set; }
    }
} 