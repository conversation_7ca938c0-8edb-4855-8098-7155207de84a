using System;
using System.ComponentModel;

namespace AOS.SRM.Entity.PXC.ViewModel
{
    /// <summary>
    /// 图纸发放记录视图模型（包含图纸和用户信息）
    /// </summary>
    public class P_Blueprint_Send_Record_View
    {
        /// <summary>
        /// 发放记录ID
        /// </summary>
        [Description("发放记录ID")]
        public string Id { get; set; }

        /// <summary>
        /// 图纸ID
        /// </summary>
        [Description("图纸ID")]
        public string BlueprintId { get; set; }

        /// <summary>
        /// 接收ID
        /// </summary>
        [Description("接收ID")]
        public string AcceptId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Description("备注")]
        public string Remark { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        [Description("创建用户")]
        public string CUser { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Description("创建时间")]
        public DateTime? CTime { get; set; }

        /// <summary>
        /// 修改用户
        /// </summary>
        [Description("修改用户")]
        public string MUser { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        [Description("修改时间")]
        public DateTime? MTime { get; set; }

        // 图纸信息
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 图纸名称
        /// </summary>
        [Description("图纸名称")]
        public string BlueprintName { get; set; }

        /// <summary>
        /// 图纸版本
        /// </summary>
        [Description("图纸版本")]
        public string Version { get; set; }

        /// <summary>
        /// 下载地址
        /// </summary>
        [Description("下载地址")]
        public string DownUrl { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        [Description("生效日期")]
        public DateTime? EffectiveDate { get; set; }

        // 用户信息
        /// <summary>
        /// 用户ID
        /// </summary>
        [Description("用户ID")]
        public string UserID { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        [Description("用户姓名")]
        public string UserName { get; set; }

        /// <summary>
        /// 登录账号
        /// </summary>
        [Description("登录账号")]
        public string LoginAccount { get; set; }

        /// <summary>
        /// 电子邮箱
        /// </summary>
        [Description("电子邮箱")]
        public string Email { get; set; }

        /// <summary>
        /// 移动手机
        /// </summary>
        [Description("移动手机")]
        public string Mobile { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }

        // 接收记录信息
        /// <summary>
        /// 接收状态（1-待确认，2-已下载，3-已确认）
        /// </summary>
        [Description("接收状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 接收状态描述
        /// </summary>
        [Description("接收状态描述")]
        public string StatusDesc { get; set; }

        /// <summary>
        /// 采购订单号
        /// </summary>
        [Description("采购订单号")]
        public string PurchaseOrder { get; set; }

        /// <summary>
        /// 采购订单行
        /// </summary>
        [Description("采购订单行")]
        public decimal? PurchaseOrderLine { get; set; }

        /// <summary>
        /// 采购订单号和行号
        /// </summary>
        [Description("采购订单号和行号")]
        public string PurchaseOrderAndLine { get; set; }

        /// <summary>
        /// 发放类型（1-自动，2-手动）
        /// </summary>
        [Description("发放类型")]
        public int Type { get; set; }

        /// <summary>
        /// 发放类型描述
        /// </summary>
        [Description("发放类型描述")]
        public string TypeDesc { get; set; }

        /// <summary>
        /// 接收时间
        /// </summary>
        [Description("接收时间")]
        public DateTime? AcceptTime { get; set; }
    }
} 