using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 托运单明细
    /// </summary>
    [SugarTable("SD_ConsignmentNoteDetail")]
    public class SD_ConsignmentNoteDetail : BaseEntity
    {
        /// <summary> 
        /// 托运单明细ID
        /// </summary> 
        [Description("托运单明细ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ConsignmentNoteDetailID { get; set; }

        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("wms单号")]
        public string DocNum { get; set; }

        /// <summary> 
        /// wms行号
        /// </summary> 
        [Description("wms行号")]
        public int? Line { get; set; }

        /// <summary> 
        /// 销售订单编号
        /// </summary> 
        [Description("销售订单编号")]
        public string BaseEntry { get; set; }

        /// <summary> 
        /// 销售订单号
        /// </summary> 
        [Description("销售订单号")]
        public string BaseNum { get; set; }

        /// <summary> 
        /// 销售单行号
        /// </summary> 
        [Description("销售单行号")]
        public int? BaseLine { get; set; }

        /// <summary> 
        /// 销售订单类型
        /// </summary> 
        [Description("销售订单类型")]
        public string BaseType { get; set; }

        /// <summary> 
        /// 批次
        /// </summary> 
        [Description("批次")]
        public string BatchNum { get; set; }

        /// <summary> 
        /// 合同号
        /// </summary> 
        [Description("合同号")]
        public string ContractNo { get; set; }

        /// <summary> 
        /// 物料编号
        /// </summary> 
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary> 
        /// 物料名称
        /// </summary> 
        [Description("物料名称")]
        public string ItemName { get; set; }

        /// <summary> 
        /// 物料组编号
        /// </summary> 
        [Description("物料组编号")]
        public string ItmsGrpCode { get; set; }

        /// <summary> 
        /// 物料组名称
        /// </summary> 
        [Description("物料组名称")]
        public string ItmsGrpName { get; set; }

        /// <summary> 
        /// 托运单数量
        /// </summary> 
        [Description("数量")]
        public decimal? Qty { get; set; }

        /// <summary> 
        /// 库存单位
        /// </summary> 
        [Description("库存单位")]
        public string Unit { get; set; }

        /// <summary> 
        /// 发货时间
        /// </summary> 
        [Description("发货时间")]
        public DateTime? DeliverDate { get; set; }

        /// <summary> 
        /// 联系人
        /// </summary> 
        [Description("联系人")]
        public string Contact { get; set; }

        /// <summary> 
        /// 联系方式
        /// </summary> 
        [Description("联系方式")]
        public string Telephone { get; set; }

        /// <summary> 
        /// 里程
        /// </summary> 
        [Description("里程")]
        public int? Mileage { get; set; }

        /// <summary> 
        /// 里程费率
        /// </summary> 
        [Description("费率")]
        public decimal? MileageRate { get; set; }

        /// <summary> 
        /// 重量
        /// </summary> 
        [Description("重量")]
        public decimal? Weight { get; set; }

        /// <summary> 
        /// 重量费率
        /// </summary> 
        [Description("重量费率")]
        public decimal? WeightRate { get; set; }

        /// <summary> 
        /// 重量单位
        /// </summary> 
        [Description("重量单位")]
        public string WeightUnit { get; set; }

        /// <summary> 
        /// 行理论费用
        /// </summary> 
        [Description("行理论费用")]
        public decimal? RowTheoreticalAmount { get; set; }

        /// <summary> 
        /// 行实际费用
        /// </summary> 
        [Description("行实际费用")]
        public decimal? RowActualAmount { get; set; }
    }
}