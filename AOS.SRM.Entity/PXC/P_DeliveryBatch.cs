using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 采购送货批次计划表
    /// </summary>
    [SugarTable("P_DeliveryBatch")]
    public class P_DeliveryBatch : BaseEntity
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Description("主键id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        /// <summary>
        /// 采购订单类型
        /// </summary>
        [Description("采购订单类型")]
        public string OrderType { get; set; }
        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string OrderNo { get; set; }
        /// <summary>
        /// 采购订单分录行
        /// </summary>
        [Description("采购订单分录行")]
        public decimal? OrderLine { get; set; }
        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }
        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }
        /// <summary>
        /// 供应商代码
        /// </summary>
        [Description("供应商代码")]
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string ItemCode { get; set; }
        /// <summary>
        /// 物料名称
        /// </summary>
        [Description("物料名称")]
        public string ItemName { get; set; }
        /// <summary>
        /// 批次号
        /// </summary>
        [Description("批次号")]
        public string BatchNum { get; set; }
        /// <summary>
        /// 项目类别
        /// </summary>
        [Description("项目类别")]
        public string ProjectType { get; set; }
        /// <summary>
        /// 仓库代码
        /// </summary>
        [Description("仓库代码")]
        public string WhsCode { get; set; }
        /// <summary>
        /// 仓库地点
        /// </summary>
        [Description("仓库地点")]
        public string WhsAddress { get; set; }
        /// <summary>
        /// 本次批次送货数量
        /// </summary>
        [Description("本次批次送货数量")]
        public decimal? BatchQty { get; set; }
        /// <summary>
        /// 本次批次送货数量总和
        /// </summary>
        [Description("本次批次送货数量总和")]
        public decimal? CumulativeBatchQuantity { get; set; }
        /// <summary>
        /// 采购订单交期
        /// </summary>
        [Description("采购订单交期")]
        public DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// 送货日期
        /// </summary>
        [Description("送货日期")]
        public DateTime? DeliveryToDate { get; set; }
        /// <summary>
        /// 销售订单编号
        /// </summary>
        [Description("销售订单编号")]
        public string SaleNo { get; set; }
        /// <summary>
        /// 销售订单分录行号
        /// </summary>
        [Description("销售订单分录行号")]
        public decimal? SaleLineNo { get; set; }
        /// <summary>
        /// 采购组织
        /// </summary>
        public string PurchaseORG { get; set; }
        /// <summary>
        /// 采购组
        /// </summary>
        public string PurchaseGroup { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
    }
}
