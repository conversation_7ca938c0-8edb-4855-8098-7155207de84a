namespace AOS.SRM.Entity.PXC.Req
{
    /// <summary>
    /// 根据用户ID查询请求模型
    /// </summary>
    public class GetByUserIdRequest
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 接收状态筛选 0-未处理 1-已确认
        /// </summary>
        public int? AcceptStatus { get; set; }

        /// <summary>
        /// 是否包含已删除的记录
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;
    }
} 