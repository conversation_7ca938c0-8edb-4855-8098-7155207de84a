using System;

namespace AOS.SRM.WebAPI.Areas.PXC.BlueprintAccept.Models
{
    /// <summary>
    /// 蓝图接收查询请求参数
    /// </summary>
    public class BlueprintQueryRequest
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        public string Version { get; set; }
        
        /// <summary>
        /// 工厂代码
        /// </summary>
        public string WERKS { get; set; }

        /// <summary>
        /// CA号
        /// </summary>
        public string AENNR { get; set; }

        /// <summary>
        /// 采购类型 自制，虚拟，采购，委外
        /// </summary>
        public string MaterialType { get; set; }
        
        /// <summary>
        /// 发送状态 0未发送 1已发送
        /// </summary>
        public int? SendStatus { get; set; }

        /// <summary>
        /// 释放状态 0未释放 1已释放
        /// </summary>
        public int? ReleaseStatus { get; set; }
        
        /// <summary>
        /// 释放时间开始
        /// </summary>
        public DateTime? ReleaseTimeStart { get; set; }
        
        /// <summary>
        /// 释放时间结束
        /// </summary>
        public DateTime? ReleaseTimeEnd { get; set; }

        /// <summary>
        /// 创建开始时间
        /// </summary>
        public DateTime? CTimeStart { get; set; }

        /// <summary>
        /// 创建结束时间
        /// </summary>
        public DateTime? CTimeEnd { get; set; }

        /// <summary>
        /// 手动发放类型 0批量处理 1只能单条处理
        /// </summary>
        public int? HandMovementSendType { get; set; }

        /// <summary>
        /// 图纸类型 常规，非标
        /// </summary>
        public string BlueprintType { get; set; }

    }
} 