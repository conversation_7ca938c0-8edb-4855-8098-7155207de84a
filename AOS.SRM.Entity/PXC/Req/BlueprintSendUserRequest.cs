using System;
using System.Collections.Generic;

namespace AOS.SRM.Entity.PXC.Req
{
    /// <summary>
    /// 图纸发放用户关联请求模型
    /// </summary>
    public class BlueprintSendUserRequest
    {
        /// <summary>
        /// 图纸ID
        /// </summary>
        public string BlueprintId { get; set; }

        /// <summary>
        /// 用户ID列表
        /// </summary>
        public List<string> UserIds { get; set; }
    }

    /// <summary>
    /// 图纸发放用户查询请求
    /// </summary>
    public class BlueprintSendUserQueryRequest
    {
        /// <summary>
        /// 图纸ID
        /// </summary>
        public string BlueprintId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 登录账号
        /// </summary>
        public string LoginAccount { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }

        /// <summary>
        /// 发送状态 1-已发送 0-未发送
        /// </summary>
        public int? SendStatus { get; set; }
    }
} 