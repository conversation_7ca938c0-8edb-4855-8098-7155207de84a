using AOS.Core.Http;

namespace AOS.SRM.Entity.PXC.Req
{
    /// <summary>
    /// 图纸发放记录查询请求模型
    /// </summary>
    public class P_Blueprint_Send_RecordQueryRequest
    {
        /// <summary>
        /// 分页参数
        /// </summary>
        public Pagination Page { get; set; }

        /// <summary>
        /// 图纸ID
        /// </summary>
        public string BlueprintId { get; set; }

        /// <summary>
        /// 接收ID
        /// </summary>
        public string AcceptId { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialCode { get; set; }

        /// <summary>
        /// 创建时间开始
        /// </summary>
        public System.DateTime? CreateTimeStart { get; set; }

        /// <summary>
        /// 创建时间结束
        /// </summary>
        public System.DateTime? CreateTimeEnd { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 关键字搜索（备注）
        /// </summary>
        public string Keyword { get; set; }
    }
} 