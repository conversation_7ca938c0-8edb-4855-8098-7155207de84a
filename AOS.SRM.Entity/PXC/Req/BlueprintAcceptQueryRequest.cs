namespace AOS.SRM.WebAPI.Areas.PXC.BlueprintAccept.Models
{
    /// <summary>
    /// 蓝图接收查询请求参数
    /// </summary>
    public class BlueprintAcceptQueryRequest
    {
        /// <summary>
        /// 物料编码
        /// </summary>
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// 类型
        /// </summary>
        public int? Type { get; set; }
        
        /// <summary>
        /// 物料描述
        /// </summary>
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 图纸编号
        /// </summary>
        public string BlueprintCode { get; set; }
        
    }
} 