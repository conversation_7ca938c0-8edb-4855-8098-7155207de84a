using System.ComponentModel;

namespace AOS.SRM.Entity.PXC.Req
{
    /// <summary>
    /// 获取图纸发放用户列表请求参数
    /// </summary>
    public class GetBlueprintSendUsersRequest
    {
        /// <summary>
        /// 图纸ID
        /// </summary>
        [Description("图纸ID")]
        public string BlueprintId { get; set; }

        /// <summary>
        /// 用户姓名（模糊查询）
        /// </summary>
        [Description("用户姓名")]
        public string UserName { get; set; }

        /// <summary>
        /// 登录账号（模糊查询）
        /// </summary>
        [Description("登录账号")]
        public string LoginAccount { get; set; }

        /// <summary>
        /// 供应商编码（模糊查询）
        /// </summary>
        [Description("供应商编码")]
        public string SupplyCode { get; set; }

        /// <summary>
        /// 供应商名称（模糊查询）
        /// </summary>
        [Description("供应商名称")]
        public string SupplyName { get; set; }

        /// <summary>
        /// 接收状态（1-待确认，2-已下载，3-已确认）
        /// </summary>
        [Description("接收状态")]
        public int? Status { get; set; }

        /// <summary>
        /// 发放类型（1-自动，2-手动）
        /// </summary>
        [Description("发放类型")]
        public int? Type { get; set; }

        /// <summary>
        /// 采购订单号（模糊查询）
        /// </summary>
        [Description("采购订单号")]
        public string PurchaseOrder { get; set; }
    }
} 