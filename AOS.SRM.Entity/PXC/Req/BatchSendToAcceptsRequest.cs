using System.Collections.Generic;

namespace AOS.SRM.Entity.PXC.Req
{
    /// <summary>
    /// 批量发放图纸给接收记录请求模型
    /// </summary>
    public class BatchSendToAcceptsRequest
    {
        /// <summary>
        /// 图纸ID
        /// </summary>
        public string BlueprintId { get; set; }

        /// <summary>
        /// 接收ID列表
        /// </summary>
        public List<string> AcceptIds { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
} 