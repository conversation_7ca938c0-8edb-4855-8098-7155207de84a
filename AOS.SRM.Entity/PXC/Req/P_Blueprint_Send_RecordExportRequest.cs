using System;

namespace AOS.SRM.Entity.PXC.Req
{
    /// <summary>
    /// 图纸发放记录导出请求模型
    /// </summary>
    public class P_Blueprint_Send_RecordExportRequest
    {
        /// <summary>
        /// 图纸ID
        /// </summary>
        public string BlueprintId { get; set; }

        /// <summary>
        /// 接收ID
        /// </summary>
        public string AcceptId { get; set; }

        /// <summary>
        /// 创建时间开始
        /// </summary>
        public DateTime? CreateTimeStart { get; set; }

        /// <summary>
        /// 创建时间结束
        /// </summary>
        public DateTime? CreateTimeEnd { get; set; }

        /// <summary>
        /// 创建用户
        /// </summary>
        public string CreateUser { get; set; }

        /// <summary>
        /// 关键字搜索（备注）
        /// </summary>
        public string Keyword { get; set; }
    }
} 