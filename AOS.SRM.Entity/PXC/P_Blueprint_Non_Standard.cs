using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PLM
{
    /// <summary>
    /// 非标图纸
    /// </summary>
    [SugarTable("P_Blueprint_Non_Standard")]
    public class P_Blueprint_Non_Standard : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Description("主键")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料版本
        /// </summary>
        [Description("物料版本")]
        public string MaterialVersion { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 组件单位
        /// </summary>
        [Description("组件单位")]
        public string Unit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Description("数量")]
        public long? Quantity { get; set; }

        /// <summary>
        /// 外部物料组
        /// </summary>
        [Description("外部物料组")]
        public string OutMaterialGroup { get; set; }

        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// SAP订单号
        /// </summary>
        [Description("SAP订单号")]
        public string SapNo { get; set; }

        /// <summary>
        /// sap订单行
        /// </summary>
        [Description("sap订单行")]
        public int? SapLine { get; set; }

        /// <summary>
        /// 文件状态 0未上传 1已上传
        /// </summary>
        [Description("文件状态")]
        public int? FileStatus { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        [Description("文件路径")]
        public string FilePath { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        [Description("文件名称")]
        public string FileName { get; set; }
    }
}
