using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 报检单明细
    /// </summary>
    [SugarTable("P_InspectionDetail")]
    public class P_InspectionDetail : BaseEntity
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键id")]
        public string Id { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// 报检单号
        /// </summary>
        [Description("报检单号")]
        public string InspectionNo { get; set; }

        /// <summary>
        /// 报检单行号
        /// </summary>
        [Description("报检单行号")]
        public int? InspectionLine { get; set; }

        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 采购订单行号
        /// </summary>
        [Description("采购订单行号")]
        public decimal? OrderLine { get; set; }

        /// <summary>
        /// 订单类型
        /// </summary>
        [Description("订单类型")]
        public string OrderType { get; set; }

        /// <summary>
        /// 采购订单分录行项目类别  L：代表委外加工；空为标准
        /// </summary>
        [Description("采购订单分录行项目类别")]
        public string ProjectType { get; set; }

        /// <summary>
        /// 供应商编号
        /// </summary>
        [Description("供应商编号")]
        public string SupplyCode { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        [Description("物料编号")]
        public string ItemCode { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        [Description("批次号")]
        public string BatchNum { get; set; }

        /// <summary>
        /// 仓库地点
        /// </summary>
        [Description("仓库地点")]
        public string WhsAddress { get; set; }

        /// <summary>
        /// 仓库编码
        /// </summary>
        [Description("仓库编码")]
        public string WhsCode { get; set; }

        /// <summary>
        /// 报检数量
        /// </summary>
        [Description("报检数量")]
        public decimal? InspectionQty { get; set; }

        /// <summary>
        /// 采购订单交期
        /// </summary>
        [Description("采购订单交期")]
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// 送货日期
        /// </summary>
        [Description("送货日期")]
        public DateTime? DeliveryToDate { get; set; }

        /// <summary>
        /// 销售订单号
        /// </summary>
        [Description("销售订单号")]
        public string SaleNo { get; set; }

        /// <summary>
        /// 销售订单行号
        /// </summary>
        [Description("销售订单行号")]
        public decimal? SaleLineNo { get; set; }

        /// <summary>
        /// 状态    1:已发货  2:检验完成  3:完成入库
        /// </summary>
        [Description("状态")]
        public string Status { get; set; }

        /// <summary>
        /// 质检结果
        /// </summary>
        [Description("质检结果")]
        public int? QualityRasult { get; set; }

        /// <summary>
        /// 检验合格数量
        /// </summary>
        [Description("检验合格数量")]
        public decimal? QualifiedQty { get; set; }

        /// <summary>
        /// 检验不合格数量
        /// </summary>
        [Description("检验不合格数量")]
        public decimal? UnQualifiedQty { get; set; }

        /// <summary>
        /// 检验员
        /// </summary>
        [Description("检验员")]
        public string QualityUser { get; set; }

        /// <summary>
        /// 检验时间
        /// </summary>
        [Description("检验时间")]
        public DateTime? QualityDate { get; set; }

        /// <summary>
        /// 品质检验备注说明
        /// </summary>
        [Description("品质检验备注说明")]
        public string QualityRemark { get; set; }

        /// <summary>
        /// 保管数量
        /// </summary>
        [Description("保管数量")]
        public decimal? StorageQty { get; set; }

        /// <summary>
        /// 保管时间
        /// </summary>
        [Description("保管时间")]
        public DateTime? StorageDate { get; set; }

        /// <summary>
        /// 保管人
        /// </summary>
        [Description("保管人")]
        public string StorageUser { get; set; }

        /// <summary>
        /// 保管备注
        /// </summary>
        [Description("保管备注")]
        public string StorageRemark { get; set; }

        /// <summary>
        /// 采购组织
        /// </summary>
        public string PurchaseORG { get; set; }

        /// <summary>
        /// 采购组
        /// </summary>
        public string PurchaseGroup { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }

        /// <summary>
        /// 送货批次Id
        /// </summary>
        public string DeliveryBatchId { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
    }
}