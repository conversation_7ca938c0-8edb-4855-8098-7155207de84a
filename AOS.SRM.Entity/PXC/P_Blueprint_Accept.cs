using System;
using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PLM
{
    /// <summary>
    /// 图纸信息记录表
    /// </summary>
    [SugarTable("P_Blueprint_Accept")]
    public class P_Blueprint_Accept : BaseEntity
    {
        
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }
        
        /// <summary>
        /// 父ID
        /// </summary>
        public string Pid { get; set; }
        
        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 物料描述
        /// </summary>
        [Description("物料描述")]
        public string MaterialDesc { get; set; }

        /// <summary>
        /// 图纸编号
        /// </summary>
        [Description("图纸编号")]
        public string BlueprintCode { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Description("名称")]
        public string Name { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Description("版本")]
        public string Version { get; set; }

        /// <summary>
        /// 下载地址
        /// </summary>
        [Description("下载地址")]
        public string DownUrl { get; set; }

        /// <summary>
        /// 文件名称
        /// </summary>
        [Description("文件名称")]
        [SugarColumn(IsIgnore = true)]
        public string FileName { get; set; }

        /// <summary>
        /// 生效日期
        /// </summary>
        [Description("生效日期")]
        public DateTime? EffectiveDate { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        [Description("状态")]
        public int? Status { get; set; }
        
        /// <summary>
        /// 采购订单号
        /// </summary>
        [Description("采购订单号")]
        public string PurchaseOrder { get; set; }
        
        /// <summary>
        /// 采购订单行
        /// </summary>
        [Description("采购订单行")]
        public decimal? PurchaseOrderLine { get; set; }
        
        /// <summary>
        /// 采购订单行
        /// </summary>
        [Description("采购订单行")]
        public string PurchaseOrderAndLine { get; set; }
        
        /// <summary>
        /// 类型 1自动 2手动
        /// </summary>
        [Description("类型")]
        public int Type { get; set; }
        
    }
}