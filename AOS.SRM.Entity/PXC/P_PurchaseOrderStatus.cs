using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 采购订单状态信息表
    /// </summary>
    [SugarTable("P_PurchaseOrderStatus")]
    public class P_PurchaseOrderStatus : BaseEntity
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Description("主键id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// 订单号
        /// </summary>
        [Description("订单号")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public decimal? OrderLine { get; set; }

        /// <summary>
        /// 0:等待接收1:已接收2:已制作送货计划3:未清4:已清
        /// </summary>
        [Description(" 0:等待接收 1:已接收 2:已制作送货计划 3:未清 4:已清")]
        public string Status { get; set; }
    }
}