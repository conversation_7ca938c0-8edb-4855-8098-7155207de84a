using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.PXC.Dto
{
    /// <summary>
    /// 
    /// </summary>
    public class P_InspectionDetail_Views
    {
        /// <summary>
        /// 是否启用自动过账
        /// </summary>
        // public bool IsPostAccount { get; set; }
        //主键id
        public string Id { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }
        /// <summary>
        /// 报检日期
        /// </summary>
        public DateTime? CTime { get; set; } = DateTime.Now;
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 创建人
        /// </summary>
        public string CUser { get; set; }
        /// <summary>
        /// 报检单号
        /// </summary>
        public string InspectionNo { get; set; }
        /// <summary>
        /// 采购收货
        /// </summary>
        public List<P_InspectionDetail> P_InspectionDetail { get; set; }
    }
}
