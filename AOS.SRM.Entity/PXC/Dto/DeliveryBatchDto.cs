using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AOS.SRM.Entity.PXC.Dto
{
    /// <summary>
    /// 
    /// </summary>
    public class DeliveryBatchDto
    {
        /// <summary>
        /// 采购订单编号
        /// </summary>
        [Description("采购订单编号")]
        public string EBELN { get; set; }
        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public decimal? EBELP { get; set; }
        /// <summary>
        /// 工厂
        /// </summary>
        [Description("工厂")]
        public string WERKS { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public class Compare : IEqualityComparer<DeliveryBatchDto>
        {
            /// <summary>
            /// 
            /// </summary>
            /// <param name="x"></param>
            /// <param name="y"></param>
            /// <returns></returns>
            public bool Equals(DeliveryBatchDto x, DeliveryBatchDto y)
            {
                return x.EBELN == y.EBELN && x.EBELP == y.EBELP;
            }
            /// <summary>
            /// 
            /// </summary>
            /// <param name="obj"></param>
            /// <returns></returns>
            public int GetHashCode(DeliveryBatchDto obj)
            {
                return obj.EBELN.GetHashCode();
            }
        }
    }
}
