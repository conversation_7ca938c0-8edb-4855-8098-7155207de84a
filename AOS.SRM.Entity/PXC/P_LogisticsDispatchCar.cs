using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 物流订单派车信息
    /// </summary>
    [SugarTable("P_LogisticsDispatchCar")]
    public class P_LogisticsDispatchCar : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 物流状态订单Id
        /// </summary>
        public string LogisticsOrderStatusId { get; set; }

        /// <summary>
        /// 物流单号
        /// </summary>
        public string LogisticsNo { get; set; }

        /// <summary>
        /// 车牌号
        /// </summary>
        public string CarNum { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 司机
        /// </summary>
        public string Driver { get; set; }
    }
}