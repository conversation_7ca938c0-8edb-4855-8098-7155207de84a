using System;
using System.ComponentModel;
using SqlSugar;


namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 托运
    /// </summary>
    [SugarTable("SD_ConsignmentNote")]
    public class SD_ConsignmentNote : BaseEntity
    {
        /// <summary> 
        /// 托运单ID
        /// </summary> 
        [Description("托运单ID")]
        [SugarColumn(IsPrimaryKey = true)]
        public string ConsignmentNoteID { get; set; }
        /// <summary> 
        /// wms单号
        /// </summary> 
        [Description("wms单号")]
        public string DocNum { get; set; }
        /// <summary> 
        /// 发货日期
        /// </summary> 
        [Description("发货日期")]
        public DateTime? DeliveryDate { get; set; }
        /// <summary> 
        /// 发运计划
        /// </summary> 
        [Description("发运计划")]
        public string ShippingPlanNum { get; set; }
        /// <summary> 
        /// 状态
        /// </summary> 
        [Description("状态")]
        public int? Status { get; set; }
        /// <summary> 
        /// 客户ID
        /// </summary> 
        [Description("客户ID")]
        public string CustomerID { get; set; }
        /// <summary> 
        /// 客户编号
        /// </summary> 
        [Description("客户编号")]
        public string CustomerCode { get; set; }
        /// <summary> 
        /// 客户名称
        /// </summary> 
        [Description("客户名称")]
        public string CustomerName { get; set; }
        /// <summary> 
        /// 客户地址
        /// </summary> 
        [Description("客户地址")]
        public string CustomerAdd { get; set; }
        /// <summary> 
        /// 结算地址
        /// </summary> 
        [Description("结算地址")]
        public string CustomerRegion { get; set; }
        /// <summary> 
        /// 物流供应商编号
        /// </summary> 
        [Description("物流供应商编号")]
        public string LogisticsSupplierCode { get; set; }
        /// <summary> 
        /// 物流供应商名称
        /// </summary> 
        [Description("物流供应商名称")]
        public string LogisticsSupplierName { get; set; }
        /// <summary>
        /// 托运部门
        /// </summary>
        [Description("托运部门")]
        public string ShippingDepar { get; set; }
        /// <summary> 
        /// 运输方式
        /// </summary> 
        [Description("运输方式")]
        public string ShippingType { get; set; }
        /// <summary> 
        /// 托运人
        /// </summary> 
        [Description("托运人")]
        public string Shipper { get; set; }
        /// <summary> 
        /// 车牌
        /// </summary> 
        [Description("车牌")]
        public string CarNum { get; set; }
        /// <summary> 
        /// 特殊费用
        /// </summary> 
        [Description("特殊费用")]
        public decimal? SpecialExpenses { get; set; }
        /// <summary> 
        /// 总理论费用
        /// </summary> 
        [Description("总理论费用")]
        public decimal? TotalTheoreticalAmount { get; set; }
        /// <summary> 
        /// 总实际费用
        /// </summary> 
        [Description("总实际费用")]
        public decimal? TotalActualAmount { get; set; }
        /// <summary> 
        /// 总重量
        /// </summary> 
        [Description("总重量")]
        public decimal? TotalWeight { get; set; }
        /// <summary> 
        /// 类型, 1:自动生成，2.手动生成
        /// </summary> 
        [Description("类型")]
        public int? Type { get; set; }
    }
}


