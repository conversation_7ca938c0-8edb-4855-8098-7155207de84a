using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.Basic.ViewModel
{
    /// <summary>
    /// 物流订单状态信息表
    /// </summary>
    public class P_PurchaseServerOrderStatus : BaseEntity
    {
        /// <summary>
        /// 主键id
        /// </summary>
        [Description("主键id")]
        [SugarColumn(IsPrimaryKey = true)]
        public string Id { get; set; }

        /// <summary>
        /// 附件上传唯一标识
        /// </summary>
        [Description("附件上传唯一标识")]
        public string ParentId { get; set; }

        /// <summary>
        /// 公司代码
        /// </summary>
        [Description("公司代码")]
        public string CompanyCode { get; set; }

        /// <summary>
        /// 工厂代码
        /// </summary>
        [Description("工厂代码")]
        public string FactoryCode { get; set; }

        /// <summary>
        /// 订
        /// 号
        /// </summary>
        [Description("订单号")]
        public string OrderNo { get; set; }

        /// <summary>
        /// 订单行号
        /// </summary>
        [Description("订单行号")]
        public decimal? OrderLine { get; set; }

        /// <summary>
        /// 0:等待接收1:已接收2:已制作送货计划3:未清4:已清
        /// </summary>
        [Description(" 0:未接收 1:已接收 2：已派车 3:已装车运输 4:已完成")]
        public string Status { get; set; }

        /// <summary>
        /// 是否被物流对账单使用
        /// </summary>
        public bool? IsUse { get; set; }

        /// <summary>
        /// 是否被物流开票申请使用
        /// </summary>
        public bool? IsUse2 { get; set; }

        /// <summary>
        /// 附件Id
        /// </summary>
        public string AttachId { get; set; }

        /// <summary>
        /// 附件名称
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        public string SupplyCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplyName { get; set; }
    }
}