using System.ComponentModel;
using SqlSugar;

namespace AOS.SRM.Entity.PXC
{
    /// <summary>
    /// 图纸发放记录
    /// </summary>
    [SugarTable("P_Blueprint_Send_Record")]
    public class P_Blueprint_Send_Record : BaseEntity
    {
        /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true)]
        [Description("主键")]
        public string Id { get; set; }

        /// <summary>
        /// 图纸ID
        /// </summary>
        [Description("图纸ID")]
        public string BlueprintId { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [Description("物料编码")]
        public string MaterialCode { get; set; }

        /// <summary>
        /// 接收ID
        /// </summary>
        [Description("接收ID")]
        public string AcceptId { get; set; }
    }
} 